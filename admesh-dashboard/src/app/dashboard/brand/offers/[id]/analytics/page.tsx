"use client";

import { useEffect, useState, use<PERSON><PERSON>back } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { format } from "date-fns";
import { centsToDollars, formatCurrency as formatCurrencyUtil } from "@/lib/utils";
import { handleTestConversion as handleTestConversionUtil } from "@/lib/conversion-utils";
import { useAuth } from "@/hooks/use-auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
// import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  ArrowLeft,
  BarChart,
  MousePointerClick,
  ArrowUpRight,
  DollarSign,
  Clock,
  Edit,
  AlertTriangle,
  Loader2,
  Zap,
  Trash2,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";

interface Conversion {
  id: string;
  timestamp: string | { seconds: number; nanoseconds: number } | Date;
  value: number;
  currency: string;
  source: string;
  agent_id: string;
  user_id: string;
  status: string;
  is_test?: boolean;
}

interface OfferStats {
  offer_id: string;
  title: string;
  description?: string;
  url?: string;
  offer_total_budget_allocated: number;
  offer_budget_left: number;
  calculated_budget_left?: number; // Added for explicit budget calculation
  total_spent: {
    production: number;
    test: number;
    all: number;
  };
  // Added for debugging - the original stored values that might be negative
  stored_total_spent?: {
    production: number;
    test: number;
    all: number;
  };
  click_count: {
    total: number;
    test: number;
    production: number;
  } | number; // Support both new structure and legacy number format
  view_count: number;
  offer_views?: {
    total: number;
    test: number;
    production: number;
  };
  conversion_count: {
    total: number;
    test: number;
    production: number;
  };
  ctr: number;
  payout: {
    amount: number;
    currency: string;
    model: string;
  };
  conversions: Conversion[];
  active: boolean; // Backward compatibility
  offer_status: "active" | "inactive" | "cancelled"; // Primary status field
  status: string; // Backward compatibility
  created_at: string | { seconds: number; nanoseconds: number } | Date;
  updated_at: string | { seconds: number; nanoseconds: number } | Date;
  valid_until?: string | { seconds: number; nanoseconds: number } | Date;
  brand_id?: string;
  product_id?: string;
  product_name?: string;
  goal?: string;
  reward_note?: string;
  suggestion_reason?: string;
  categories?: string[];
  keywords?: string[];
  trust_score?: number;

  // Budget tracking fields
  offer_total_budget_spent?: number;
  offer_total_promo_spent?: number;
  offer_total_promo_available?: number;
  offer_intial_promo_balance?: number;
  promo_conversions_left?: number;
  promo_applied?: boolean;


  tracking?: {
    method?: string;
    webhook_url?: string;
    notes?: string;
    redirect_url?: string;
    target_urls?: string[];
    updated_at?: string | { seconds: number; nanoseconds: number } | Date;
  };

  // Legacy field for backward compatibility
  integration?: {
    method?: string;
    webhook_url?: string;
    notes?: string;
    redirect_url?: string;
    updated_at?: string | { seconds: number; nanoseconds: number } | Date;
  };
}

export default function OfferAnalyticsPage() {
  const { id } = useParams() as { id: string };
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<OfferStats | null>(null);
  const [timeRange, setTimeRange] = useState("all");
  const [conversionType, setConversionType] = useState("all");
   const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isTestingConversion, setIsTestingConversion] = useState(false);
  const [deleteTestClicks, setDeleteTestClicks] = useState(true);
   const [deletingSingleConversion, setDeletingSingleConversion] = useState<string | null>(null);
  const [confirmDeleteConversionId, setConfirmDeleteConversionId] = useState<string | null>(null);

  const fetchOfferAnalytics = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/offers/stats/${id}/detailed?time_range=${timeRange}&conversion_type=${conversionType}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API Error (${response.status}):`, errorText);
        throw new Error(`Failed to fetch offer analytics: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Debug log to see what values we're getting from the API
      console.log('API Response:', {
        offer_total_budget_allocated: data.offer_total_budget_allocated,
        offer_budget_left: data.offer_budget_left,
        calculated_budget_left: data.calculated_budget_left,
        production_spent: data.total_spent?.production,
        test_spent: data.total_spent?.test,
        all_spent: data.total_spent?.all,
        stored_production_spent: data.stored_total_spent?.production,
        stored_test_spent: data.stored_total_spent?.test,
        stored_all_spent: data.stored_total_spent?.all,
        frontend_calculated_remaining: Math.max(0, (data.offer_total_budget_allocated || 0) - (data.total_spent?.production || 0)),
        // Budget tracking fields
        offer_total_budget_spent: data.offer_total_budget_spent,
        offer_total_promo_spent: data.offer_total_promo_spent,
        offer_total_promo_available: data.offer_total_promo_available,
        offer_intial_promo_balance: data.offer_intial_promo_balance,
        promo_applied: data.promo_applied,
        promo_conversions_left: data.promo_conversions_left,
        keywords: data.keywords,
        categories: data.categories
      });

      // Verify that our budget calculations match
      if (data.offer_budget_left !== data.calculated_budget_left) {
        console.warn('Budget left mismatch between API values:', {
          offer_budget_left: data.offer_budget_left,
          calculated_budget_left: data.calculated_budget_left
        });
      }

      // Check if we have negative values in stored_total_spent
      if (data.stored_total_spent) {
        const hasNegativeValues =
          data.stored_total_spent.production < 0 ||
          data.stored_total_spent.test < 0 ||
          data.stored_total_spent.all < 0;

        if (hasNegativeValues) {
          console.warn('Negative values detected in stored_total_spent:', data.stored_total_spent);
        }
      }

      setStats(data);
    } catch (error) {
      console.error("Error fetching offer analytics:", error);
      toast.error("Failed to load offer analytics");
    } finally {
      setLoading(false);
    }
  }, [user, id, timeRange, conversionType]);

  useEffect(() => {
    if (user) {
      fetchOfferAnalytics();
    }
  }, [user, id, timeRange, conversionType, fetchOfferAnalytics]);

  const handleBack = () => {
    router.push("/dashboard/brand/offers");
  };

  const handleEdit = () => {
    router.push(`/dashboard/brand/offers/${id}/edit`);
  };


 const handleDeleteTestConversions = async () => {
    if (!user || !stats) return;

    try {
      setIsDeleting(true);
      const token = await user.getIdToken();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/conversion/delete-tests`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ offer_id: id, delete_clicks: deleteTestClicks })
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete test data: ${errorText}`);
      }

      const data = await response.json();
      toast.success(data.message || 'Test data deleted successfully');

      // Refresh the data
      fetchOfferAnalytics();
    } catch (error) {
      console.error('Error deleting test data:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete test data');
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };
  const handleDeleteSingleConversion = async (conversionId: string) => {
    if (!user || !stats) return;

    try {
      setDeletingSingleConversion(conversionId);
      const token = await user.getIdToken();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/conversion/delete-single-test`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ conversion_id: conversionId, offer_id: id })
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete test conversion: ${errorText}`);
      }

      const data = await response.json();
      toast.success(data.message || 'Test conversion deleted successfully');

      // Refresh the data
      fetchOfferAnalytics();
    } catch (error) {
      console.error('Error deleting test conversion:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete test conversion');
    } finally {
      setDeletingSingleConversion(null);
    }
  };

  const handleTestConversion = async () => {
    if (!user || !stats) return;

    try {
      // Use the offer's own redirect URL from the tracking settings
      // Fall back to a test page only if no redirect URL is configured
      const redirectUrl = stats.tracking?.redirect_url || `${window.location.origin}/test-conversion`;

      // Use the utility function to handle test conversion
      const success = await handleTestConversionUtil({
        offerId: id,
        redirectUrl,
        user,
        productId: stats.product_id || '',
        setIsTestingConversion
      });

      if (success) {
        // Refresh the data to show the new click
        fetchOfferAnalytics();
      }
    } catch (error) {
      console.error('Error in test conversion flow:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to start test conversion flow');
    }
  };

  const formatDate = (timestamp: string | { seconds: number; nanoseconds: number } | Date | undefined | null) => {
    if (!timestamp) return "—";

    // Handle Firestore timestamp objects
    if (typeof timestamp === 'object' && 'seconds' in timestamp) {
      return format(new Date(timestamp.seconds * 1000), "MMM dd, yyyy HH:mm");
    }

    // Handle Date objects
    if (timestamp instanceof Date) {
      return format(timestamp, "MMM dd, yyyy HH:mm");
    }

    // Handle ISO strings
    try {
      return format(new Date(timestamp as string), "MMM dd, yyyy HH:mm");
    } catch {
      return "Invalid date";
    }
  };

  const formatCurrency = (amount: number | undefined | null, currency: string = "USD") => {
    // Handle undefined, null, or NaN values
    if (amount === undefined || amount === null || isNaN(amount)) {
      amount = 0;
    }

    // Convert from cents to dollars first, then format
    const dollars = centsToDollars(amount);
    return formatCurrencyUtil(dollars, currency);
  };

  const getBudgetUsagePercentage = () => {
    if (!stats) return 0;
    if (stats.offer_total_budget_allocated <= 0) return 0;

    // Get production spent amount, defaulting to 0 if undefined or NaN
    // Both values are in cents, so no need to convert
    const productionSpent = stats.total_spent?.production || 0;

    // Calculate percentage, ensuring we don't divide by zero
    const percentage = stats.offer_total_budget_allocated > 0 ? (productionSpent / stats.offer_total_budget_allocated) * 100 : 0;

    // Handle NaN and limit to 100%
    return Math.min(100, Math.round(isNaN(percentage) ? 0 : percentage));
  };

  if (loading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading offer analytics...</p>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button variant="outline" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Offers
          </Button>
        </div>
        <Card>
          <CardContent className="py-10">
            <div className="text-center">
              <AlertTriangle className="h-10 w-10 text-amber-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Analytics Not Available</h3>
              <p className="text-muted-foreground mb-6">
                We couldn&apos;t load analytics for this offer. It may have been deleted or you don&apos;t have permission to view it.
              </p>
              <Button onClick={handleBack}>Return to Offers</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header with navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center">
          <Button variant="outline" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex items-center">
            <div>
              <div className="flex items-center gap-2">
                <h2 className="text-2xl font-bold tracking-tight">{stats.title}</h2>
                <Badge
                  variant={stats.offer_status === "active" ? "default" : "outline"}
                  className={
                    stats.offer_status === "active"
                      ? "bg-green-100 text-green-700 border-green-200"
                      : stats.offer_status === "cancelled"
                      ? "bg-red-100 text-red-700 border-red-200"
                      : "bg-gray-100 text-gray-600 border-gray-200"
                  }
                >
                  {stats.offer_status || stats.status || (stats.active ? "active" : "inactive")}
                </Badge>
              </div>
              <p className="text-muted-foreground text-sm">
                Offer Analytics and Performance Metrics
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
           <Button variant="outline" onClick={handleEdit} size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit Offer
          </Button>
          <Button
            variant="outline"
            onClick={handleTestConversion}
            size="sm"
            className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
            disabled={isTestingConversion}
          >

            {isTestingConversion ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Test Conversion
              </>
            )}
          </Button>




        </div>
      </div>


      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Filter by:</span>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Time Range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last 30 Days</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          <Select value={conversionType} onValueChange={setConversionType}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Conversion Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Conversions</SelectItem>
              <SelectItem value="production">Production Only</SelectItem>
              <SelectItem value="test">Test Only</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="text-sm text-muted-foreground">
          <Clock className="h-4 w-4 inline mr-1" />
          Last updated: {formatDate(stats.updated_at) || "Never"}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-white">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Views
                </p>
                <h3 className="text-2xl font-bold">
                  {stats.offer_views?.production || 0}
                </h3>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                    {stats.offer_views?.production || 0} Production
                  </Badge>
                  {(stats.offer_views?.test || 0) > 0 && (
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      +{stats.offer_views?.test || 0} Test
                    </Badge>
                  )}
                </div>
              </div>
              <div className="bg-indigo-100 p-2 rounded-full">
                <BarChart className="text-indigo-600 h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Total Clicks
                </p>
                <h3 className="text-2xl font-bold">
                  {typeof stats.click_count === 'object' ? stats.click_count.production : stats.click_count}
                </h3>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                    {typeof stats.click_count === 'object' ? stats.click_count.production : stats.click_count} Production
                  </Badge>
                  {typeof stats.click_count === 'object' && stats.click_count.test > 0 && (
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      +{stats.click_count.test} Test
                    </Badge>
                  )}
                </div>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <MousePointerClick className="text-blue-600 h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Conversions
                </p>
                <h3 className="text-2xl font-bold">{stats.conversion_count.production}</h3>
                <div className="flex gap-2 mt-1">
                  <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                    {stats.conversion_count.production} Production
                  </Badge>
                  {stats.conversion_count.test > 0 && (
                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                      +{stats.conversion_count.test} Test
                    </Badge>
                  )}
                </div>
              </div>
              <div className="bg-green-100 p-2 rounded-full">
                <ArrowUpRight className="text-green-600 h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Budget
                </p>
                <h3 className="text-2xl font-bold">
                  {/* Budget is stored in cents in the database, formatCurrency converts to dollars */}
                  {formatCurrency(stats.offer_total_budget_allocated || 0, stats.payout?.currency)}
                </h3>
                <div className="mt-2">
                  <Progress value={getBudgetUsagePercentage()} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">
                    {getBudgetUsagePercentage()}% used
                  </p>
                </div>
              </div>
              <div className="bg-purple-100 p-2 rounded-full">
                <DollarSign className="text-purple-600 h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Spent / Remaining
                </p>
                <h3 className="text-2xl font-bold">
                  {/* All monetary values are stored in cents in the database, formatCurrency converts to dollars */}
                  {formatCurrency(stats.offer_total_budget_spent !== undefined ? stats.offer_total_budget_spent : stats.total_spent?.production || 0, stats.payout?.currency)}
                </h3>
                <p className="text-xs text-muted-foreground mt-1">
                  {formatCurrency(
                    // Use offer_budget_left if available, otherwise calculate it
                    stats.offer_budget_left !== undefined
                      ? stats.offer_budget_left
                      : stats.calculated_budget_left !== undefined
                        ? stats.calculated_budget_left
                        : Math.max(0, (stats.offer_total_budget_allocated || 0) - (stats.total_spent?.production || 0)),
                    stats.payout?.currency
                  )} remaining
                </p>
                {stats.total_spent?.test > 0 && (
                  <p className="text-xs text-blue-500 mt-1">
                    + {formatCurrency(stats.total_spent.test, stats.payout?.currency)} in test conversions
                    <span className="ml-1 text-xs italic">(not counted in budget)</span>
                  </p>
                )}
                {stats.stored_total_spent && stats.stored_total_spent.test < 0 && (
                  <p className="text-xs text-red-500 mt-1">
                    Warning: Negative test spending detected ({formatCurrency(stats.stored_total_spent.test, stats.payout?.currency)})
                  </p>
                )}
              </div>
              <div className="bg-amber-100 p-2 rounded-full">
                <BarChart className="text-amber-600 h-5 w-5" />
              </div>
            </div>
          </CardContent>
        </Card>


      </div>
  {/* Conversion Details */}
      <Card className="shadow-sm border">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CardTitle className="text-base font-semibold text-gray-900">
                Conversion Details
                <span className="ml-2 text-xs font-normal text-muted-foreground">
                  (Test conversions are highlighted in blue)
                </span>
              </CardTitle>
              {(stats.conversion_count.test > 0 || (typeof stats.click_count === 'object' && stats.click_count.test > 0)) && (
                <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 border-red-200 hover:bg-red-50 ml-4"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete Test Data
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Test Data?</AlertDialogTitle>
                      <div className="text-muted-foreground text-sm">
                        {stats.conversion_count.test > 0 && (
                          <div>This will permanently delete all {stats.conversion_count.test} test conversions for this offer.</div>
                        )}
                        {typeof stats.click_count === 'object' && stats.click_count.test > 0 && (
                          <div className="mt-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="delete-clicks"
                                checked={deleteTestClicks}
                                onCheckedChange={(checked: boolean | 'indeterminate') => setDeleteTestClicks(checked === true)}
                              />
                              <label htmlFor="delete-clicks" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                Also delete {stats.click_count.test} test clicks
                              </label>
                            </div>
                          </div>
                        )}
                        <div className="mt-2">This action cannot be undone.</div>
                      </div>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteTestConversions}
                        className="bg-red-600 hover:bg-red-700 text-white"
                        disabled={isDeleting}
                      >
                        {isDeleting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Deleting...
                          </>
                        ) : (
                          <>Delete</>
                        )}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
            {/* <div className="text-sm text-muted-foreground">
              CTR: <span className="font-semibold">{stats.ctr}%</span>
            </div> */}
          </div>
          <CardDescription>
            {conversionType === "all"
              ? "Showing all conversions"
              : conversionType === "production"
              ? "Showing production conversions only"
              : "Showing test conversions only"}
          </CardDescription>
        </CardHeader>

        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Agent</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Type</TableHead>
                <TableHead className="w-[80px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {stats.conversions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-10 text-muted-foreground">
                    No conversions found for the selected filters.
                  </TableCell>
                </TableRow>
              ) : (
                stats.conversions.map((conversion) => {
                  const isTest = conversion.source === "test" ||
                                conversion.source === "pixel_test" ||
                                conversion.source?.includes("test") ||
                                conversion.is_test === true;

                  return (
                    <TableRow
                      key={conversion.id}
                      className={isTest ? "bg-white-50" : ""}
                    >
                      <TableCell>{formatDate(conversion.timestamp)}</TableCell>
                      <TableCell>
                        {/* Conversion value is stored in cents in the database, formatCurrency converts to dollars */}
                        {formatCurrency(conversion.value, conversion.currency)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {conversion.source}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {conversion.agent_id ? conversion.agent_id.substring(0, 8) : "—"}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={conversion.status === "confirmed" || conversion.status === "verified" ? "default" : "outline"}
                          className={
                            conversion.status === "confirmed" || conversion.status === "verified"
                              ? "bg-green-500 text-white"
                              : conversion.status === "pending"
                              ? "bg-amber-100 text-amber-700"
                              : "bg-red-100 text-red-700"
                          }
                        >
                          {conversion.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={isTest
                            ? "bg-blue-100 text-blue-700 border-blue-200"
                            : "bg-green-100 text-green-700 border-green-200"
                          }
                        >
                          {isTest ? "Test" : "Production"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {isTest && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => setConfirmDeleteConversionId(conversion.id)}
                                  disabled={deletingSingleConversion === conversion.id}
                                >
                                  {deletingSingleConversion === conversion.id ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete this test conversion</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Offer Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-semibold">Offer Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-primary mb-3">Basic Information</h3>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">Title</p>
                    <p className="font-semibold">{stats.title}</p>
                  </div>

                  {stats.description && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Description</p>
                      <p className="text-sm">{stats.description}</p>
                    </div>
                  )}

                  {stats.url && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Landing URL</p>
                      <a
                        href={stats.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:underline break-all"
                      >
                        {stats.url}
                      </a>
                    </div>
                  )}

                  {stats.product_name && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Product</p>
                      <p className="text-sm">{stats.product_name}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Payout & Budget */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-primary mb-3">Payout & Budget</h3>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">Payout Model</p>
                    <p className="font-semibold">
                      {/* Payout amount is stored in cents in the database, formatCurrency converts to dollars */}
                      {stats.payout?.model || "CPA"} - {formatCurrency(stats.payout?.amount || 0, stats.payout?.currency)}
                    </p>
                  </div>

                  {stats.goal && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Goal</p>
                      <p className="text-sm capitalize">{stats.goal}</p>
                    </div>
                  )}

                  {stats.reward_note && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Reward Note</p>
                      <p className="text-sm">{stats.reward_note}</p>
                    </div>
                  )}

                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">Total Budget</p>
                    <p className="text-sm">
                      {/* Budget is stored in cents in the database, formatCurrency converts to dollars */}
                      {formatCurrency(stats.offer_total_budget_allocated || 0, stats.payout?.currency)}
                    </p>
                  </div>


                  {stats.valid_until && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Valid Until</p>
                      <p className="text-sm">{formatDate(stats.valid_until)}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Details */}
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-primary mb-3">Additional Details</h3>

                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">Status</p>
                    <Badge
                      variant={stats.offer_status === "active" ? "default" : "outline"}
                      className={
                        stats.offer_status === "active"
                          ? "bg-green-100 text-green-700 border-green-200"
                          : stats.offer_status === "cancelled"
                          ? "bg-red-100 text-red-700 border-red-200"
                          : "bg-gray-100 text-gray-600 border-gray-200"
                      }
                    >
                      {stats.offer_status || stats.status || (stats.active ? "active" : "inactive")}
                    </Badge>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">Created</p>
                    <p className="text-sm">{formatDate(stats.created_at)}</p>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">Last Updated</p>
                    <p className="text-sm">{formatDate(stats.updated_at) || "Never"}</p>
                  </div>

                  {stats.suggestion_reason && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Suggestion Reason</p>
                      <p className="text-sm">{stats.suggestion_reason}</p>
                    </div>
                  )}

                  {stats.trust_score !== undefined && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">Trust Score</p>
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-semibold">{stats.trust_score || 0}/100</p>
                        <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${stats.trust_score || 0}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Categories & Keywords */}
          {((stats.categories && stats.categories.length > 0) || (stats.keywords && stats.keywords.length > 0)) && (
            <div className="mt-6 pt-6 border-t border-gray-100">
              <h3 className="text-sm font-medium text-primary mb-3">Categories & Keywords</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {stats.categories && stats.categories.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Categories</p>
                    <div className="flex flex-wrap gap-2">
                      {stats.categories?.map((category, index) => (
                        <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {category}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {stats.keywords && stats.keywords.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Keywords</p>
                    <div className="flex flex-wrap gap-2">
                      {stats.keywords?.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Tracking */}
          {(stats.tracking || stats.integration) && (
            <div className="mt-6 pt-6 border-t border-gray-100">
              <h3 className="text-sm font-medium text-primary mb-3">Tracking</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Tracking Details</p>
                    <div className="space-y-2">
                      {/* Use tracking data if available, fall back to integration data */}
                      {(stats.tracking?.method || stats.integration?.method) && (
                        <div>
                          <p className="text-xs text-muted-foreground">Method:</p>
                          <p className="text-xs capitalize">{(stats.tracking?.method || stats.integration?.method || "").replace('_', ' ')}</p>
                        </div>
                      )}
                      {(stats.tracking?.webhook_url || stats.integration?.webhook_url) && (
                        <div>
                          <p className="text-xs text-muted-foreground">Webhook URL:</p>
                          <p className="text-xs break-all">{stats.tracking?.webhook_url || stats.integration?.webhook_url}</p>
                        </div>
                      )}
                      {(stats.tracking?.redirect_url || stats.integration?.redirect_url) && (
                        <div>
                          <p className="text-xs text-muted-foreground">Redirect URL:</p>
                          <p className="text-xs break-all">{stats.tracking?.redirect_url || stats.integration?.redirect_url}</p>
                        </div>
                      )}
                      {stats.tracking?.target_urls && stats.tracking.target_urls.length > 0 && (
                        <div>
                          <p className="text-xs text-muted-foreground">Target URLs:</p>
                          <div className="space-y-1">
                            {stats.tracking.target_urls.map((url, index) => (
                              <p key={index} className="text-xs break-all">{url}</p>
                            ))}
                          </div>
                        </div>
                      )}
                      {(stats.tracking?.notes || stats.integration?.notes) && (
                        <div>
                          <p className="text-xs text-muted-foreground">Notes:</p>
                          <p className="text-xs">{stats.tracking?.notes || stats.integration?.notes}</p>
                        </div>
                      )}
                      {(stats.tracking?.updated_at || stats.integration?.updated_at) && (
                        <div>
                          <p className="text-xs text-muted-foreground">Last Updated:</p>
                          <p className="text-xs">{formatDate(stats.tracking?.updated_at || stats.integration?.updated_at)}</p>
                        </div>
                      )}
                    </div>
                  </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Confirmation dialog for deleting a single test conversion */}
      <AlertDialog open={confirmDeleteConversionId !== null} onOpenChange={(open) => !open && setConfirmDeleteConversionId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Test Conversion?</AlertDialogTitle>
            <div className="text-muted-foreground text-sm">
              This will permanently delete this test conversion. This action cannot be undone.
            </div>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (confirmDeleteConversionId) {
                  handleDeleteSingleConversion(confirmDeleteConversionId);
                  setConfirmDeleteConversionId(null);
                }
              }}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={deletingSingleConversion !== null}
            >
              {deletingSingleConversion ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>Delete</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
