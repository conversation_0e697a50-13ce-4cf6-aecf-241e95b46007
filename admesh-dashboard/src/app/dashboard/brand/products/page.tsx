"use client";

import {  useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Plus,
  Gift,
  Sparkles,
  Tag,
  Pencil,
  PlusCircle,
  Search,
  ArrowUpDown,
  Loader2,
  ArrowUpCircle,
  RefreshCw,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { Badge } from "@/components/ui/badge";
import DashboardFooter from "@/components/DashboardFooter";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Toolt<PERSON>,
  TooltipContent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
// Removed unused 'Tabs', 'TabsContent', 'TabsList', and 'TabsTrigger'

interface Product {
  id: string;
  title: string;
  description: string | null;
  status: "active" | "inactive";
  is_ai_powered: boolean;
  has_free_tier: boolean;
  keywords: string[];
  categories: string[];
  view_count: number;
  clicks: number;
  conversions: number;
  created_at: number;
  category?: string;
  url?: string;
  trust_score?: number;
}

export default function ProductsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [hasActiveOffer, setHasActiveOffer] = useState(false);
  const canAddMoreProducts = true;

  // Fetch products
  useEffect(() => {
    if (!user) return;

    const fetchProducts = async () => {
      setLoading(true);
      try {
        const token = await user.getIdToken();

        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/brand/all`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!res.ok) {
          throw new Error("Failed to fetch products");
        }

        const data = await res.json();
        setProducts(data.products || []);
      } catch (err) {
        console.error("❌ Error fetching products from API:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [user]);

  // Check if user has active offers
  useEffect(() => {
    if (!user) return;

    const checkActiveOffers = async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (res.ok) {
          const data = await res.json();
          const activeOffers = data.offers.filter((offer: { offer_status: string }) => offer.offer_status === "active");
          setHasActiveOffer(activeOffers.length > 0);
        }
      } catch (err) {
        console.error("Error checking active offers:", err);
      }
    };

    checkActiveOffers();
  }, [user]);

  // Get unique categories from products
  const categories = ["all", ...new Set(products.map(p => p.category).filter(Boolean))];

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = categoryFilter === "all" || product.category === categoryFilter;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      if (sortBy === "newest") {
        return (new Date(b.created_at).getTime()) - (new Date(a.created_at).getTime());
      } else if (sortBy === "oldest") {
        return (new Date(a.created_at).getTime()) - (new Date(b.created_at).getTime());
      } else if (sortBy === "alphabetical") {
        return a.title.localeCompare(b.title);
      }
      return 0;
    });

  return (
    <div className="min-h-screen bg-gray-50/30 dark:bg-gray-900/30">
      {/* Header Section */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div className="space-y-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white tracking-tight">
                  Your Products
                </h1>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                  Manage all tools you&apos;ve submitted to AdMesh and track their performance
                </p>
                {!loading && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {products.length} {products.length === 1 ? 'product' : 'products'} in total
                  </div>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-3 lg:shrink-0">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <Button
                          onClick={() => router.push("/dashboard/brand/products/new")}
                          className="bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100 text-white font-medium px-4 sm:px-6 py-2.5 rounded-lg transition-all duration-200 flex items-center gap-2 justify-center"
                          disabled={!canAddMoreProducts}
                        >
                          <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
                          Add Product
                        </Button>
                      </div>
                    </TooltipTrigger>
                    {!canAddMoreProducts && (
                      <TooltipContent>
                        <p>You&apos;ve reached your product limit. <br />Upgrade your subscription to add more products.</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
               
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="max-w-7xl mx-auto space-y-6 sm:space-y-8">

          {/* Search and Filter Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6 shadow-sm">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
              <div className="lg:col-span-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <Input
                    placeholder="Search products..."
                    className="pl-10 w-full h-10 border-gray-200 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-gray-400 dark:focus:ring-gray-500 rounded-lg dark:bg-gray-700 dark:text-white"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full h-10 border-gray-200 dark:border-gray-600 focus:border-gray-400 dark:focus:border-gray-500 focus:ring-gray-400 dark:focus:ring-gray-500 rounded-lg dark:bg-gray-700 dark:text-white">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category || ""}>
                        {category === "all" ? "All Categories" : category || "Uncategorized"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="shrink-0 h-10 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg dark:bg-gray-700 dark:text-white">
                      <ArrowUpDown className="h-4 w-4 mr-2" />
                      Sort
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSortBy("newest")}>
                      Newest First
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                      Oldest First
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy("alphabetical")}>
                      Alphabetical
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-16 sm:py-24">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-gray-600 dark:text-gray-400 mx-auto mb-4" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Loading products...</p>
              </div>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl py-12 sm:py-16 text-center">
              <div className="max-w-md mx-auto px-4">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No products found</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {products.length === 0
                    ? "Get started by adding your first product to AdMesh."
                    : "Try adjusting your search or filters."}
                </p>
                {products.length === 0 && (
                  canAddMoreProducts ? (
                    <Button
                      onClick={() => router.push("/dashboard/brand/products/new")}
                      className="bg-gray-900 hover:bg-gray-800 text-white"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Your First Product
                    </Button>
                  ) : (
                    <Button
                      onClick={() => router.push("/dashboard/brand/subscription")}
                      className="bg-gray-900 hover:bg-gray-800 text-white"
                    >
                      <ArrowUpCircle className="w-4 h-4 mr-2" />
                      Upgrade to Add Products
                    </Button>
                  )
                )}
              </div>
            </div>
          ) : (
            <div>
              {/* Products Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                {filteredProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-md transition-all duration-200 group rounded-xl">
                    <CardHeader className="pb-3 p-4 sm:p-6">
                      <div className="flex items-start justify-between gap-3">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors leading-tight">
                            {product.title}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge
                              variant={product.status === "active" ? "default" : "secondary"}
                              className={`text-xs ${
                                product.status === "active"
                                  ? "bg-green-100 text-green-800 border-green-200"
                                  : "bg-gray-100 text-gray-600 border-gray-200"
                              }`}
                            >
                              {product.status}
                            </Badge>
                            {product.category && (
                              <Badge variant="outline" className="text-xs text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-600">
                                {product.category}
                              </Badge>
                            )}
                          </div>
                        </div>
                        {product.trust_score && (
                          <div className="text-right">
                            <div className="text-xs text-gray-500 dark:text-gray-400">Trust Score</div>
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">{product.trust_score}%</div>
                          </div>
                        )}
                      </div>
                    </CardHeader>

                    <CardContent className="pb-3 px-4 sm:px-6">
                      {product.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-4 leading-relaxed">
                          {product.description}
                        </p>
                      )}

                      {/* Performance Metrics */}
                      <div className="grid grid-cols-3 gap-3 mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-center">
                          <div className="text-lg font-semibold text-gray-900 dark:text-white">{product.view_count || 0}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">Views</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-gray-900 dark:text-white">{product.clicks || 0}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">Clicks</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-gray-900 dark:text-white">{product.conversions || 0}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">Conversions</div>
                        </div>
                      </div>

                      {/* Tags and Features */}
                      <div className="flex flex-wrap gap-2">
                        {product.is_ai_powered && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                            <Sparkles className="h-3 w-3 mr-1" />
                            AI Powered
                          </Badge>
                        )}

                        {product.has_free_tier && (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs">
                            <Gift className="h-3 w-3 mr-1" />
                            Free Tier
                          </Badge>
                        )}

                        {/* Show categories if available */}
                        {product.categories?.slice(0, 2).map((category, i) => (
                          <Badge key={`cat-${i}`} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {category}
                          </Badge>
                        ))}

                        {/* Show keywords */}
                        {product.keywords?.slice(0, 2).map((keyword, i) => (
                          <Badge key={`kw-${i}`} variant="outline" className="text-gray-600 border-gray-200 text-xs">
                            <Tag className="h-3 w-3 mr-1" />
                            {keyword}
                          </Badge>
                        ))}

                        {(product.keywords?.length > 2 || product.categories?.length > 2) && (
                          <Badge variant="outline" className="text-gray-500 border-gray-200 text-xs">
                            +{(product.keywords?.length || 0) + (product.categories?.length || 0) - 4} more
                          </Badge>
                        )}
                      </div>
                    </CardContent>

                    <CardFooter className="border-t border-gray-100 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-700/50 p-4 sm:p-6">
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full">
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex-1 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 hover:bg-white dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-all duration-200 h-9"
                          onClick={() => router.push(`/dashboard/brand/products/${product.id}/edit`)}
                        >
                          <Pencil className="h-4 w-4 mr-2" />
                          Edit
                        </Button>

                        <Button
                          size="sm"
                          className="flex-1 bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100 text-white transition-all duration-200 h-9"
                          onClick={() => {
                            if (hasActiveOffer) {
                              toast.error("You can only have one active offer at a time. Please deactivate your existing offer before creating a new one.");
                            } else {
                              router.push(`/dashboard/brand/offers/new?productId=${product.id}`);
                            }
                          }}
                        >
                          <PlusCircle className="h-4 w-4 mr-2" />
                          Create Offer
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          )}

        </div>
      </div>

      {/* Dashboard Footer */}
      <DashboardFooter />
    </div>
  );
}