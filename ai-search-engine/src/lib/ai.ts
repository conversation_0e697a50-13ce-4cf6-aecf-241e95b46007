import OpenA<PERSON> from 'openai';
import Admesh from 'admesh';
import { SearchResult } from './search';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface AIResponseOptions {
  query: string;
  sources: SearchResult[];
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export async function generateAIResponse(options: AIResponseOptions): Promise<string> {
  const { query, sources, model = 'gpt-4', temperature = 0.3, maxTokens = 1000 } = options;

  // Prepare context from sources
  const context = sources
    .map((source, index) => 
      `Source ${index + 1}: ${source.title}\nURL: ${source.url}\nContent: ${source.content || source.snippet}\n`
    )
    .join('\n---\n');

  const systemPrompt = `You are an AI search assistant similar to Perplexity AI. Your task is to provide comprehensive, accurate, and well-structured answers based on the search results provided.

Guidelines:
- Synthesize information from multiple sources to create a cohesive response
- Include relevant citations using [1], [2], [3] format corresponding to the source numbers
- Be objective and factual, avoiding speculation
- If information is conflicting between sources, acknowledge this
- Structure your response with clear paragraphs and logical flow
- Keep responses informative but concise
- If the sources don't contain enough information to answer the query, acknowledge this limitation
- Use markdown formatting for better readability (headers, lists, etc.)

Citation format: Use [1], [2], [3] etc. to reference sources. Place citations at the end of sentences or claims.`;

  const userPrompt = `Query: ${query}

Search Results:
${context}

Please provide a comprehensive answer based on these search results. Include proper citations and structure your response clearly.`;

  try {
    const completion = await openai.chat.completions.create({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature,
      max_tokens: maxTokens,
    });

    return completion.choices[0]?.message?.content || 'Unable to generate response';
  } catch (error) {
    console.error('AI response generation error:', error);
    throw new Error('Failed to generate AI response');
  }
}

export async function* generateStreamingAIResponse(options: AIResponseOptions): AsyncGenerator<string, void, unknown> {
  const { query, sources, model = 'gpt-4', temperature = 0.3, maxTokens = 1000 } = options;

  // Prepare context from sources
  const context = sources
    .map((source, index) => 
      `Source ${index + 1}: ${source.title}\nURL: ${source.url}\nContent: ${source.content || source.snippet}\n`
    )
    .join('\n---\n');

  const systemPrompt = `You are an AI search assistant similar to Perplexity AI. Your task is to provide comprehensive, accurate, and well-structured answers based on the search results provided.

Guidelines:
- Synthesize information from multiple sources to create a cohesive response
- Include relevant citations using [1], [2], [3] format corresponding to the source numbers
- Be objective and factual, avoiding speculation
- If information is conflicting between sources, acknowledge this
- Structure your response with clear paragraphs and logical flow
- Keep responses informative but concise
- If the sources don't contain enough information to answer the query, acknowledge this limitation
- Use markdown formatting for better readability (headers, lists, etc.)

Citation format: Use [1], [2], [3] etc. to reference sources. Place citations at the end of sentences or claims.`;

  const userPrompt = `Query: ${query}

Search Results:
${context}

Please provide a comprehensive answer based on these search results. Include proper citations and structure your response clearly.`;

  try {
    const stream = await openai.chat.completions.create({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature,
      max_tokens: maxTokens,
      stream: true,
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        yield content;
      }
    }
  } catch (error) {
    console.error('Streaming AI response error:', error);
    throw new Error('Failed to generate streaming AI response');
  }
}

// Utility function to validate AI response quality
export function validateAIResponse(response: string, sources: SearchResult[]): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  // Check if response is too short
  if (response.length < 100) {
    issues.push('Response is too short');
  }
  
  // Check if response contains citations
  const citationPattern = /\[\d+\]/g;
  const citations = response.match(citationPattern);
  if (!citations || citations.length === 0) {
    issues.push('Response lacks proper citations');
  }
  
  // Check if citations reference valid sources
  if (citations) {
    const maxSourceIndex = sources.length;
    for (const citation of citations) {
      const index = parseInt(citation.match(/\d+/)?.[0] || '0');
      if (index > maxSourceIndex) {
        issues.push(`Citation [${index}] references non-existent source`);
      }
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

// Function to enhance response with additional formatting
export function formatAIResponse(response: string): string {
  // Add proper spacing around citations
  let formatted = response.replace(/\[(\d+)\]/g, ' [$1]');
  
  // Clean up extra spaces
  formatted = formatted.replace(/\s+/g, ' ').trim();
  
  // Ensure proper paragraph breaks
  formatted = formatted.replace(/\n\s*\n/g, '\n\n');
  
  return formatted;
}

// Simple Admesh service
class AdmeshService {
  private client: Admesh | null = null;

  constructor() {
    const apiKey = process.env.ADMESH_API_KEY;
    if (apiKey) {
      this.client = new Admesh({
        apiKey,
        baseURL: process.env.ADMESH_BASE_URL || 'https://api.useadmesh.com'
      });
    }
  }

  async getRecommendations(query: string) {
    if (!this.client) {
      console.log('🚨 AdMesh client not initialized - missing API key?');
      return null;
    }

    console.log('🔍 Making AdMesh API request for query:', query);
    try {
      const response = await this.client.recommend.getRecommendations({
        query,
        format: 'auto'
      });
      console.log('✅ AdMesh API response received:', response);
      console.log('🔍 Response structure:', {
        hasResponse: !!response.response,
        hasRecommendations: !!response.response?.recommendations,
        recommendationCount: response.response?.recommendations?.length || 0,
        firstRecommendation: response.response?.recommendations?.[0] || null
      });
      return response;
    } catch (error) {
      console.error('🚨 AdMesh API error:', error);
      return null;
    }
  }
}

export const admeshService = new AdmeshService();
