module.exports = {

"[project]/.next-internal/server/app/api/search/stream/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/node:assert [external] (node:assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:assert", () => require("node:assert"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:querystring [external] (node:querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:querystring", () => require("node:querystring"));

module.exports = mod;
}}),
"[externals]/node:events [external] (node:events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:events", () => require("node:events"));

module.exports = mod;
}}),
"[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:diagnostics_channel", () => require("node:diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:tls [external] (node:tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:tls", () => require("node:tls"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:util/types [external] (node:util/types, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util/types", () => require("node:util/types"));

module.exports = mod;
}}),
"[externals]/node:worker_threads [external] (node:worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:worker_threads", () => require("node:worker_threads"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/node:http2 [external] (node:http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http2", () => require("node:http2"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:console [external] (node:console, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:console", () => require("node:console"));

module.exports = mod;
}}),
"[externals]/node:dns [external] (node:dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:dns", () => require("node:dns"));

module.exports = mod;
}}),
"[project]/src/lib/search.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractDomain": (()=>extractDomain),
    "fetchPageContent": (()=>fetchPageContent),
    "isValidUrl": (()=>isValidUrl),
    "searchWeb": (()=>searchWeb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/load-parse.js [app-route] (ecmascript)");
;
;
async function searchWeb(query) {
    const results = [];
    try {
        // Try DuckDuckGo first
        const duckResults = await searchDuckDuckGo(query);
        results.push(...duckResults);
    } catch (error) {
        console.error('DuckDuckGo search failed:', error);
    }
    // If we don't have enough results, add some mock results for demo
    if (results.length < 3) {
        const mockResults = generateMockResults(query);
        results.push(...mockResults);
    }
    return results.slice(0, 5); // Limit to 5 results
}
async function searchDuckDuckGo(query) {
    const searchUrl = `https://duckduckgo.com/html/?q=${encodeURIComponent(query)}`;
    const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(searchUrl, {
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        timeout: 10000
    });
    const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
    const results = [];
    $('.result').each((i, element)=>{
        if (i >= 5) return false;
        const titleElement = $(element).find('.result__title a');
        const snippetElement = $(element).find('.result__snippet');
        const title = titleElement.text().trim();
        const url = titleElement.attr('href') || '';
        const snippet = snippetElement.text().trim();
        if (title && url && snippet) {
            results.push({
                title,
                url: url.startsWith('//') ? `https:${url}` : url,
                snippet,
                favicon: `https://www.google.com/s2/favicons?domain=${new URL(url.startsWith('//') ? `https:${url}` : url).hostname}`
            });
        }
    });
    return results;
}
function generateMockResults(query) {
    const topics = [
        {
            title: `Understanding ${query}: A Comprehensive Guide`,
            url: 'https://example.com/guide',
            snippet: `This comprehensive guide covers everything you need to know about ${query}, including key concepts, practical applications, and expert insights.`
        },
        {
            title: `Latest Developments in ${query}`,
            url: 'https://news.example.com/latest',
            snippet: `Stay up to date with the latest news and developments related to ${query}. Expert analysis and breaking updates.`
        },
        {
            title: `${query}: Best Practices and Tips`,
            url: 'https://tips.example.com/best-practices',
            snippet: `Learn the best practices and professional tips for ${query}. Proven strategies from industry experts.`
        }
    ];
    return topics.map((topic)=>({
            ...topic,
            favicon: 'https://www.google.com/s2/favicons?domain=example.com'
        }));
}
async function fetchPageContent(url) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            timeout: 8000,
            maxContentLength: 100000,
            maxRedirects: 3
        });
        const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
        // Remove unwanted elements
        $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();
        // Try to find main content in order of preference
        const contentSelectors = [
            'main article',
            'article',
            'main',
            '.content',
            '#content',
            '.post-content',
            '.entry-content',
            '.article-content',
            '.story-body',
            '.post-body'
        ];
        let content = '';
        for (const selector of contentSelectors){
            const element = $(selector).first();
            if (element.length > 0) {
                content = element.text().trim();
                if (content.length > 200) break; // Good enough content found
            }
        }
        // Fallback to body content if no specific content area found
        if (!content || content.length < 200) {
            content = $('body').text().trim();
        }
        // Clean up the content
        content = content.replace(/\s+/g, ' ') // Replace multiple whitespace with single space
        .replace(/\n+/g, '\n') // Replace multiple newlines with single newline
        .substring(0, 3000); // Limit content length
        return content;
    } catch (error) {
        console.error(`Error fetching content from ${url}:`, error);
        return '';
    }
}
function extractDomain(url) {
    try {
        return new URL(url).hostname;
    } catch  {
        return 'unknown';
    }
}
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch  {
        return false;
    }
}
}}),
"[project]/src/lib/ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatAIResponse": (()=>formatAIResponse),
    "generateAIResponse": (()=>generateAIResponse),
    "generateStreamingAIResponse": (()=>generateStreamingAIResponse),
    "validateAIResponse": (()=>validateAIResponse)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
async function generateAIResponse(options) {
    const { query, sources, model = 'gpt-4', temperature = 0.3, maxTokens = 1000 } = options;
    // Prepare context from sources
    const context = sources.map((source, index)=>`Source ${index + 1}: ${source.title}\nURL: ${source.url}\nContent: ${source.content || source.snippet}\n`).join('\n---\n');
    const systemPrompt = `You are an AI search assistant similar to Perplexity AI. Your task is to provide comprehensive, accurate, and well-structured answers based on the search results provided.

Guidelines:
- Synthesize information from multiple sources to create a cohesive response
- Include relevant citations using [1], [2], [3] format corresponding to the source numbers
- Be objective and factual, avoiding speculation
- If information is conflicting between sources, acknowledge this
- Structure your response with clear paragraphs and logical flow
- Keep responses informative but concise
- If the sources don't contain enough information to answer the query, acknowledge this limitation
- Use markdown formatting for better readability (headers, lists, etc.)

Citation format: Use [1], [2], [3] etc. to reference sources. Place citations at the end of sentences or claims.`;
    const userPrompt = `Query: ${query}

Search Results:
${context}

Please provide a comprehensive answer based on these search results. Include proper citations and structure your response clearly.`;
    try {
        const completion = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                {
                    role: 'user',
                    content: userPrompt
                }
            ],
            temperature,
            max_tokens: maxTokens
        });
        return completion.choices[0]?.message?.content || 'Unable to generate response';
    } catch (error) {
        console.error('AI response generation error:', error);
        throw new Error('Failed to generate AI response');
    }
}
async function* generateStreamingAIResponse(options) {
    const { query, sources, model = 'gpt-4', temperature = 0.3, maxTokens = 1000 } = options;
    // Prepare context from sources
    const context = sources.map((source, index)=>`Source ${index + 1}: ${source.title}\nURL: ${source.url}\nContent: ${source.content || source.snippet}\n`).join('\n---\n');
    const systemPrompt = `You are an AI search assistant similar to Perplexity AI. Your task is to provide comprehensive, accurate, and well-structured answers based on the search results provided.

Guidelines:
- Synthesize information from multiple sources to create a cohesive response
- Include relevant citations using [1], [2], [3] format corresponding to the source numbers
- Be objective and factual, avoiding speculation
- If information is conflicting between sources, acknowledge this
- Structure your response with clear paragraphs and logical flow
- Keep responses informative but concise
- If the sources don't contain enough information to answer the query, acknowledge this limitation
- Use markdown formatting for better readability (headers, lists, etc.)

Citation format: Use [1], [2], [3] etc. to reference sources. Place citations at the end of sentences or claims.`;
    const userPrompt = `Query: ${query}

Search Results:
${context}

Please provide a comprehensive answer based on these search results. Include proper citations and structure your response clearly.`;
    try {
        const stream = await openai.chat.completions.create({
            model,
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                {
                    role: 'user',
                    content: userPrompt
                }
            ],
            temperature,
            max_tokens: maxTokens,
            stream: true
        });
        for await (const chunk of stream){
            const content = chunk.choices[0]?.delta?.content;
            if (content) {
                yield content;
            }
        }
    } catch (error) {
        console.error('Streaming AI response error:', error);
        throw new Error('Failed to generate streaming AI response');
    }
}
function validateAIResponse(response, sources) {
    const issues = [];
    // Check if response is too short
    if (response.length < 100) {
        issues.push('Response is too short');
    }
    // Check if response contains citations
    const citationPattern = /\[\d+\]/g;
    const citations = response.match(citationPattern);
    if (!citations || citations.length === 0) {
        issues.push('Response lacks proper citations');
    }
    // Check if citations reference valid sources
    if (citations) {
        const maxSourceIndex = sources.length;
        for (const citation of citations){
            const index = parseInt(citation.match(/\d+/)?.[0] || '0');
            if (index > maxSourceIndex) {
                issues.push(`Citation [${index}] references non-existent source`);
            }
        }
    }
    return {
        isValid: issues.length === 0,
        issues
    };
}
function formatAIResponse(response) {
    // Add proper spacing around citations
    let formatted = response.replace(/\[(\d+)\]/g, ' [$1]');
    // Clean up extra spaces
    formatted = formatted.replace(/\s+/g, ' ').trim();
    // Ensure proper paragraph breaks
    formatted = formatted.replace(/\n\s*\n/g, '\n\n');
    return formatted;
}
}}),
"[project]/src/lib/admesh.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "admeshService": (()=>admeshService),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$admesh$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/admesh/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$admesh$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Admesh__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/admesh/client.mjs [app-route] (ecmascript) <export Admesh as default>");
;
class AdmeshService {
    client = null;
    isInitialized = false;
    constructor(){
        this.initializeClient();
    }
    initializeClient() {
        try {
            const apiKey = process.env.ADMESH_API_KEY;
            if (!apiKey) {
                console.warn('ADMESH_API_KEY not found in environment variables');
                return;
            }
            this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$admesh$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__Admesh__as__default$3e$__["default"]({
                apiKey,
                baseURL: process.env.ADMESH_BASE_URL || 'https://api.useadmesh.com'
            });
            this.isInitialized = true;
            console.log('Admesh client initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Admesh client:', error);
            this.isInitialized = false;
        }
    }
    async getRecommendations(query) {
        if (!this.isInitialized || !this.client) {
            return {
                recommendations: [],
                query,
                success: false,
                error: 'Admesh client not initialized. Please check your API key configuration.'
            };
        }
        try {
            console.log(`Getting Admesh recommendations for query: "${query}"`);
            const response = await this.client.recommend.getRecommendations({
                query,
                format: 'auto'
            });
            console.log('Raw Admesh API response:', JSON.stringify(response, null, 2));
            // Transform the response to match our interface
            // Handle different possible response structures
            let rawRecommendations = [];
            const responseAny = response; // Type assertion for flexibility
            if (responseAny.response?.recommendations) {
                // Admesh API structure: response.response.recommendations
                rawRecommendations = responseAny.response.recommendations;
            } else if (responseAny.recommendations) {
                rawRecommendations = responseAny.recommendations;
            } else if (Array.isArray(responseAny)) {
                rawRecommendations = responseAny;
            } else if (responseAny.data) {
                rawRecommendations = responseAny.data;
            } else if (responseAny.results) {
                rawRecommendations = responseAny.results;
            } else {
                console.warn('Unknown Admesh response structure:', responseAny);
            }
            const recommendations = rawRecommendations.map((rec)=>({
                    title: rec.title || rec.name || 'Untitled Recommendation',
                    description: rec.description || rec.snippet || rec.summary || 'No description available',
                    url: rec.url || rec.link || rec.admesh_link,
                    category: rec.category || rec.type || 'General',
                    relevanceScore: rec.relevanceScore || rec.score || rec.match_score || 0.8
                }));
            console.log(`✅ Successfully received ${recommendations.length} recommendations from Admesh`);
            console.log('Transformed recommendations:', recommendations);
            return {
                recommendations,
                query,
                success: true
            };
        } catch (error) {
            console.error('Error getting Admesh recommendations:', error);
            return {
                recommendations: [],
                query,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    // Fallback recommendations when Admesh is not available
    getFallbackRecommendations(query) {
        const fallbackRecommendations = [
            {
                title: `Explore more about "${query}"`,
                description: `Discover comprehensive resources and insights related to ${query}`,
                category: 'General'
            },
            {
                title: `Latest trends in ${query}`,
                description: `Stay updated with the most recent developments and trends`,
                category: 'Trends'
            },
            {
                title: `Best practices for ${query}`,
                description: `Learn proven strategies and methodologies from experts`,
                category: 'Best Practices'
            }
        ];
        return fallbackRecommendations;
    }
    async getRecommendationsWithFallback(query) {
        const result = await this.getRecommendations(query);
        // If Admesh fails or returns no recommendations, provide fallback
        if (!result.success || result.recommendations.length === 0) {
            return {
                recommendations: this.getFallbackRecommendations(query),
                query,
                success: true,
                error: result.error ? `Admesh unavailable: ${result.error}. Showing fallback recommendations.` : undefined
            };
        }
        return result;
    }
    isAvailable() {
        return this.isInitialized && this.client !== null;
    }
}
const admeshService = new AdmeshService();
const __TURBOPACK__default__export__ = admeshService;
}}),
"[project]/src/app/api/search/stream/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/search.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$admesh$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/admesh.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    const { query } = await request.json();
    if (!query || typeof query !== 'string') {
        return new Response('Query is required', {
            status: 400
        });
    }
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
        async start (controller) {
            try {
                // Send initial status
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'status',
                    message: 'Searching the web...'
                })}\n\n`));
                // Search the web
                const searchResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchWeb"])(query);
                // Send search results
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'sources',
                    data: searchResults.map((result, index)=>({
                            id: index + 1,
                            title: result.title,
                            url: result.url,
                            snippet: result.snippet,
                            favicon: result.favicon
                        }))
                })}\n\n`));
                // Get Admesh recommendations
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'status',
                    message: 'Getting smart recommendations...'
                })}\n\n`));
                const admeshResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$admesh$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["admeshService"].getRecommendationsWithFallback(query);
                // Send recommendations
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'recommendations',
                    data: admeshResponse
                })}\n\n`));
                // Update status
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'status',
                    message: 'Analyzing content...'
                })}\n\n`));
                // Fetch content from top results
                const resultsWithContent = await Promise.all(searchResults.slice(0, 3).map(async (result)=>{
                    const content = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fetchPageContent"])(result.url);
                    return {
                        ...result,
                        content
                    };
                }));
                // Update status
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'status',
                    message: 'Generating AI response...'
                })}\n\n`));
                // Stream AI response using the improved utility
                const streamGenerator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateStreamingAIResponse"])({
                    query,
                    sources: resultsWithContent
                });
                for await (const content of streamGenerator){
                    controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                        type: 'content',
                        data: content
                    })}\n\n`));
                }
                // Send completion signal
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'complete'
                })}\n\n`));
            } catch (error) {
                console.error('Streaming error:', error);
                controller.enqueue(encoder.encode(`data: ${JSON.stringify({
                    type: 'error',
                    message: 'An error occurred'
                })}\n\n`));
            } finally{
                controller.close();
            }
        }
    });
    return new Response(stream, {
        headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e50470c5._.js.map