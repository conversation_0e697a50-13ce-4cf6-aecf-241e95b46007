{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/src/lib/search.ts"], "sourcesContent": ["import axios from 'axios';\nimport * as cheerio from 'cheerio';\n\nexport interface SearchResult {\n  title: string;\n  url: string;\n  snippet: string;\n  content?: string;\n  favicon?: string;\n}\n\n// Enhanced web search with multiple fallback strategies\nexport async function searchWeb(query: string): Promise<SearchResult[]> {\n  const results: SearchResult[] = [];\n  \n  try {\n    // Try DuckDuckGo first\n    const duckResults = await searchDuckDuckGo(query);\n    results.push(...duckResults);\n  } catch (error) {\n    console.error('DuckDuckGo search failed:', error);\n  }\n\n  // If we don't have enough results, add some mock results for demo\n  if (results.length < 3) {\n    const mockResults = generateMockResults(query);\n    results.push(...mockResults);\n  }\n\n  return results.slice(0, 5); // Limit to 5 results\n}\n\nasync function searchDuckDuckGo(query: string): Promise<SearchResult[]> {\n  const searchUrl = `https://duckduckgo.com/html/?q=${encodeURIComponent(query)}`;\n  \n  const response = await axios.get(searchUrl, {\n    headers: {\n      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n    },\n    timeout: 10000\n  });\n\n  const $ = cheerio.load(response.data);\n  const results: SearchResult[] = [];\n\n  $('.result').each((i, element) => {\n    if (i >= 5) return false;\n    \n    const titleElement = $(element).find('.result__title a');\n    const snippetElement = $(element).find('.result__snippet');\n    \n    const title = titleElement.text().trim();\n    const url = titleElement.attr('href') || '';\n    const snippet = snippetElement.text().trim();\n\n    if (title && url && snippet) {\n      results.push({\n        title,\n        url: url.startsWith('//') ? `https:${url}` : url,\n        snippet,\n        favicon: `https://www.google.com/s2/favicons?domain=${new URL(url.startsWith('//') ? `https:${url}` : url).hostname}`\n      });\n    }\n  });\n\n  return results;\n}\n\nfunction generateMockResults(query: string): SearchResult[] {\n  const topics = [\n    {\n      title: `Understanding ${query}: A Comprehensive Guide`,\n      url: 'https://example.com/guide',\n      snippet: `This comprehensive guide covers everything you need to know about ${query}, including key concepts, practical applications, and expert insights.`\n    },\n    {\n      title: `Latest Developments in ${query}`,\n      url: 'https://news.example.com/latest',\n      snippet: `Stay up to date with the latest news and developments related to ${query}. Expert analysis and breaking updates.`\n    },\n    {\n      title: `${query}: Best Practices and Tips`,\n      url: 'https://tips.example.com/best-practices',\n      snippet: `Learn the best practices and professional tips for ${query}. Proven strategies from industry experts.`\n    }\n  ];\n\n  return topics.map(topic => ({\n    ...topic,\n    favicon: 'https://www.google.com/s2/favicons?domain=example.com'\n  }));\n}\n\n// Enhanced content fetching with better error handling\nexport async function fetchPageContent(url: string): Promise<string> {\n  try {\n    const response = await axios.get(url, {\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n      },\n      timeout: 8000,\n      maxContentLength: 100000, // Limit content size\n      maxRedirects: 3\n    });\n\n    const $ = cheerio.load(response.data);\n    \n    // Remove unwanted elements\n    $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();\n    \n    // Try to find main content in order of preference\n    const contentSelectors = [\n      'main article',\n      'article',\n      'main',\n      '.content',\n      '#content',\n      '.post-content',\n      '.entry-content',\n      '.article-content',\n      '.story-body',\n      '.post-body'\n    ];\n\n    let content = '';\n    for (const selector of contentSelectors) {\n      const element = $(selector).first();\n      if (element.length > 0) {\n        content = element.text().trim();\n        if (content.length > 200) break; // Good enough content found\n      }\n    }\n\n    // Fallback to body content if no specific content area found\n    if (!content || content.length < 200) {\n      content = $('body').text().trim();\n    }\n\n    // Clean up the content\n    content = content\n      .replace(/\\s+/g, ' ') // Replace multiple whitespace with single space\n      .replace(/\\n+/g, '\\n') // Replace multiple newlines with single newline\n      .substring(0, 3000); // Limit content length\n\n    return content;\n  } catch (error) {\n    console.error(`Error fetching content from ${url}:`, error);\n    return '';\n  }\n}\n\n// Utility function to extract domain from URL\nexport function extractDomain(url: string): string {\n  try {\n    return new URL(url).hostname;\n  } catch {\n    return 'unknown';\n  }\n}\n\n// Utility function to validate URLs\nexport function isValidUrl(string: string): boolean {\n  try {\n    new URL(string);\n    return true;\n  } catch {\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AAWO,eAAe,UAAU,KAAa;IAC3C,MAAM,UAA0B,EAAE;IAElC,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,iBAAiB;QAC3C,QAAQ,IAAI,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,kEAAkE;IAClE,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,cAAc,oBAAoB;QACxC,QAAQ,IAAI,IAAI;IAClB;IAEA,OAAO,QAAQ,KAAK,CAAC,GAAG,IAAI,qBAAqB;AACnD;AAEA,eAAe,iBAAiB,KAAa;IAC3C,MAAM,YAAY,CAAC,+BAA+B,EAAE,mBAAmB,QAAQ;IAE/E,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,WAAW;QAC1C,SAAS;YACP,cAAc;QAChB;QACA,SAAS;IACX;IAEA,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;IACpC,MAAM,UAA0B,EAAE;IAElC,EAAE,WAAW,IAAI,CAAC,CAAC,GAAG;QACpB,IAAI,KAAK,GAAG,OAAO;QAEnB,MAAM,eAAe,EAAE,SAAS,IAAI,CAAC;QACrC,MAAM,iBAAiB,EAAE,SAAS,IAAI,CAAC;QAEvC,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI;QACtC,MAAM,MAAM,aAAa,IAAI,CAAC,WAAW;QACzC,MAAM,UAAU,eAAe,IAAI,GAAG,IAAI;QAE1C,IAAI,SAAS,OAAO,SAAS;YAC3B,QAAQ,IAAI,CAAC;gBACX;gBACA,KAAK,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,GAAG;gBAC7C;gBACA,SAAS,CAAC,0CAA0C,EAAE,IAAI,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,QAAQ,EAAE;YACvH;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,oBAAoB,KAAa;IACxC,MAAM,SAAS;QACb;YACE,OAAO,CAAC,cAAc,EAAE,MAAM,uBAAuB,CAAC;YACtD,KAAK;YACL,SAAS,CAAC,kEAAkE,EAAE,MAAM,sEAAsE,CAAC;QAC7J;QACA;YACE,OAAO,CAAC,uBAAuB,EAAE,OAAO;YACxC,KAAK;YACL,SAAS,CAAC,iEAAiE,EAAE,MAAM,uCAAuC,CAAC;QAC7H;QACA;YACE,OAAO,GAAG,MAAM,yBAAyB,CAAC;YAC1C,KAAK;YACL,SAAS,CAAC,mDAAmD,EAAE,MAAM,0CAA0C,CAAC;QAClH;KACD;IAED,OAAO,OAAO,GAAG,CAAC,CAAA,QAAS,CAAC;YAC1B,GAAG,KAAK;YACR,SAAS;QACX,CAAC;AACH;AAGO,eAAe,iBAAiB,GAAW;IAChD,IAAI;QACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;YACpC,SAAS;gBACP,cAAc;YAChB;YACA,SAAS;YACT,kBAAkB;YAClB,cAAc;QAChB;QAEA,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;QAEpC,2BAA2B;QAC3B,EAAE,kFAAkF,MAAM;QAE1F,kDAAkD;QAClD,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,IAAI,UAAU;QACd,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,UAAU,QAAQ,IAAI,GAAG,IAAI;gBAC7B,IAAI,QAAQ,MAAM,GAAG,KAAK,OAAO,4BAA4B;YAC/D;QACF;QAEA,6DAA6D;QAC7D,IAAI,CAAC,WAAW,QAAQ,MAAM,GAAG,KAAK;YACpC,UAAU,EAAE,QAAQ,IAAI,GAAG,IAAI;QACjC;QAEA,uBAAuB;QACvB,UAAU,QACP,OAAO,CAAC,QAAQ,KAAK,gDAAgD;SACrE,OAAO,CAAC,QAAQ,MAAM,gDAAgD;SACtE,SAAS,CAAC,GAAG,OAAO,uBAAuB;QAE9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC,EAAE;QACrD,OAAO;IACT;AACF;AAGO,SAAS,cAAc,GAAW;IACvC,IAAI;QACF,OAAO,IAAI,IAAI,KAAK,QAAQ;IAC9B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,WAAW,MAAc;IACvC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/src/lib/ai.ts"], "sourcesContent": ["import OpenA<PERSON> from 'openai';\nimport Admesh from 'admesh';\nimport { SearchResult } from './search';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\nexport interface AIResponseOptions {\n  query: string;\n  sources: SearchResult[];\n  model?: string;\n  temperature?: number;\n  maxTokens?: number;\n}\n\nexport async function generateAIResponse(options: AIResponseOptions): Promise<string> {\n  const { query, sources, model = 'gpt-4', temperature = 0.3, maxTokens = 1000 } = options;\n\n  // Prepare context from sources\n  const context = sources\n    .map((source, index) => \n      `Source ${index + 1}: ${source.title}\\nURL: ${source.url}\\nContent: ${source.content || source.snippet}\\n`\n    )\n    .join('\\n---\\n');\n\n  const systemPrompt = `You are an AI search assistant similar to Perplexity AI. Your task is to provide comprehensive, accurate, and well-structured answers based on the search results provided.\n\nGuidelines:\n- Synthesize information from multiple sources to create a cohesive response\n- Include relevant citations using [1], [2], [3] format corresponding to the source numbers\n- Be objective and factual, avoiding speculation\n- If information is conflicting between sources, acknowledge this\n- Structure your response with clear paragraphs and logical flow\n- Keep responses informative but concise\n- If the sources don't contain enough information to answer the query, acknowledge this limitation\n- Use markdown formatting for better readability (headers, lists, etc.)\n\nCitation format: Use [1], [2], [3] etc. to reference sources. Place citations at the end of sentences or claims.`;\n\n  const userPrompt = `Query: ${query}\n\nSearch Results:\n${context}\n\nPlease provide a comprehensive answer based on these search results. Include proper citations and structure your response clearly.`;\n\n  try {\n    const completion = await openai.chat.completions.create({\n      model,\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      temperature,\n      max_tokens: maxTokens,\n    });\n\n    return completion.choices[0]?.message?.content || 'Unable to generate response';\n  } catch (error) {\n    console.error('AI response generation error:', error);\n    throw new Error('Failed to generate AI response');\n  }\n}\n\nexport async function* generateStreamingAIResponse(options: AIResponseOptions): AsyncGenerator<string, void, unknown> {\n  const { query, sources, model = 'gpt-4', temperature = 0.3, maxTokens = 1000 } = options;\n\n  // Prepare context from sources\n  const context = sources\n    .map((source, index) => \n      `Source ${index + 1}: ${source.title}\\nURL: ${source.url}\\nContent: ${source.content || source.snippet}\\n`\n    )\n    .join('\\n---\\n');\n\n  const systemPrompt = `You are an AI search assistant similar to Perplexity AI. Your task is to provide comprehensive, accurate, and well-structured answers based on the search results provided.\n\nGuidelines:\n- Synthesize information from multiple sources to create a cohesive response\n- Include relevant citations using [1], [2], [3] format corresponding to the source numbers\n- Be objective and factual, avoiding speculation\n- If information is conflicting between sources, acknowledge this\n- Structure your response with clear paragraphs and logical flow\n- Keep responses informative but concise\n- If the sources don't contain enough information to answer the query, acknowledge this limitation\n- Use markdown formatting for better readability (headers, lists, etc.)\n\nCitation format: Use [1], [2], [3] etc. to reference sources. Place citations at the end of sentences or claims.`;\n\n  const userPrompt = `Query: ${query}\n\nSearch Results:\n${context}\n\nPlease provide a comprehensive answer based on these search results. Include proper citations and structure your response clearly.`;\n\n  try {\n    const stream = await openai.chat.completions.create({\n      model,\n      messages: [\n        { role: 'system', content: systemPrompt },\n        { role: 'user', content: userPrompt }\n      ],\n      temperature,\n      max_tokens: maxTokens,\n      stream: true,\n    });\n\n    for await (const chunk of stream) {\n      const content = chunk.choices[0]?.delta?.content;\n      if (content) {\n        yield content;\n      }\n    }\n  } catch (error) {\n    console.error('Streaming AI response error:', error);\n    throw new Error('Failed to generate streaming AI response');\n  }\n}\n\n// Utility function to validate AI response quality\nexport function validateAIResponse(response: string, sources: SearchResult[]): {\n  isValid: boolean;\n  issues: string[];\n} {\n  const issues: string[] = [];\n  \n  // Check if response is too short\n  if (response.length < 100) {\n    issues.push('Response is too short');\n  }\n  \n  // Check if response contains citations\n  const citationPattern = /\\[\\d+\\]/g;\n  const citations = response.match(citationPattern);\n  if (!citations || citations.length === 0) {\n    issues.push('Response lacks proper citations');\n  }\n  \n  // Check if citations reference valid sources\n  if (citations) {\n    const maxSourceIndex = sources.length;\n    for (const citation of citations) {\n      const index = parseInt(citation.match(/\\d+/)?.[0] || '0');\n      if (index > maxSourceIndex) {\n        issues.push(`Citation [${index}] references non-existent source`);\n      }\n    }\n  }\n  \n  return {\n    isValid: issues.length === 0,\n    issues\n  };\n}\n\n// Function to enhance response with additional formatting\nexport function formatAIResponse(response: string): string {\n  // Add proper spacing around citations\n  let formatted = response.replace(/\\[(\\d+)\\]/g, ' [$1]');\n  \n  // Clean up extra spaces\n  formatted = formatted.replace(/\\s+/g, ' ').trim();\n  \n  // Ensure proper paragraph breaks\n  formatted = formatted.replace(/\\n\\s*\\n/g, '\\n\\n');\n  \n  return formatted;\n}\n\n// Simple Admesh service\nclass AdmeshService {\n  private client: Admesh | null = null;\n\n  constructor() {\n    const apiKey = process.env.ADMESH_API_KEY;\n    if (apiKey) {\n      this.client = new Admesh({\n        apiKey,\n        baseURL: process.env.ADMESH_BASE_URL || 'https://api.useadmesh.com'\n      });\n    }\n  }\n\n  async getRecommendations(query: string) {\n    if (!this.client) return null;\n\n    try {\n      const response = await this.client.recommend.getRecommendations({\n        query,\n        format: 'auto'\n      });\n      return response;\n    } catch (error) {\n      console.error('Admesh error:', error);\n      return null;\n    }\n  }\n}\n\nexport const admeshService = new AdmeshService();\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AAAA;;;AAGA,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAUO,eAAe,mBAAmB,OAA0B;IACjE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,OAAO,EAAE,cAAc,GAAG,EAAE,YAAY,IAAI,EAAE,GAAG;IAEjF,+BAA+B;IAC/B,MAAM,UAAU,QACb,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,EAE3G,IAAI,CAAC;IAER,MAAM,eAAe,CAAC;;;;;;;;;;;;gHAYwF,CAAC;IAE/G,MAAM,aAAa,CAAC,OAAO,EAAE,MAAM;;;AAGrC,EAAE,QAAQ;;kIAEwH,CAAC;IAEjI,IAAI;QACF,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACtD;YACA,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAAa;gBACxC;oBAAE,MAAM;oBAAQ,SAAS;gBAAW;aACrC;YACD;YACA,YAAY;QACd;QAEA,OAAO,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,gBAAgB,4BAA4B,OAA0B;IAC3E,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,OAAO,EAAE,cAAc,GAAG,EAAE,YAAY,IAAI,EAAE,GAAG;IAEjF,+BAA+B;IAC/B,MAAM,UAAU,QACb,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,EAE3G,IAAI,CAAC;IAER,MAAM,eAAe,CAAC;;;;;;;;;;;;gHAYwF,CAAC;IAE/G,MAAM,aAAa,CAAC,OAAO,EAAE,MAAM;;;AAGrC,EAAE,QAAQ;;kIAEwH,CAAC;IAEjI,IAAI;QACF,MAAM,SAAS,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAClD;YACA,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAAa;gBACxC;oBAAE,MAAM;oBAAQ,SAAS;gBAAW;aACrC;YACD;YACA,YAAY;YACZ,QAAQ;QACV;QAEA,WAAW,MAAM,SAAS,OAAQ;YAChC,MAAM,UAAU,MAAM,OAAO,CAAC,EAAE,EAAE,OAAO;YACzC,IAAI,SAAS;gBACX,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,SAAS,mBAAmB,QAAgB,EAAE,OAAuB;IAI1E,MAAM,SAAmB,EAAE;IAE3B,iCAAiC;IACjC,IAAI,SAAS,MAAM,GAAG,KAAK;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,uCAAuC;IACvC,MAAM,kBAAkB;IACxB,MAAM,YAAY,SAAS,KAAK,CAAC;IACjC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,OAAO,IAAI,CAAC;IACd;IAEA,6CAA6C;IAC7C,IAAI,WAAW;QACb,MAAM,iBAAiB,QAAQ,MAAM;QACrC,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,QAAQ,SAAS,SAAS,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI;YACrD,IAAI,QAAQ,gBAAgB;gBAC1B,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,MAAM,gCAAgC,CAAC;YAClE;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,sCAAsC;IACtC,IAAI,YAAY,SAAS,OAAO,CAAC,cAAc;IAE/C,wBAAwB;IACxB,YAAY,UAAU,OAAO,CAAC,QAAQ,KAAK,IAAI;IAE/C,iCAAiC;IACjC,YAAY,UAAU,OAAO,CAAC,YAAY;IAE1C,OAAO;AACT;AAEA,wBAAwB;AACxB,MAAM;IACI,SAAwB,KAAK;IAErC,aAAc;QACZ,MAAM,SAAS,QAAQ,GAAG,CAAC,cAAc;QACzC,IAAI,QAAQ;YACV,IAAI,CAAC,MAAM,GAAG,IAAI,wKAAA,CAAA,UAAM,CAAC;gBACvB;gBACA,SAAS,QAAQ,GAAG,CAAC,eAAe,IAAI;YAC1C;QACF;IACF;IAEA,MAAM,mBAAmB,KAAa,EAAE;QACtC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;QAEzB,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC;gBAC9D;gBACA,QAAQ;YACV;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT;IACF;AACF;AAEO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AdMesh/protocol/ai-search-engine/src/app/api/search/stream/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { searchWeb, fetchPageContent } from '@/lib/search';\nimport { generateStreamingAIResponse, admeshService } from '@/lib/ai';\n\nexport async function POST(request: NextRequest) {\n  const { query } = await request.json();\n\n  if (!query || typeof query !== 'string') {\n    return new Response('Query is required', { status: 400 });\n  }\n\n  const encoder = new TextEncoder();\n  \n  const stream = new ReadableStream({\n    async start(controller) {\n      try {\n        // Send initial status\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Searching the web...' })}\\n\\n`)\n        );\n\n        // Search the web\n        const searchResults = await searchWeb(query);\n\n        // Send search results\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({\n            type: 'sources',\n            data: searchResults.map((result, index) => ({\n              id: index + 1,\n              title: result.title,\n              url: result.url,\n              snippet: result.snippet,\n              favicon: result.favicon\n            }))\n          })}\\n\\n`)\n        );\n\n        // Get Admesh recommendations\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Getting smart recommendations...' })}\\n\\n`)\n        );\n\n        const admeshResponse = await admeshService.getRecommendations(query);\n\n        // Send recommendations\n        if (admeshResponse) {\n          controller.enqueue(\n            encoder.encode(`data: ${JSON.stringify({\n              type: 'recommendations',\n              data: admeshResponse\n            })}\\n\\n`)\n          );\n        }\n\n        // Update status\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Analyzing content...' })}\\n\\n`)\n        );\n\n        // Fetch content from top results\n        const resultsWithContent = await Promise.all(\n          searchResults.slice(0, 3).map(async (result) => {\n            const content = await fetchPageContent(result.url);\n            return { ...result, content };\n          })\n        );\n\n        // Update status\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({ type: 'status', message: 'Generating AI response...' })}\\n\\n`)\n        );\n\n        // Stream AI response using the improved utility\n        const streamGenerator = generateStreamingAIResponse({\n          query,\n          sources: resultsWithContent\n        });\n\n        for await (const content of streamGenerator) {\n          controller.enqueue(\n            encoder.encode(`data: ${JSON.stringify({ type: 'content', data: content })}\\n\\n`)\n          );\n        }\n\n        // Send completion signal\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({ type: 'complete' })}\\n\\n`)\n        );\n\n      } catch (error) {\n        console.error('Streaming error:', error);\n        controller.enqueue(\n          encoder.encode(`data: ${JSON.stringify({ type: 'error', message: 'An error occurred' })}\\n\\n`)\n        );\n      } finally {\n        controller.close();\n      }\n    }\n  });\n\n  return new Response(stream, {\n    headers: {\n      'Content-Type': 'text/event-stream',\n      'Cache-Control': 'no-cache',\n      'Connection': 'keep-alive',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;IAEpC,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO,IAAI,SAAS,qBAAqB;YAAE,QAAQ;QAAI;IACzD;IAEA,MAAM,UAAU,IAAI;IAEpB,MAAM,SAAS,IAAI,eAAe;QAChC,MAAM,OAAM,UAAU;YACpB,IAAI;gBACF,sBAAsB;gBACtB,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAU,SAAS;gBAAuB,GAAG,IAAI,CAAC;gBAGnG,iBAAiB;gBACjB,MAAM,gBAAgB,MAAM,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE;gBAEtC,sBAAsB;gBACtB,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBACrC,MAAM;oBACN,MAAM,cAAc,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;4BAC1C,IAAI,QAAQ;4BACZ,OAAO,OAAO,KAAK;4BACnB,KAAK,OAAO,GAAG;4BACf,SAAS,OAAO,OAAO;4BACvB,SAAS,OAAO,OAAO;wBACzB,CAAC;gBACH,GAAG,IAAI,CAAC;gBAGV,6BAA6B;gBAC7B,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAU,SAAS;gBAAmC,GAAG,IAAI,CAAC;gBAG/G,MAAM,iBAAiB,MAAM,kHAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC;gBAE9D,uBAAuB;gBACvB,IAAI,gBAAgB;oBAClB,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;wBACrC,MAAM;wBACN,MAAM;oBACR,GAAG,IAAI,CAAC;gBAEZ;gBAEA,gBAAgB;gBAChB,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAU,SAAS;gBAAuB,GAAG,IAAI,CAAC;gBAGnG,iCAAiC;gBACjC,MAAM,qBAAqB,MAAM,QAAQ,GAAG,CAC1C,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO;oBACnC,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,GAAG;oBACjD,OAAO;wBAAE,GAAG,MAAM;wBAAE;oBAAQ;gBAC9B;gBAGF,gBAAgB;gBAChB,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAU,SAAS;gBAA4B,GAAG,IAAI,CAAC;gBAGxG,gDAAgD;gBAChD,MAAM,kBAAkB,CAAA,GAAA,kHAAA,CAAA,8BAA2B,AAAD,EAAE;oBAClD;oBACA,SAAS;gBACX;gBAEA,WAAW,MAAM,WAAW,gBAAiB;oBAC3C,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;wBAAE,MAAM;wBAAW,MAAM;oBAAQ,GAAG,IAAI,CAAC;gBAEpF;gBAEA,yBAAyB;gBACzB,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAW,GAAG,IAAI,CAAC;YAGtE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,WAAW,OAAO,CAChB,QAAQ,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;oBAAE,MAAM;oBAAS,SAAS;gBAAoB,GAAG,IAAI,CAAC;YAEjG,SAAU;gBACR,WAAW,KAAK;YAClB;QACF;IACF;IAEA,OAAO,IAAI,SAAS,QAAQ;QAC1B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;YACjB,cAAc;QAChB;IACF;AACF", "debugId": null}}]}