module.exports = {

"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "hasA11yProp": (()=>hasA11yProp),
    "mergeClasses": (()=>mergeClasses),
    "toCamelCase": (()=>toCamelCase),
    "toKebabCase": (()=>toKebabCase),
    "toPascalCase": (()=>toPascalCase)
});
const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string)=>string.replace(/^([A-Z])|[\s-_]+(\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());
const toPascalCase = (string)=>{
    const camelCase = toCamelCase(string);
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = (...classes)=>classes.filter((className, index, array)=>{
        return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
    }).join(" ").trim();
const hasA11yProp = (props)=>{
    for(const prop in props){
        if (prop.startsWith("aria-") || prop === "role" || prop === "title") {
            return true;
        }
    }
};
;
 //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>defaultAttributes)
});
var defaultAttributes = {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: 2,
    strokeLinecap: "round",
    strokeLinejoin: "round"
};
;
 //# sourceMappingURL=defaultAttributes.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/Icon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>Icon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-ssr] (ecmascript)");
;
;
;
const Icon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ color = "currentColor", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = "", children, iconNode, ...rest }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        ref,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
        width: size,
        height: size,
        stroke: color,
        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeClasses"])("lucide", className),
        ...!children && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hasA11yProp"])(rest) && {
            "aria-hidden": "true"
        },
        ...rest
    }, [
        ...iconNode.map(([tag, attrs])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(tag, attrs)),
        ...Array.isArray(children) ? children : [
            children
        ]
    ]));
;
 //# sourceMappingURL=Icon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>createLucideIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/Icon.js [app-ssr] (ecmascript)");
;
;
;
const createLucideIcon = (iconName, iconNode)=>{
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            ref,
            iconNode,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mergeClasses"])(`lucide-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toKebabCase"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName))}`, `lucide-${iconName}`, className),
            ...props
        }));
    Component.displayName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName);
    return Component;
};
;
 //# sourceMappingURL=createLucideIcon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Search)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m21 21-4.34-4.34",
            key: "14j7rj"
        }
    ],
    [
        "circle",
        {
            cx: "11",
            cy: "11",
            r: "8",
            key: "4ej97u"
        }
    ]
];
const Search = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("search", __iconNode);
;
 //# sourceMappingURL=search.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Search": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>LoaderCircle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M21 12a9 9 0 1 1-6.219-8.56",
            key: "13zald"
        }
    ]
];
const LoaderCircle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("loader-circle", __iconNode);
;
 //# sourceMappingURL=loader-circle.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Loader2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ExternalLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M15 3h6v6",
            key: "1q9fwt"
        }
    ],
    [
        "path",
        {
            d: "M10 14 21 3",
            key: "gplh6r"
        }
    ],
    [
        "path",
        {
            d: "M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",
            key: "a6xqqp"
        }
    ]
];
const ExternalLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("external-link", __iconNode);
;
 //# sourceMappingURL=external-link.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-ssr] (ecmascript) <export default as ExternalLink>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ExternalLink": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Moon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",
            key: "a7tn18"
        }
    ]
];
const Moon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("moon", __iconNode);
;
 //# sourceMappingURL=moon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-ssr] (ecmascript) <export default as Moon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Moon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$moon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$moon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Sun)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "circle",
        {
            cx: "12",
            cy: "12",
            r: "4",
            key: "4exip2"
        }
    ],
    [
        "path",
        {
            d: "M12 2v2",
            key: "tus03m"
        }
    ],
    [
        "path",
        {
            d: "M12 20v2",
            key: "1lh1kg"
        }
    ],
    [
        "path",
        {
            d: "m4.93 4.93 1.41 1.41",
            key: "149t6j"
        }
    ],
    [
        "path",
        {
            d: "m17.66 17.66 1.41 1.41",
            key: "ptbguv"
        }
    ],
    [
        "path",
        {
            d: "M2 12h2",
            key: "1t8f8n"
        }
    ],
    [
        "path",
        {
            d: "M20 12h2",
            key: "1q8mjw"
        }
    ],
    [
        "path",
        {
            d: "m6.34 17.66-1.41 1.41",
            key: "1m8zz5"
        }
    ],
    [
        "path",
        {
            d: "m19.07 4.93-1.41 1.41",
            key: "1shlcs"
        }
    ]
];
const Sun = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("sun", __iconNode);
;
 //# sourceMappingURL=sun.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-ssr] (ecmascript) <export default as Sun>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Sun": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sun$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sun$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Clock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "circle",
        {
            cx: "12",
            cy: "12",
            r: "10",
            key: "1mglay"
        }
    ],
    [
        "polyline",
        {
            points: "12 6 12 12 16 14",
            key: "68esgv"
        }
    ]
];
const Clock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("clock", __iconNode);
;
 //# sourceMappingURL=clock.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Clock": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>X)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M18 6 6 18",
            key: "1bl5f8"
        }
    ],
    [
        "path",
        {
            d: "m6 6 12 12",
            key: "d8bk6v"
        }
    ]
];
const X = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("x", __iconNode);
;
 //# sourceMappingURL=x.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "X": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/admesh-ui-sdk/dist/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdMeshAutoRecommendationWidget": (()=>qe),
    "AdMeshBadge": (()=>Le),
    "AdMeshChatInput": (()=>Pe),
    "AdMeshChatInterface": (()=>Be),
    "AdMeshChatMessage": (()=>ze),
    "AdMeshCitationReference": (()=>xe),
    "AdMeshCitationUnit": (()=>Ie),
    "AdMeshCompareTable": (()=>ue),
    "AdMeshConversationSummary": (()=>Re),
    "AdMeshConversationalUnit": (()=>ge),
    "AdMeshFloatingChat": (()=>Ye),
    "AdMeshInlineRecommendation": (()=>V),
    "AdMeshLayout": (()=>Ee),
    "AdMeshLinkTracker": (()=>q),
    "AdMeshProductCard": (()=>ee),
    "AdMeshSidebar": (()=>He),
    "AdMeshSidebarContent": (()=>Oe),
    "AdMeshSidebarHeader": (()=>We),
    "AdMeshSimpleAd": (()=>Ue),
    "DEFAULT_CONFIG": (()=>Je),
    "VERSION": (()=>Ge),
    "buildAdMeshLink": (()=>Ve),
    "extractTrackingData": (()=>De),
    "setAdMeshTrackerConfig": (()=>Fe),
    "useAdMeshStyles": (()=>Te),
    "useAdMeshTracker": (()=>_e)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function fe(r) {
    return r && r.__esModule && Object.prototype.hasOwnProperty.call(r, "default") ? r.default : r;
}
var Z = {
    exports: {}
}, G = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var de;
function ye() {
    if (de) return G;
    de = 1;
    var r = Symbol.for("react.transitional.element"), t = Symbol.for("react.fragment");
    function s(d, n, a) {
        var i = null;
        if (a !== void 0 && (i = "" + a), n.key !== void 0 && (i = "" + n.key), "key" in n) {
            a = {};
            for(var m in n)m !== "key" && (a[m] = n[m]);
        } else a = n;
        return n = a.ref, {
            $$typeof: r,
            type: d,
            key: i,
            ref: n !== void 0 ? n : null,
            props: a
        };
    }
    return G.Fragment = t, G.jsx = s, G.jsxs = s, G;
}
var J = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var ce;
function ve() {
    return ce || (ce = 1, ("TURBOPACK compile-time value", "development") !== "production" && function() {
        function r(o) {
            if (o == null) return null;
            if (typeof o == "function") return o.$$typeof === $ ? null : o.displayName || o.name || null;
            if (typeof o == "string") return o;
            switch(o){
                case y:
                    return "Fragment";
                case b:
                    return "Profiler";
                case f:
                    return "StrictMode";
                case _:
                    return "Suspense";
                case T:
                    return "SuspenseList";
                case R:
                    return "Activity";
            }
            if (typeof o == "object") switch(typeof o.tag == "number" && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), o.$$typeof){
                case u:
                    return "Portal";
                case j:
                    return (o.displayName || "Context") + ".Provider";
                case N:
                    return (o._context.displayName || "Context") + ".Consumer";
                case A:
                    var v = o.render;
                    return o = o.displayName, o || (o = v.displayName || v.name || "", o = o !== "" ? "ForwardRef(" + o + ")" : "ForwardRef"), o;
                case k:
                    return v = o.displayName || null, v !== null ? v : r(o.type) || "Memo";
                case M:
                    v = o._payload, o = o._init;
                    try {
                        return r(o(v));
                    } catch  {}
            }
            return null;
        }
        function t(o) {
            return "" + o;
        }
        function s(o) {
            try {
                t(o);
                var v = !1;
            } catch  {
                v = !0;
            }
            if (v) {
                v = console;
                var L = v.error, S = typeof Symbol == "function" && Symbol.toStringTag && o[Symbol.toStringTag] || o.constructor.name || "Object";
                return L.call(v, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", S), t(o);
            }
        }
        function d(o) {
            if (o === y) return "<>";
            if (typeof o == "object" && o !== null && o.$$typeof === M) return "<...>";
            try {
                var v = r(o);
                return v ? "<" + v + ">" : "<...>";
            } catch  {
                return "<...>";
            }
        }
        function n() {
            var o = B.A;
            return o === null ? null : o.getOwner();
        }
        function a() {
            return Error("react-stack-top-frame");
        }
        function i(o) {
            if (Q.call(o, "key")) {
                var v = Object.getOwnPropertyDescriptor(o, "key").get;
                if (v && v.isReactWarning) return !1;
            }
            return o.key !== void 0;
        }
        function m(o, v) {
            function L() {
                W || (W = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", v));
            }
            L.isReactWarning = !0, Object.defineProperty(o, "key", {
                get: L,
                configurable: !0
            });
        }
        function g() {
            var o = r(this.type);
            return F[o] || (F[o] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")), o = this.props.ref, o !== void 0 ? o : null;
        }
        function x(o, v, L, S, D, O, te, se) {
            return L = O.ref, o = {
                $$typeof: p,
                type: o,
                key: v,
                props: O,
                _owner: D
            }, (L !== void 0 ? L : null) !== null ? Object.defineProperty(o, "ref", {
                enumerable: !1,
                get: g
            }) : Object.defineProperty(o, "ref", {
                enumerable: !1,
                value: null
            }), o._store = {}, Object.defineProperty(o._store, "validated", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: 0
            }), Object.defineProperty(o, "_debugInfo", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: null
            }), Object.defineProperty(o, "_debugStack", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: te
            }), Object.defineProperty(o, "_debugTask", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: se
            }), Object.freeze && (Object.freeze(o.props), Object.freeze(o)), o;
        }
        function l(o, v, L, S, D, O, te, se) {
            var E = v.children;
            if (E !== void 0) if (S) if (re(E)) {
                for(S = 0; S < E.length; S++)h(E[S]);
                Object.freeze && Object.freeze(E);
            } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else h(E);
            if (Q.call(v, "key")) {
                E = r(o);
                var Y = Object.keys(v).filter(function(pe) {
                    return pe !== "key";
                });
                S = 0 < Y.length ? "{key: someKey, " + Y.join(": ..., ") + ": ...}" : "{key: someKey}", ie[E + S] || (Y = 0 < Y.length ? "{" + Y.join(": ..., ") + ": ...}" : "{}", console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`, S, E, Y, E), ie[E + S] = !0);
            }
            if (E = null, L !== void 0 && (s(L), E = "" + L), i(v) && (s(v.key), E = "" + v.key), "key" in v) {
                L = {};
                for(var ae in v)ae !== "key" && (L[ae] = v[ae]);
            } else L = v;
            return E && m(L, typeof o == "function" ? o.displayName || o.name || "Unknown" : o), x(o, E, O, D, n(), L, te, se);
        }
        function h(o) {
            typeof o == "object" && o !== null && o.$$typeof === p && o._store && (o._store.validated = 1);
        }
        var c = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], p = Symbol.for("react.transitional.element"), u = Symbol.for("react.portal"), y = Symbol.for("react.fragment"), f = Symbol.for("react.strict_mode"), b = Symbol.for("react.profiler"), N = Symbol.for("react.consumer"), j = Symbol.for("react.context"), A = Symbol.for("react.forward_ref"), _ = Symbol.for("react.suspense"), T = Symbol.for("react.suspense_list"), k = Symbol.for("react.memo"), M = Symbol.for("react.lazy"), R = Symbol.for("react.activity"), $ = Symbol.for("react.client.reference"), B = c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, Q = Object.prototype.hasOwnProperty, re = Array.isArray, I = console.createTask ? console.createTask : function() {
            return null;
        };
        c = {
            "react-stack-bottom-frame": function(o) {
                return o();
            }
        };
        var W, F = {}, z = c["react-stack-bottom-frame"].bind(c, a)(), X = I(d(a)), ie = {};
        J.Fragment = y, J.jsx = function(o, v, L, S, D) {
            var O = 1e4 > B.recentlyCreatedOwnerStacks++;
            return l(o, v, L, !1, S, D, O ? Error("react-stack-top-frame") : z, O ? I(d(o)) : X);
        }, J.jsxs = function(o, v, L, S, D) {
            var O = 1e4 > B.recentlyCreatedOwnerStacks++;
            return l(o, v, L, !0, S, D, O ? Error("react-stack-top-frame") : z, O ? I(d(o)) : X);
        };
    }()), J;
}
var me;
function ke() {
    return me || (me = 1, ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : Z.exports = ve()), Z.exports;
}
var e = ke(), ne = {
    exports: {}
};
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/ var he;
function we() {
    return he || (he = 1, function(r) {
        (function() {
            var t = {}.hasOwnProperty;
            function s() {
                for(var a = "", i = 0; i < arguments.length; i++){
                    var m = arguments[i];
                    m && (a = n(a, d(m)));
                }
                return a;
            }
            function d(a) {
                if (typeof a == "string" || typeof a == "number") return a;
                if (typeof a != "object") return "";
                if (Array.isArray(a)) return s.apply(null, a);
                if (a.toString !== Object.prototype.toString && !a.toString.toString().includes("[native code]")) return a.toString();
                var i = "";
                for(var m in a)t.call(a, m) && a[m] && (i = n(i, m));
                return i;
            }
            function n(a, i) {
                return i ? a ? a + " " + i : a + i : a;
            }
            r.exports ? (s.default = s, r.exports = s) : window.classNames = s;
        })();
    }(ne)), ne.exports;
}
var je = we();
const w = /* @__PURE__ */ fe(je), Ne = "https://api.useadmesh.com/track";
let oe = {
    apiBaseUrl: Ne,
    enabled: !0,
    debug: !1,
    retryAttempts: 3,
    retryDelay: 1e3
};
const Fe = (r)=>{
    oe = {
        ...oe,
        ...r
    };
}, _e = (r)=>{
    const [t, s] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), [d, n] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            ...oe,
            ...r
        }), [
        r
    ]), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((h, c)=>{
        a.debug && console.log(`[AdMesh Tracker] ${h}`, c);
    }, [
        a.debug
    ]), m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (h, c)=>{
        if (!a.enabled) {
            i("Tracking disabled, skipping event", {
                eventType: h,
                data: c
            });
            return;
        }
        if (!c.adId || !c.admeshLink) {
            const f = "Missing required tracking data: adId and admeshLink are required";
            i(f, c), n(f);
            return;
        }
        s(!0), n(null);
        const p = {
            event_type: h,
            ad_id: c.adId,
            admesh_link: c.admeshLink,
            product_id: c.productId,
            user_id: c.userId,
            session_id: c.sessionId,
            revenue: c.revenue,
            conversion_type: c.conversionType,
            metadata: c.metadata,
            timestamp: /* @__PURE__ */ new Date().toISOString(),
            user_agent: navigator.userAgent,
            referrer: document.referrer,
            page_url: window.location.href
        };
        i(`Sending ${h} event`, p);
        let u = null;
        for(let f = 1; f <= (a.retryAttempts || 3); f++)try {
            const b = await fetch(`${a.apiBaseUrl}/events`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(p)
            });
            if (!b.ok) throw new Error(`HTTP ${b.status}: ${b.statusText}`);
            const N = await b.json();
            i(`${h} event tracked successfully`, N), s(!1);
            return;
        } catch (b) {
            u = b, i(`Attempt ${f} failed for ${h} event`, b), f < (a.retryAttempts || 3) && await new Promise((N)=>setTimeout(N, (a.retryDelay || 1e3) * f));
        }
        const y = `Failed to track ${h} event after ${a.retryAttempts} attempts: ${u == null ? void 0 : u.message}`;
        i(y, u), n(y), s(!1);
    }, [
        a,
        i
    ]), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (h)=>m("click", h), [
        m
    ]), x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (h)=>m("view", h), [
        m
    ]), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (h)=>(!h.revenue && !h.conversionType && i("Warning: Conversion tracking without revenue or conversion type", h), m("conversion", h)), [
        m,
        i
    ]);
    return {
        trackClick: g,
        trackView: x,
        trackConversion: l,
        isTracking: t,
        error: d
    };
}, Ve = (r, t, s)=>{
    try {
        const d = new URL(r);
        return d.searchParams.set("ad_id", t), d.searchParams.set("utm_source", "admesh"), d.searchParams.set("utm_medium", "recommendation"), s && Object.entries(s).forEach(([n, a])=>{
            d.searchParams.set(n, a);
        }), d.toString();
    } catch (d) {
        return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:", r, d), r;
    }
}, De = (r, t)=>({
        adId: r.ad_id,
        admeshLink: r.admesh_link,
        productId: r.product_id,
        ...t
    }), q = ({ adId: r, admeshLink: t, productId: s, children: d, onClick: n, trackingData: a, className: i })=>{
    const { trackClick: m, trackView: g } = _e(), x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!x.current || l.current) return;
        const c = new IntersectionObserver((p)=>{
            p.forEach((u)=>{
                u.isIntersecting && !l.current && (l.current = !0, g({
                    adId: r,
                    admeshLink: t,
                    productId: s,
                    ...a
                }).catch(console.error));
            });
        }, {
            threshold: 0.5,
            // Track when 50% of the element is visible
            rootMargin: "0px"
        });
        return c.observe(x.current), ()=>{
            c.disconnect();
        };
    }, [
        r,
        t,
        s,
        a,
        g
    ]);
    const h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (c)=>{
        try {
            await m({
                adId: r,
                admeshLink: t,
                productId: s,
                ...a
            });
        } catch (y) {
            console.error("Failed to track click:", y);
        }
        n && n(), c.target.closest("a") || window.open(t, "_blank", "noopener,noreferrer");
    }, [
        r,
        t,
        s,
        a,
        m,
        n
    ]);
    return /* @__PURE__ */ e.jsx("div", {
        ref: x,
        className: i,
        onClick: h,
        style: {
            cursor: "pointer"
        },
        children: d
    });
};
q.displayName = "AdMeshLinkTracker";
const ee = ({ recommendation: r, theme: t, showMatchScore: s = !0, showBadges: d = !0, onClick: n, className: a })=>{
    const i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        var p;
        const l = [];
        r.intent_match_score >= 0.8 && l.push("Top Match"), r.has_free_tier && l.push("Free Tier"), r.trial_days && r.trial_days > 0 && l.push("Trial Available");
        const h = [
            "ai",
            "artificial intelligence",
            "machine learning",
            "ml",
            "automation"
        ];
        return (((p = r.keywords) == null ? void 0 : p.some((u)=>h.some((y)=>u.toLowerCase().includes(y)))) || r.title.toLowerCase().includes("ai")) && l.push("AI Powered"), l;
    }, [
        r
    ]), m = Math.round(r.intent_match_score * 100), g = w("admesh-component", "admesh-card", "relative p-3 sm:p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow cursor-pointer", a), x = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor,
        "--admesh-primary-hover": t.accentColor + "dd"
    } : void 0;
    return /* @__PURE__ */ e.jsx(q, {
        adId: r.ad_id,
        admeshLink: r.admesh_link,
        productId: r.product_id,
        onClick: ()=>n == null ? void 0 : n(r.ad_id, r.admesh_link),
        trackingData: {
            title: r.title,
            matchScore: r.intent_match_score
        },
        className: g,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "h-full flex flex-col",
            style: x,
            "data-admesh-theme": t == null ? void 0 : t.mode,
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-2",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0",
                            children: [
                                d && i.includes("Top Match") && /* @__PURE__ */ e.jsx("span", {
                                    className: "text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full w-fit",
                                    children: "Top Match"
                                }),
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate",
                                    children: r.title
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "flex gap-2 flex-shrink-0",
                            children: /* @__PURE__ */ e.jsxs("button", {
                                className: "text-xs sm:text-sm px-2 py-1 rounded-full bg-black text-white hover:bg-gray-800 flex items-center",
                                children: [
                                    "Visit",
                                    /* @__PURE__ */ e.jsx("svg", {
                                        className: "ml-1 h-3 w-3",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                        })
                                    })
                                ]
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("p", {
                    className: "text-sm text-gray-700 dark:text-gray-300 mb-3",
                    children: r.reason
                }),
                s && typeof r.intent_match_score == "number" && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-3",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-between text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1",
                            children: [
                                /* @__PURE__ */ e.jsx("span", {
                                    className: "font-medium",
                                    children: "Match Score"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "font-semibold text-gray-700 dark:text-gray-300 whitespace-nowrap",
                                    children: [
                                        m,
                                        "% match"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden",
                            children: /* @__PURE__ */ e.jsx("div", {
                                className: "bg-black h-1.5 transition-all duration-300 ease-out",
                                style: {
                                    width: `${m}%`
                                }
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-wrap gap-2 text-xs mb-2",
                    children: [
                        r.pricing && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center text-gray-600 dark:text-gray-400",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                    })
                                }),
                                r.pricing
                            ]
                        }),
                        r.has_free_tier && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                                    })
                                }),
                                "Free Tier"
                            ]
                        }),
                        r.trial_days && r.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center text-gray-600 dark:text-gray-400",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"
                                    })
                                }),
                                r.trial_days,
                                "-day trial"
                            ]
                        })
                    ]
                }),
                r.features && r.features.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-2",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-500 dark:text-gray-400 mb-1",
                            children: "Features:"
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "flex flex-wrap gap-1.5",
                            children: r.features.map((l, h)=>/* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300",
                                    children: [
                                        /* @__PURE__ */ e.jsx("svg", {
                                            className: "h-3 w-3 mr-0.5 inline text-gray-500",
                                            fill: "none",
                                            stroke: "currentColor",
                                            viewBox: "0 0 24 24",
                                            children: /* @__PURE__ */ e.jsx("path", {
                                                strokeLinecap: "round",
                                                strokeLinejoin: "round",
                                                strokeWidth: 2,
                                                d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                            })
                                        }),
                                        l
                                    ]
                                }, h))
                        })
                    ]
                }),
                r.integrations && r.integrations.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-2",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-500 dark:text-gray-400 mb-1",
                            children: "Integrates with:"
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "flex flex-wrap gap-1.5",
                            children: r.integrations.map((l, h)=>/* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300",
                                    children: [
                                        /* @__PURE__ */ e.jsx("svg", {
                                            className: "h-3 w-3 mr-0.5 inline",
                                            fill: "none",
                                            stroke: "currentColor",
                                            viewBox: "0 0 24 24",
                                            children: /* @__PURE__ */ e.jsx("path", {
                                                strokeLinecap: "round",
                                                strokeLinejoin: "round",
                                                strokeWidth: 2,
                                                d: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                            })
                                        }),
                                        l
                                    ]
                                }, h))
                        })
                    ]
                }),
                r.reviews_summary && /* @__PURE__ */ e.jsx("div", {
                    className: "text-xs text-gray-600 dark:text-gray-400 mt-2",
                    children: r.reviews_summary
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex justify-end mt-auto pt-2",
                    children: /* @__PURE__ */ e.jsx("span", {
                        className: "text-xs text-gray-400 dark:text-gray-500",
                        children: "Powered by AdMesh"
                    })
                })
            ]
        })
    });
};
ee.displayName = "AdMeshProductCard";
const ue = ({ recommendations: r, theme: t, maxProducts: s = 3, showMatchScores: d = !0, showFeatures: n = !0, onProductClick: a, className: i })=>{
    const m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>r.slice(0, s), [
        r,
        s
    ]), g = w("admesh-component", "admesh-compare-layout", i), x = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return m.length === 0 ? /* @__PURE__ */ e.jsx("div", {
        className: g,
        children: /* @__PURE__ */ e.jsx("div", {
            className: "p-8 text-center text-gray-500 dark:text-gray-400",
            children: /* @__PURE__ */ e.jsx("p", {
                children: "No products to compare"
            })
        })
    }) : /* @__PURE__ */ e.jsx("div", {
        className: g,
        style: x,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "space-y-6",
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    className: "text-center",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-center gap-2 mb-2",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-5 h-5 text-gray-600 dark:text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    })
                                }),
                                /* @__PURE__ */ e.jsx("h3", {
                                    className: "text-lg font-semibold text-gray-800 dark:text-gray-200",
                                    children: "Smart Comparison"
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsxs("p", {
                            className: "text-sm text-gray-600 dark:text-gray-400",
                            children: [
                                m.length,
                                " intelligent matches found"
                            ]
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                    children: m.map((l, h)=>/* @__PURE__ */ e.jsxs(q, {
                            adId: l.ad_id,
                            admeshLink: l.admesh_link,
                            productId: l.product_id,
                            onClick: ()=>a == null ? void 0 : a(l.ad_id, l.admesh_link),
                            className: "relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow",
                            children: [
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex justify-between items-start mb-3",
                                    children: [
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                h === 0 && /* @__PURE__ */ e.jsx("span", {
                                                    className: "text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full",
                                                    children: "Top Match"
                                                }),
                                                /* @__PURE__ */ e.jsxs("span", {
                                                    className: "text-xs text-gray-400 dark:text-gray-500",
                                                    children: [
                                                        "#",
                                                        h + 1
                                                    ]
                                                })
                                            ]
                                        }),
                                        d && /* @__PURE__ */ e.jsxs("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap",
                                            children: [
                                                Math.round(l.intent_match_score * 100),
                                                "% match"
                                            ]
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "font-semibold text-gray-800 dark:text-gray-200 mb-2",
                                    children: l.title
                                }),
                                d && /* @__PURE__ */ e.jsxs("div", {
                                    className: "mb-3",
                                    children: [
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1",
                                            children: [
                                                /* @__PURE__ */ e.jsx("span", {
                                                    children: "Match Score"
                                                }),
                                                /* @__PURE__ */ e.jsxs("span", {
                                                    className: "whitespace-nowrap",
                                                    children: [
                                                        Math.round(l.intent_match_score * 100),
                                                        "% match"
                                                    ]
                                                })
                                            ]
                                        }),
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden",
                                            children: /* @__PURE__ */ e.jsx("div", {
                                                className: "bg-black h-1.5",
                                                style: {
                                                    width: `${Math.round(l.intent_match_score * 100)}%`
                                                }
                                            })
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex flex-wrap gap-2 text-xs mb-3",
                                    children: [
                                        l.pricing && /* @__PURE__ */ e.jsxs("span", {
                                            className: "flex items-center text-gray-600 dark:text-gray-400",
                                            children: [
                                                /* @__PURE__ */ e.jsx("svg", {
                                                    className: "h-3 w-3 mr-1",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /* @__PURE__ */ e.jsx("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                                    })
                                                }),
                                                l.pricing
                                            ]
                                        }),
                                        l.has_free_tier && /* @__PURE__ */ e.jsxs("span", {
                                            className: "flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full",
                                            children: [
                                                /* @__PURE__ */ e.jsx("svg", {
                                                    className: "h-3 w-3 mr-1",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /* @__PURE__ */ e.jsx("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                                                    })
                                                }),
                                                "Free Tier"
                                            ]
                                        }),
                                        l.trial_days && l.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", {
                                            className: "flex items-center text-gray-600 dark:text-gray-400",
                                            children: [
                                                /* @__PURE__ */ e.jsx("svg", {
                                                    className: "h-3 w-3 mr-1",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /* @__PURE__ */ e.jsx("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"
                                                    })
                                                }),
                                                l.trial_days,
                                                "-day trial"
                                            ]
                                        })
                                    ]
                                }),
                                n && l.features && l.features.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                                    className: "mb-3",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 mb-1",
                                            children: "Key Features:"
                                        }),
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "flex flex-wrap gap-1.5",
                                            children: [
                                                l.features.slice(0, 4).map((c, p)=>/* @__PURE__ */ e.jsxs("span", {
                                                        className: "text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300",
                                                        children: [
                                                            /* @__PURE__ */ e.jsx("svg", {
                                                                className: "h-3 w-3 mr-0.5 inline text-gray-500",
                                                                fill: "none",
                                                                stroke: "currentColor",
                                                                viewBox: "0 0 24 24",
                                                                children: /* @__PURE__ */ e.jsx("path", {
                                                                    strokeLinecap: "round",
                                                                    strokeLinejoin: "round",
                                                                    strokeWidth: 2,
                                                                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                                })
                                                            }),
                                                            c
                                                        ]
                                                    }, p)),
                                                (l.features.length || 0) > 4 && /* @__PURE__ */ e.jsxs("span", {
                                                    className: "text-xs text-gray-500 dark:text-gray-400 italic",
                                                    children: [
                                                        "+",
                                                        l.features.length - 4,
                                                        " more"
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsxs("button", {
                                    className: "w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto",
                                    children: [
                                        "Visit Offer",
                                        /* @__PURE__ */ e.jsx("svg", {
                                            className: "h-3 w-3",
                                            fill: "none",
                                            stroke: "currentColor",
                                            viewBox: "0 0 24 24",
                                            children: /* @__PURE__ */ e.jsx("path", {
                                                strokeLinecap: "round",
                                                strokeLinejoin: "round",
                                                strokeWidth: 2,
                                                d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                            })
                                        })
                                    ]
                                })
                            ]
                        }, l.product_id || h))
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50",
                    children: /* @__PURE__ */ e.jsxs("span", {
                        className: "flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-3 h-3 text-indigo-500",
                                fill: "currentColor",
                                viewBox: "0 0 20 20",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    fillRule: "evenodd",
                                    d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",
                                    clipRule: "evenodd"
                                })
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                className: "font-medium",
                                children: "Powered by"
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                className: "font-semibold text-indigo-600 dark:text-indigo-400",
                                children: "AdMesh"
                            })
                        ]
                    })
                })
            ]
        })
    });
};
ue.displayName = "AdMeshCompareTable";
const Ce = {
    "Top Match": "primary",
    "Free Tier": "success",
    "AI Powered": "secondary",
    Popular: "warning",
    New: "primary",
    "Trial Available": "success"
}, Me = {
    "Top Match": "★",
    "Free Tier": "◆",
    "AI Powered": "◉",
    Popular: "▲",
    New: "●",
    "Trial Available": "◈"
}, Le = ({ type: r, variant: t, size: s = "md", className: d })=>{
    const n = t || Ce[r] || "secondary", a = Me[r], i = w("admesh-component", "admesh-badge", `admesh-badge--${n}`, `admesh-badge--${s}`, d);
    return /* @__PURE__ */ e.jsxs("span", {
        className: i,
        children: [
            a && /* @__PURE__ */ e.jsx("span", {
                className: "admesh-badge__icon",
                children: a
            }),
            /* @__PURE__ */ e.jsx("span", {
                className: "admesh-badge__text",
                children: r
            })
        ]
    });
};
Le.displayName = "AdMeshBadge";
const Ae = `
/* AdMesh UI SDK Scoped Styles - Smart Recommendations Design */
.admesh-component {
  --admesh-primary: #6366f1;
  --admesh-primary-hover: #4f46e5;
  --admesh-secondary: #8b5cf6;
  --admesh-accent: #06b6d4;
  --admesh-background: #ffffff;
  --admesh-surface: #ffffff;
  --admesh-border: #e2e8f0;
  --admesh-text: #0f172a;
  --admesh-text-muted: #64748b;
  --admesh-text-light: #94a3b8;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --admesh-radius: 0.75rem;
  --admesh-radius-sm: 0.375rem;
  --admesh-radius-lg: 1rem;
  --admesh-radius-xl: 1.5rem;
}

.admesh-component[data-admesh-theme="dark"] {
  --admesh-background: #111827;
  --admesh-surface: #1f2937;
  --admesh-border: #374151;
  --admesh-text: #f9fafb;
  --admesh-text-muted: #9ca3af;
  --admesh-text-light: #6b7280;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Layout Styles */
.admesh-layout {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: var(--admesh-text);
  background-color: var(--admesh-background);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  box-shadow: var(--admesh-shadow);
  border: 1px solid var(--admesh-border);
}

.admesh-layout__header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.admesh-layout__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
}

.admesh-layout__subtitle {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

.admesh-layout__cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admesh-layout__more-indicator {
  text-align: center;
  padding: 1rem;
  color: var(--admesh-text-muted);
  font-size: 0.875rem;
}

.admesh-layout__empty {
  text-align: center;
  padding: 3rem 1rem;
}

.admesh-layout__empty-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text-muted);
  margin-bottom: 0.5rem;
}

.admesh-layout__empty-content p {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

/* Product Card Styles */
.admesh-product-card {
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.admesh-product-card:hover {
  box-shadow: var(--admesh-shadow-lg);
  transform: translateY(-2px);
  border-color: var(--admesh-primary);
}

.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.admesh-product-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.admesh-product-card__reason {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.admesh-product-card__match-score {
  margin-bottom: 1rem;
}

.admesh-product-card__match-score-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--admesh-text-muted);
  margin-bottom: 0.25rem;
}

.admesh-product-card__match-score-bar {
  width: 100%;
  height: 0.375rem;
  background-color: var(--admesh-border);
  border-radius: var(--admesh-radius-sm);
  overflow: hidden;
}

.admesh-product-card__match-score-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);
  border-radius: var(--admesh-radius-sm);
  transition: width 0.3s ease-in-out;
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admesh-product-card__badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--admesh-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--admesh-radius-sm);
}

.admesh-product-card__badge--secondary {
  background-color: var(--admesh-secondary);
}

.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.admesh-product-card__keyword {
  padding: 0.125rem 0.375rem;
  background-color: var(--admesh-border);
  color: var(--admesh-text-muted);
  font-size: 0.75rem;
  border-radius: var(--admesh-radius-sm);
}

/* Dark mode specific enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-product-card__keyword {
  background-color: #4b5563;
  color: #d1d5db;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card:hover {
  border-color: var(--admesh-primary);
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card__button:hover {
  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));
}

.admesh-product-card__footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

/* Mobile-specific sidebar improvements */
@media (max-width: 640px) {
  .admesh-sidebar {
    /* Ensure proper mobile viewport handling */
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */
    max-height: 100vh !important;
    max-height: 100dvh !important;
    width: 100vw !important;
    max-width: 90vw !important;
    overflow: hidden !important;
  }

  .admesh-sidebar.relative {
    height: 100% !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Improve touch scrolling */
  .admesh-sidebar .overflow-y-auto {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scroll-behavior: smooth !important;
  }

  /* Prevent body scroll when sidebar is open */
  body:has(.admesh-sidebar[data-mobile-open="true"]) {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }
}

/* Tablet improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .admesh-sidebar {
    max-width: 400px !important;
  }
}

/* Mobile responsiveness improvements for all components */
@media (max-width: 640px) {
  /* Product cards mobile optimization */
  .admesh-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Inline recommendations mobile optimization */
  .admesh-inline-recommendation {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Conversation summary mobile optimization */
  .admesh-conversation-summary {
    padding: 1rem !important;
  }

  /* Percentage text mobile improvements */
  .admesh-component .text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
  }

  .admesh-component .text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }

  /* Button mobile improvements */
  .admesh-component button {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
    min-height: 2rem !important;
    touch-action: manipulation !important;
  }

  /* Badge mobile improvements */
  .admesh-component .rounded-full {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.625rem !important;
    line-height: 1rem !important;
  }

  /* Progress bar mobile improvements */
  .admesh-component .bg-gray-200,
  .admesh-component .bg-slate-600 {
    height: 0.25rem !important;
  }

  /* Flex layout mobile improvements */
  .admesh-component .flex {
    flex-wrap: wrap !important;
  }

  .admesh-component .gap-2 {
    gap: 0.375rem !important;
  }

  .admesh-component .gap-3 {
    gap: 0.5rem !important;
  }
}

.admesh-product-card__button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: var(--admesh-radius);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.admesh-product-card__button:hover {
  transform: translateY(-1px);
  box-shadow: var(--admesh-shadow-lg);
}

/* Utility Classes */
.admesh-text-xs { font-size: 0.75rem; }
.admesh-text-sm { font-size: 0.875rem; }
.admesh-text-base { font-size: 1rem; }
.admesh-text-lg { font-size: 1.125rem; }
.admesh-text-xl { font-size: 1.25rem; }

.admesh-font-medium { font-weight: 500; }
.admesh-font-semibold { font-weight: 600; }
.admesh-font-bold { font-weight: 700; }

.admesh-text-muted { color: var(--admesh-text-muted); }

/* Comparison Table Styles */
.admesh-compare-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  overflow: hidden;
}

.admesh-compare-table th,
.admesh-compare-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admesh-border);
}

.admesh-compare-table th {
  background-color: var(--admesh-background);
  font-weight: 600;
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table td {
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table tr:hover {
  background-color: var(--admesh-border);
}

/* Dark mode table enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-compare-table th {
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-compare-table tr:hover {
  background-color: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admesh-layout {
    padding: 1rem;
  }

  .admesh-layout__cards-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admesh-product-card {
    padding: 1rem;
  }

  .admesh-compare-table {
    font-size: 0.75rem;
  }

  .admesh-compare-table th,
  .admesh-compare-table td {
    padding: 0.5rem;
  }
}
`;
let le = !1;
const Te = ()=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (le) return;
        const r = document.createElement("style");
        return r.id = "admesh-ui-sdk-styles", r.textContent = Ae, document.getElementById("admesh-ui-sdk-styles") || (document.head.appendChild(r), le = !0), ()=>{
            const t = document.getElementById("admesh-ui-sdk-styles");
            t && document.head.contains(t) && (document.head.removeChild(t), le = !1);
        };
    }, []);
}, Se = (r, t, s)=>{
    if (!s && t) switch(t){
        case "compare_products":
            return "compare";
        case "best_for_use_case":
        case "trial_demo":
        case "budget_conscious":
            return "cards";
        default:
            return "cards";
    }
    const d = r.length;
    if (d >= 2 && d <= 4) {
        const n = r.some((i)=>i.features && i.features.length > 0), a = r.some((i)=>i.pricing);
        if (n || a) return "compare";
    }
    return "cards";
}, Ee = ({ recommendations: r, intentType: t, theme: s, maxDisplayed: d = 6, showMatchScores: n = !0, showFeatures: a = !0, autoLayout: i = !0, onProductClick: m, onTrackView: g, className: x })=>{
    Te();
    const l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>r.slice(0, d), [
        r,
        d
    ]), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>Se(l, t, i), [
        l,
        t,
        i
    ]), c = w("admesh-component", x), p = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return l.length === 0 ? /* @__PURE__ */ e.jsx("div", {
        className: c,
        children: /* @__PURE__ */ e.jsx("div", {
            className: "admesh-layout__empty",
            children: /* @__PURE__ */ e.jsxs("div", {
                className: "admesh-layout__empty-content",
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: "flex items-center justify-center mb-3",
                        children: /* @__PURE__ */ e.jsx("svg", {
                            className: "w-8 h-8 text-indigo-500",
                            fill: "currentColor",
                            viewBox: "0 0 20 20",
                            children: /* @__PURE__ */ e.jsx("path", {
                                fillRule: "evenodd",
                                d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",
                                clipRule: "evenodd"
                            })
                        })
                    }),
                    /* @__PURE__ */ e.jsx("h3", {
                        className: "admesh-text-lg admesh-font-semibold admesh-text-muted",
                        children: "No smart recommendations found"
                    }),
                    /* @__PURE__ */ e.jsx("p", {
                        className: "admesh-text-sm admesh-text-muted",
                        children: "Try refining your search or check back later for new matches."
                    })
                ]
            })
        })
    }) : /* @__PURE__ */ e.jsx("div", {
        className: c,
        style: p,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: h === "compare" ? /* @__PURE__ */ e.jsx(ue, {
            recommendations: l,
            theme: s,
            maxProducts: Math.min(l.length, 4),
            showMatchScores: n,
            showFeatures: a,
            onProductClick: m
        }) : /* @__PURE__ */ e.jsx("div", {
            className: "space-y-4",
            children: l.map((u, y)=>/* @__PURE__ */ e.jsx(ee, {
                    recommendation: u,
                    theme: s,
                    showMatchScore: n,
                    showBadges: !0,
                    maxKeywords: 3,
                    onClick: m,
                    onTrackView: g
                }, u.product_id || u.ad_id || y))
        })
    });
};
Ee.displayName = "AdMeshLayout";
const Ue = ({ recommendation: r, theme: t = {
    mode: "light"
}, className: s = "", onClick: d, showPoweredBy: n = !0, variation: a = "question" })=>{
    const i = ()=>{
        d == null || d(r.ad_id, r.admesh_link);
    }, m = r.title, g = ()=>{
        var x, l, h;
        if (a === "question") return `Looking for ${((l = (x = r.keywords) == null ? void 0 : x[0]) == null ? void 0 : l.toLowerCase()) || "solutions"} for your business? Try ${m}`;
        {
            const c = ((h = r.reason) == null ? void 0 : h.split(".")[0]) || "offering best solutions for your business";
            return `${m} is ${c.toLowerCase()}, visit`;
        }
    };
    return /* @__PURE__ */ e.jsx(q, {
        adId: r.ad_id,
        admeshLink: r.admesh_link,
        productId: r.product_id,
        onClick: i,
        trackingData: {
            title: r.title,
            variation: a,
            component: "simple_ad"
        },
        className: `admesh-simple-ad ${s}`,
        children: /* @__PURE__ */ e.jsxs("div", {
            "data-admesh-theme": t.mode,
            style: {
                padding: "12px 16px",
                borderRadius: "8px",
                border: `1px solid ${t.mode === "dark" ? "#374151" : "#e5e7eb"}`,
                backgroundColor: t.mode === "dark" ? "#1f2937" : "#ffffff",
                fontSize: "14px",
                lineHeight: "1.5",
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            },
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    style: {
                        marginBottom: n ? "8px" : "0"
                    },
                    children: [
                        /* @__PURE__ */ e.jsx("span", {
                            style: {
                                color: t.mode === "dark" ? "#f3f4f6" : "#374151",
                                marginRight: "4px"
                            },
                            children: g()
                        }),
                        /* @__PURE__ */ e.jsx("span", {
                            style: {
                                color: t.accentColor || "#2563eb",
                                textDecoration: "underline",
                                cursor: "pointer",
                                fontSize: "inherit",
                                fontFamily: "inherit"
                            },
                            children: m
                        })
                    ]
                }),
                n && /* @__PURE__ */ e.jsxs("div", {
                    style: {
                        fontSize: "11px",
                        color: t.mode === "dark" ? "#9ca3af" : "#6b7280",
                        textAlign: "right"
                    },
                    children: [
                        "powered by ",
                        /* @__PURE__ */ e.jsx("strong", {
                            children: "AdMesh"
                        })
                    ]
                })
            ]
        })
    });
}, V = ({ recommendation: r, theme: t, compact: s = !1, showReason: d = !0, onClick: n, className: a })=>{
    const i = Math.round(r.intent_match_score * 100), m = w("admesh-inline-recommendation", "group cursor-pointer transition-all duration-200", {
        "p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700": !s,
        "p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30": s
    }, a), g = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsx(q, {
        adId: r.ad_id,
        admeshLink: r.admesh_link,
        productId: r.product_id,
        onClick: ()=>n == null ? void 0 : n(r.ad_id, r.admesh_link),
        trackingData: {
            title: r.title,
            matchScore: r.intent_match_score
        },
        className: m,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "flex items-start gap-3",
            style: g,
            "data-admesh-theme": t == null ? void 0 : t.mode,
            children: [
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex-shrink-0 mt-0.5",
                    children: r.intent_match_score >= 0.8 ? /* @__PURE__ */ e.jsx("div", {
                        className: "w-2 h-2 bg-green-500 rounded-full"
                    }) : /* @__PURE__ */ e.jsx("div", {
                        className: "w-2 h-2 bg-blue-500 rounded-full"
                    })
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex-1 min-w-0",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row",
                            children: [
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: w("font-medium transition-colors duration-200 flex-shrink-0", "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300", "cursor-pointer hover:underline", s ? "text-sm sm:text-base" : "text-base sm:text-lg"),
                                    children: r.title
                                }),
                                r.intent_match_score >= 0.7 && /* @__PURE__ */ e.jsxs("span", {
                                    className: w("inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap", r.intent_match_score >= 0.8 ? "bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100" : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"),
                                    children: [
                                        i,
                                        "% match"
                                    ]
                                })
                            ]
                        }),
                        d && r.reason && /* @__PURE__ */ e.jsx("p", {
                            className: w("text-gray-600 dark:text-gray-400 line-clamp-2", s ? "text-xs" : "text-sm"),
                            children: r.reason
                        }),
                        !s && r.keywords && r.keywords.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-wrap gap-1 mt-2",
                            children: [
                                r.keywords.slice(0, 3).map((x, l)=>/* @__PURE__ */ e.jsx("span", {
                                        className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300",
                                        children: x
                                    }, l)),
                                r.keywords.length > 3 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs text-gray-500 dark:text-gray-400",
                                    children: [
                                        "+",
                                        r.keywords.length - 3,
                                        " more"
                                    ]
                                })
                            ]
                        }),
                        !s && (r.has_free_tier || r.trial_days) && /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-2 mt-2",
                            children: [
                                r.has_free_tier && /* @__PURE__ */ e.jsx("span", {
                                    className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100",
                                    children: "Free tier"
                                }),
                                r.trial_days && r.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                                    children: [
                                        r.trial_days,
                                        "-day trial"
                                    ]
                                })
                            ]
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity",
                    children: /* @__PURE__ */ e.jsx("svg", {
                        className: "w-4 h-4 text-gray-400 dark:text-gray-500",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /* @__PURE__ */ e.jsx("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M9 5l7 7-7 7"
                        })
                    })
                })
            ]
        })
    });
}, Re = ({ recommendations: r, conversationSummary: t, theme: s, showTopRecommendations: d = 3, onRecommendationClick: n, onStartNewConversation: a, className: i })=>{
    const m = r.sort((l, h)=>h.intent_match_score - l.intent_match_score).slice(0, d), g = w("admesh-conversation-summary", "bg-white dark:bg-black", "rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6", "font-sans", // Standardize font family
    i), x = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: g,
        style: x,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex items-center gap-3 mb-4",
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: "flex-shrink-0",
                        children: /* @__PURE__ */ e.jsx("div", {
                            className: "w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center",
                            children: /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                })
                            })
                        })
                    }),
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "min-w-0 flex-1",
                        children: [
                            /* @__PURE__ */ e.jsx("h3", {
                                className: "text-base sm:text-lg font-semibold text-black dark:text-white",
                                children: "Conversation Summary"
                            }),
                            /* @__PURE__ */ e.jsx("p", {
                                className: "text-xs sm:text-sm text-gray-600 dark:text-gray-300",
                                children: "Here's what we discussed and found for you"
                            })
                        ]
                    })
                ]
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "mb-6",
                children: /* @__PURE__ */ e.jsx("div", {
                    className: "bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700",
                    children: /* @__PURE__ */ e.jsx("p", {
                        className: "text-gray-800 dark:text-gray-200 leading-relaxed",
                        children: t
                    })
                })
            }),
            m.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                className: "mb-6",
                children: [
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-2 mb-3",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-5 h-5 text-black dark:text-white",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                })
                            }),
                            /* @__PURE__ */ e.jsx("h4", {
                                className: "font-medium text-black dark:text-white",
                                children: "Top Recommendations"
                            })
                        ]
                    }),
                    /* @__PURE__ */ e.jsx("div", {
                        className: "space-y-2",
                        children: m.map((l, h)=>/* @__PURE__ */ e.jsxs("div", {
                                className: "relative",
                                children: [
                                    /* @__PURE__ */ e.jsx("div", {
                                        className: "absolute -left-2 top-2 z-10",
                                        children: /* @__PURE__ */ e.jsx("div", {
                                            className: w("w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold", h === 0 ? "bg-black dark:bg-white text-white dark:text-black" : h === 1 ? "bg-gray-600 dark:bg-gray-400 text-white dark:text-black" : "bg-gray-800 dark:bg-gray-200 text-white dark:text-black"),
                                            children: h + 1
                                        })
                                    }),
                                    /* @__PURE__ */ e.jsx("div", {
                                        className: "ml-4",
                                        children: /* @__PURE__ */ e.jsx(V, {
                                            recommendation: l,
                                            theme: s,
                                            compact: !0,
                                            showReason: !0,
                                            onClick: n
                                        })
                                    })
                                ]
                            }, l.ad_id || h))
                    })
                ]
            }),
            r.length > d && /* @__PURE__ */ e.jsx("div", {
                className: "mb-6",
                children: /* @__PURE__ */ e.jsx("div", {
                    className: "bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700",
                    children: /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4 text-black dark:text-white",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                })
                            }),
                            /* @__PURE__ */ e.jsxs("span", {
                                className: "text-sm font-medium text-gray-800 dark:text-gray-200",
                                children: [
                                    r.length - d,
                                    " additional recommendation",
                                    r.length - d > 1 ? "s" : "",
                                    " available"
                                ]
                            })
                        ]
                    })
                })
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex flex-col sm:flex-row gap-3",
                children: [
                    a && /* @__PURE__ */ e.jsxs("button", {
                        onClick: a,
                        className: "flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                })
                            }),
                            "Start New Conversation"
                        ]
                    }),
                    /* @__PURE__ */ e.jsxs("button", {
                        onClick: ()=>{
                            m.length > 0 && (n == null || n(m[0].ad_id, m[0].admesh_link));
                        },
                        className: "flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                })
                            }),
                            "View Top Pick"
                        ]
                    })
                ]
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",
                children: /* @__PURE__ */ e.jsx("span", {
                    className: "text-xs text-gray-500 dark:text-gray-400",
                    children: "Powered by AdMesh"
                })
            })
        ]
    });
}, xe = ({ recommendation: r, citationNumber: t, citationStyle: s = "numbered", theme: d, showTooltip: n = !0, onClick: a, onHover: i, className: m })=>{
    const [g, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), l = ()=>{
        x(!0), i == null || i(r);
    }, h = ()=>{
        x(!1);
    }, c = ()=>{
        a == null || a(r.ad_id, r.admesh_link);
    }, p = ()=>{
        switch(s){
            case "bracketed":
                return `[${t}]`;
            case "superscript":
                return t.toString();
            case "numbered":
            default:
                return t.toString();
        }
    }, u = w("admesh-citation-reference", "inline-flex items-center justify-center", "cursor-pointer transition-all duration-200", "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300", "font-medium", {
        // Numbered style (default)
        "w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50": s === "numbered",
        // Bracketed style
        "px-1 text-sm hover:underline": s === "bracketed",
        // Superscript style
        "text-xs align-super hover:underline": s === "superscript"
    }, m), y = d != null && d.accentColor ? {
        "--admesh-primary": d.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("span", {
        className: "relative inline-block",
        children: [
            /* @__PURE__ */ e.jsx(q, {
                adId: r.ad_id,
                admeshLink: r.admesh_link,
                productId: r.product_id,
                onClick: c,
                trackingData: {
                    title: r.title,
                    matchScore: r.intent_match_score,
                    citationNumber: t,
                    citationStyle: s
                },
                className: u,
                children: /* @__PURE__ */ e.jsx("span", {
                    style: y,
                    "data-admesh-theme": d == null ? void 0 : d.mode,
                    onMouseEnter: l,
                    onMouseLeave: h,
                    children: p()
                })
            }),
            n && g && /* @__PURE__ */ e.jsx("div", {
                className: "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50",
                children: /* @__PURE__ */ e.jsxs("div", {
                    className: "bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "font-semibold mb-1",
                            children: r.title
                        }),
                        r.reason && /* @__PURE__ */ e.jsx("div", {
                            className: "text-gray-300 dark:text-gray-600 text-xs",
                            children: r.reason.length > 100 ? `${r.reason.substring(0, 100)}...` : r.reason
                        }),
                        r.intent_match_score >= 0.7 && /* @__PURE__ */ e.jsxs("div", {
                            className: "text-green-400 dark:text-green-600 text-xs mt-1",
                            children: [
                                Math.round(r.intent_match_score * 100),
                                "% match"
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-gray-400 dark:text-gray-500 text-xs mt-1 italic",
                            children: "Click to visit product page"
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"
                        })
                    ]
                })
            })
        ]
    });
}, Ie = ({ recommendations: r, conversationText: t, theme: s, showCitationList: d = !0, citationStyle: n = "numbered", onRecommendationClick: a, onCitationHover: i, className: m })=>{
    const [g, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!t || r.length === 0) return {
            text: t,
            citationMap: /* @__PURE__ */ new Map()
        };
        let u = t;
        const y = /* @__PURE__ */ new Map();
        return [
            ...r
        ].sort((b, N)=>N.intent_match_score - b.intent_match_score).forEach((b, N)=>{
            const j = N + 1, A = b.title;
            y.set(j, b);
            const _ = new RegExp(`\\b${A.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi");
            if (_.test(u)) u = u.replace(_, (T)=>`${T}{{CITATION_${j}}}`);
            else {
                const T = b.keywords || [];
                let k = !1;
                for (const M of T){
                    const R = new RegExp(`\\b${M.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi");
                    if (R.test(u) && !k) {
                        u = u.replace(R, ($)=>(k = !0, `${$}{{CITATION_${j}}}`));
                        break;
                    }
                }
                k || (u += `{{CITATION_${j}}}`);
            }
        }), {
            text: u,
            citationMap: y
        };
    }, [
        t,
        r
    ]), h = ()=>{
        const { text: u, citationMap: y } = l;
        return u.split(/(\{\{CITATION_\d+\}\})/).map((b, N)=>{
            const j = b.match(/\{\{CITATION_(\d+)\}\}/);
            if (j) {
                const A = parseInt(j[1]), _ = y.get(A);
                if (_) return /* @__PURE__ */ e.jsx(xe, {
                    recommendation: _,
                    citationNumber: A,
                    citationStyle: n,
                    theme: s,
                    showTooltip: !0,
                    onClick: a,
                    onHover: (T)=>{
                        x(T), i == null || i(T);
                    }
                }, `citation-${A}-${N}`);
            }
            return /* @__PURE__ */ e.jsx("span", {
                children: b
            }, N);
        });
    }, c = w("admesh-citation-unit", "space-y-4", m), p = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: c,
        style: p,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                className: "admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed",
                children: h()
            }),
            d && r.length > 0 && /* @__PURE__ */ e.jsx("div", {
                className: "admesh-citation-list",
                children: /* @__PURE__ */ e.jsxs("div", {
                    className: "border-t border-gray-200 dark:border-slate-700 pt-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("h4", {
                            className: "text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-4 h-4",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                    })
                                }),
                                "References"
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "space-y-2",
                            children: r.sort((u, y)=>y.intent_match_score - u.intent_match_score).map((u, y)=>/* @__PURE__ */ e.jsxs("div", {
                                    className: w("flex items-start gap-3 p-2 rounded-lg transition-colors duration-200", {
                                        "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800": (g == null ? void 0 : g.ad_id) === u.ad_id,
                                        "hover:bg-gray-50 dark:hover:bg-slate-800/50": (g == null ? void 0 : g.ad_id) !== u.ad_id
                                    }),
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "flex-shrink-0 mt-1",
                                            children: /* @__PURE__ */ e.jsx(xe, {
                                                recommendation: u,
                                                citationNumber: y + 1,
                                                citationStyle: n,
                                                theme: s,
                                                showTooltip: !1,
                                                onClick: a
                                            })
                                        }),
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "flex-1 min-w-0",
                                            children: /* @__PURE__ */ e.jsx(V, {
                                                recommendation: u,
                                                theme: s,
                                                compact: !0,
                                                showReason: !1,
                                                onClick: a
                                            })
                                        })
                                    ]
                                }, u.ad_id || y))
                        })
                    ]
                })
            })
        ]
    });
}, ge = ({ recommendations: r, config: t, theme: s, conversationSummary: d, sessionId: n, onRecommendationClick: a, onDismiss: i, className: m })=>{
    const [g, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(t.autoShow !== !1), [l, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (t.delayMs && t.delayMs > 0) {
            const j = setTimeout(()=>{
                x(!0), h(!0);
            }, t.delayMs);
            return ()=>clearTimeout(j);
        } else h(!0);
    }, [
        t.delayMs
    ]), !g || r.length === 0) return null;
    const c = t.maxRecommendations || 3, p = r.slice(0, c), u = (j, A)=>{
        a == null || a(j, A);
    }, y = ()=>{
        x(!1), i == null || i();
    }, f = ()=>{
        switch(t.displayMode){
            case "summary":
                return d ? /* @__PURE__ */ e.jsx(Re, {
                    recommendations: p,
                    conversationSummary: d,
                    theme: s,
                    showTopRecommendations: c,
                    onRecommendationClick: u,
                    onStartNewConversation: i
                }) : null;
            case "inline":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-2",
                    children: p.map((j, A)=>/* @__PURE__ */ e.jsx(V, {
                            recommendation: j,
                            theme: s,
                            compact: !0,
                            showReason: !0,
                            onClick: u
                        }, j.ad_id || A))
                });
            case "minimal":
                return p.length > 0 ? /* @__PURE__ */ e.jsxs("div", {
                    className: "admesh-minimal-unit",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-2 mb-2",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-2 h-2 bg-blue-500 rounded-full"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                    children: [
                                        p.length,
                                        " intelligent match",
                                        p.length > 1 ? "es" : "",
                                        " found"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx(V, {
                            recommendation: p[0],
                            theme: s,
                            compact: !0,
                            showReason: !1,
                            onClick: u
                        }),
                        p.length > 1 && /* @__PURE__ */ e.jsxs("div", {
                            className: "text-xs text-gray-500 dark:text-gray-400 mt-1",
                            children: [
                                "+",
                                p.length - 1,
                                " more recommendation",
                                p.length > 2 ? "s" : ""
                            ]
                        })
                    ]
                }) : null;
            case "citation":
                return d ? /* @__PURE__ */ e.jsx(Ie, {
                    recommendations: p,
                    conversationText: d,
                    theme: s,
                    showCitationList: !0,
                    citationStyle: "numbered",
                    onRecommendationClick: u
                }) : null;
            case "floating":
                return /* @__PURE__ */ e.jsxs("div", {
                    className: "admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex justify-between items-start mb-3",
                            children: [
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "w-2 h-2 bg-blue-500 rounded-full"
                                        }),
                                        /* @__PURE__ */ e.jsx("span", {
                                            className: "text-sm font-semibold text-gray-800 dark:text-gray-200",
                                            children: "Recommended for you"
                                        })
                                    ]
                                }),
                                i && /* @__PURE__ */ e.jsx("button", {
                                    onClick: y,
                                    className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",
                                    "aria-label": "Dismiss recommendations",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M6 18L18 6M6 6l12 12"
                                        })
                                    })
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "space-y-2",
                            children: p.map((j, A)=>/* @__PURE__ */ e.jsx(V, {
                                    recommendation: j,
                                    theme: s,
                                    compact: !0,
                                    showReason: !1,
                                    onClick: u
                                }, j.ad_id || A))
                        })
                    ]
                });
            default:
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: p.map((j, A)=>/* @__PURE__ */ e.jsx(ee, {
                            recommendation: j,
                            theme: s,
                            showMatchScore: !1,
                            showBadges: !0,
                            onClick: u
                        }, j.ad_id || A))
                });
        }
    }, b = w("admesh-conversational-unit", "transition-all duration-300 ease-in-out", {
        "opacity-0 translate-y-2": !l,
        "opacity-100 translate-y-0": l,
        "fixed bottom-4 right-4 max-w-sm z-50": t.displayMode === "floating",
        "my-3": t.displayMode === "inline",
        "mt-4 pt-4 border-t border-gray-200 dark:border-slate-700": t.displayMode === "summary"
    }, m), N = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: b,
        style: N,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        "data-admesh-context": t.context,
        "data-session-id": n,
        children: [
            f(),
            t.showPoweredBy !== !1 && /* @__PURE__ */ e.jsx("div", {
                className: "flex justify-end mt-2",
                children: /* @__PURE__ */ e.jsx("span", {
                    className: "text-xs text-gray-400 dark:text-gray-500",
                    children: "Powered by AdMesh"
                })
            })
        ]
    });
}, ze = ({ message: r, theme: t, onRecommendationClick: s, className: d })=>{
    const n = r.role === "user", a = r.role === "assistant", i = w("admesh-chat-message", "flex items-start gap-3", {
        "flex-row-reverse": n
    }, d), m = w("max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm", {
        "bg-gradient-to-r from-blue-600 to-indigo-600 text-white": n,
        "bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100": a,
        "bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100": r.role === "system"
    }), g = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0, x = (l)=>l.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit"
        });
    return /* @__PURE__ */ e.jsxs("div", {
        className: i,
        style: g,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            !n && /* @__PURE__ */ e.jsx("div", {
                className: "w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",
                children: /* @__PURE__ */ e.jsx("svg", {
                    className: "w-4 h-4 text-white",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ e.jsx("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M13 10V3L4 14h7v7l9-11h-7z"
                    })
                })
            }),
            n && /* @__PURE__ */ e.jsx("div", {
                className: "w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0",
                children: /* @__PURE__ */ e.jsx("svg", {
                    className: "w-4 h-4 text-gray-600 dark:text-gray-300",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ e.jsx("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    })
                })
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: `flex flex-col ${n ? "items-end" : "items-start"} flex-1`,
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: m,
                        children: /* @__PURE__ */ e.jsx("div", {
                            className: "whitespace-pre-wrap break-words",
                            children: r.content
                        })
                    }),
                    /* @__PURE__ */ e.jsx("div", {
                        className: w("text-xs text-gray-500 dark:text-gray-400 mt-1", {
                            "text-right": n
                        }),
                        children: x(r.timestamp)
                    }),
                    r.recommendations && r.recommendations.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                        className: "mt-3 w-full max-w-lg",
                        children: [
                            /* @__PURE__ */ e.jsxs("div", {
                                className: "flex items-center gap-2 mb-3",
                                children: [
                                    /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4 text-blue-600 dark:text-blue-400",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 10V3L4 14h7v7l9-11h-7z"
                                        })
                                    }),
                                    /* @__PURE__ */ e.jsxs("span", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                        children: [
                                            r.recommendations.length,
                                            " recommendation",
                                            r.recommendations.length > 1 ? "s" : "",
                                            " found"
                                        ]
                                    })
                                ]
                            }),
                            /* @__PURE__ */ e.jsx(ge, {
                                recommendations: r.recommendations,
                                config: {
                                    displayMode: "inline",
                                    context: "chat",
                                    maxRecommendations: 3,
                                    showPoweredBy: !1,
                                    autoShow: !0,
                                    delayMs: 300
                                },
                                theme: t,
                                onRecommendationClick: s,
                                className: "bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700"
                            })
                        ]
                    })
                ]
            })
        ]
    });
}, Pe = ({ placeholder: r = "Type your message...", disabled: t = !1, suggestions: s = [], theme: d, onSendMessage: n, className: a })=>{
    const [i, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""), [g, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), [l, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), p = (_)=>{
        const T = _.target.value;
        if (m(T), T.trim() && s.length > 0) {
            const k = s.filter((M)=>M.toLowerCase().includes(T.toLowerCase()));
            h(k), x(k.length > 0);
        } else x(!1);
        c.current && (c.current.style.height = "auto", c.current.style.height = `${Math.min(c.current.scrollHeight, 120)}px`);
    }, u = (_)=>{
        _.key === "Enter" && !_.shiftKey && (_.preventDefault(), y());
    }, y = ()=>{
        const _ = i.trim();
        _ && !t && n && (n(_), m(""), x(!1), c.current && (c.current.style.height = "auto"));
    }, f = (_)=>{
        m(_), x(!1), c.current && c.current.focus();
    }, b = w("admesh-chat-input", "relative", a), N = w("w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600", "bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100", "placeholder-gray-500 dark:placeholder-gray-400", "focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent", "transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600", "pr-12 pl-4 py-3 text-sm leading-5", {
        "opacity-50 cursor-not-allowed": t
    }), j = w("absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200", "flex items-center justify-center", {
        "bg-blue-600 hover:bg-blue-700 text-white": i.trim() && !t,
        "bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed": !i.trim() || t
    }), A = d != null && d.accentColor ? {
        "--admesh-primary": d.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: b,
        style: A,
        "data-admesh-theme": d == null ? void 0 : d.mode,
        children: [
            g && l.length > 0 && /* @__PURE__ */ e.jsx("div", {
                className: "absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10",
                children: l.slice(0, 5).map((_, T)=>/* @__PURE__ */ e.jsx("button", {
                        onClick: ()=>f(_),
                        className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg",
                        children: _
                    }, T))
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: "relative",
                children: [
                    /* @__PURE__ */ e.jsx("textarea", {
                        ref: c,
                        value: i,
                        onChange: p,
                        onKeyDown: u,
                        placeholder: r,
                        disabled: t,
                        rows: 1,
                        className: N,
                        style: {
                            minHeight: "44px",
                            maxHeight: "120px"
                        }
                    }),
                    /* @__PURE__ */ e.jsx("button", {
                        onClick: y,
                        disabled: !i.trim() || t,
                        className: j,
                        "aria-label": "Send message",
                        children: /* @__PURE__ */ e.jsx("svg", {
                            className: "w-4 h-4",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /* @__PURE__ */ e.jsx("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                            })
                        })
                    })
                ]
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400",
                children: [
                    /* @__PURE__ */ e.jsx("span", {
                        children: "Press Enter to send, Shift+Enter for new line"
                    }),
                    /* @__PURE__ */ e.jsxs("span", {
                        className: w("transition-opacity duration-200", {
                            "opacity-0": i.length < 100
                        }),
                        children: [
                            i.length,
                            "/500"
                        ]
                    })
                ]
            })
        ]
    });
}, Be = ({ messages: r, config: t, theme: s, isLoading: d = !1, onSendMessage: n, onRecommendationClick: a, className: i })=>{
    const m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        m.current && m.current.scrollIntoView({
            behavior: "smooth"
        });
    }, [
        r
    ]);
    const x = w("admesh-chat-interface", "flex flex-col h-full bg-white dark:bg-slate-900", i), l = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0, h = t.maxMessages ? r.slice(-t.maxMessages) : r;
    return /* @__PURE__ */ e.jsxs("div", {
        className: x,
        style: l,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                ref: g,
                className: "flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",
                children: h.length === 0 ? /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-col items-center justify-center h-full text-center",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4",
                            children: /* @__PURE__ */ e.jsx("svg", {
                                className: "w-8 h-8 text-blue-600 dark:text-blue-400",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                })
                            })
                        }),
                        /* @__PURE__ */ e.jsx("h3", {
                            className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",
                            children: "Welcome to AdMesh AI"
                        }),
                        /* @__PURE__ */ e.jsx("p", {
                            className: "text-sm text-gray-600 dark:text-gray-400 max-w-xs",
                            children: "Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!"
                        })
                    ]
                }) : /* @__PURE__ */ e.jsxs(e.Fragment, {
                    children: [
                        h.map((c)=>/* @__PURE__ */ e.jsx(ze, {
                                message: c,
                                theme: s,
                                onRecommendationClick: a
                            }, c.id)),
                        d && t.enableTypingIndicator !== !1 && /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-start gap-3",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4 text-white",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 10V3L4 14h7v7l9-11h-7z"
                                        })
                                    })
                                }),
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs",
                                    children: /* @__PURE__ */ e.jsxs("div", {
                                        className: "flex space-x-1",
                                        children: [
                                            /* @__PURE__ */ e.jsx("div", {
                                                className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                                            }),
                                            /* @__PURE__ */ e.jsx("div", {
                                                className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
                                                style: {
                                                    animationDelay: "0.1s"
                                                }
                                            }),
                                            /* @__PURE__ */ e.jsx("div", {
                                                className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
                                                style: {
                                                    animationDelay: "0.2s"
                                                }
                                            })
                                        ]
                                    })
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            ref: m
                        })
                    ]
                })
            }),
            t.enableSuggestions && t.suggestions && t.suggestions.length > 0 && r.length === 0 && /* @__PURE__ */ e.jsxs("div", {
                className: "px-4 pb-2",
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: "text-xs text-gray-500 dark:text-gray-400 mb-2",
                        children: "Quick suggestions:"
                    }),
                    /* @__PURE__ */ e.jsx("div", {
                        className: "flex flex-wrap gap-2",
                        children: t.suggestions.slice(0, 3).map((c, p)=>/* @__PURE__ */ e.jsx("button", {
                                onClick: ()=>n == null ? void 0 : n(c),
                                className: "px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors",
                                children: c
                            }, p))
                    })
                ]
            }),
            t.showInputField !== !1 && n && /* @__PURE__ */ e.jsx("div", {
                className: "border-t border-gray-200 dark:border-slate-700 p-4",
                children: /* @__PURE__ */ e.jsx(Pe, {
                    placeholder: t.placeholder || "Ask me about products, tools, or services...",
                    disabled: d,
                    suggestions: t.suggestions,
                    theme: s,
                    onSendMessage: n
                })
            })
        ]
    });
}, Ye = ({ config: r, theme: t, title: s = "AI Assistant", subtitle: d = "Get personalized recommendations", isOpen: n, onToggle: a, onSendMessage: i, onRecommendationClick: m, autoRecommendations: g, autoRecommendationTrigger: x, showInputField: l = !0, autoShowRecommendations: h = !1, onAutoRecommendationDismiss: c, className: p })=>{
    const [u, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(r.autoOpen || !1), [f, b] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]), [N, j] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), [A, _] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), T = n !== void 0 ? n : u;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (r.showWelcomeMessage && r.welcomeMessage && f.length === 0) {
            const I = {
                id: "welcome",
                role: "assistant",
                content: r.welcomeMessage,
                timestamp: /* @__PURE__ */ new Date()
            };
            b([
                I
            ]);
        }
    }, [
        r.showWelcomeMessage,
        r.welcomeMessage,
        f.length
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (g && g.length > 0 && h) {
            const I = {
                id: `auto-${Date.now()}`,
                role: "assistant",
                content: x ? `Based on "${x}", here are some relevant recommendations:` : "I found some relevant recommendations for you:",
                timestamp: /* @__PURE__ */ new Date(),
                recommendations: g
            };
            n === void 0 && y(!0), b((W)=>W.some((z)=>z.id.startsWith("auto-")) ? W.map((z)=>z.id.startsWith("auto-") ? I : z) : [
                    ...W,
                    I
                ]);
        }
    }, [
        g,
        h,
        x,
        n
    ]);
    const k = ()=>{
        a ? a() : y(!u), _(!0);
    }, M = async (I)=>{
        if (!i) return;
        const W = {
            id: `user-${Date.now()}`,
            role: "user",
            content: I,
            timestamp: /* @__PURE__ */ new Date()
        };
        b((F)=>[
                ...F,
                W
            ]), j(!0);
        try {
            const F = await i(I);
            b((z)=>[
                    ...z,
                    F
                ]);
        } catch (F) {
            console.error("Error sending message:", F);
            const z = {
                id: `error-${Date.now()}`,
                role: "assistant",
                content: "Sorry, I encountered an error. Please try again.",
                timestamp: /* @__PURE__ */ new Date()
            };
            b((X)=>[
                    ...X,
                    z
                ]);
        } finally{
            j(!1);
        }
    }, R = ()=>{
        switch(r.size){
            case "sm":
                return "w-80 h-96";
            case "md":
                return "w-96 h-[32rem]";
            case "lg":
                return "w-[28rem] h-[36rem]";
            case "xl":
                return "w-[32rem] h-[40rem]";
            default:
                return "w-96 h-[32rem]";
        }
    }, B = w("admesh-floating-chat", "fixed z-50 transition-all duration-300 ease-in-out", (()=>{
        switch(r.position){
            case "bottom-right":
                return "bottom-4 right-4";
            case "bottom-left":
                return "bottom-4 left-4";
            case "top-right":
                return "top-4 right-4";
            case "top-left":
                return "top-4 left-4";
            default:
                return "bottom-4 right-4";
        }
    })(), p), Q = w("bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden", R(), {
        "opacity-0 scale-95 pointer-events-none": !T,
        "opacity-100 scale-100": T
    }), re = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: B,
        style: re,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                className: Q,
                children: T && /* @__PURE__ */ e.jsxs(e.Fragment, {
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",
                            children: [
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-3",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",
                                            children: /* @__PURE__ */ e.jsx("svg", {
                                                className: "w-4 h-4",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                                })
                                            })
                                        }),
                                        /* @__PURE__ */ e.jsxs("div", {
                                            children: [
                                                /* @__PURE__ */ e.jsx("h3", {
                                                    className: "font-semibold text-sm",
                                                    children: s
                                                }),
                                                /* @__PURE__ */ e.jsx("p", {
                                                    className: "text-xs text-blue-100",
                                                    children: d
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        g && g.length > 0 && c && /* @__PURE__ */ e.jsx("button", {
                                            onClick: ()=>{
                                                c(), b((I)=>I.filter((W)=>!W.id.startsWith("auto-")));
                                            },
                                            className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                                            "aria-label": "Dismiss recommendations",
                                            title: "Dismiss recommendations",
                                            children: /* @__PURE__ */ e.jsx("svg", {
                                                className: "w-4 h-4",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                                                })
                                            })
                                        }),
                                        /* @__PURE__ */ e.jsx("button", {
                                            onClick: k,
                                            className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                                            "aria-label": "Close chat",
                                            children: /* @__PURE__ */ e.jsx("svg", {
                                                className: "w-4 h-4",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M6 18L18 6M6 6l12 12"
                                                })
                                            })
                                        })
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx(Be, {
                            messages: f,
                            config: {
                                ...r,
                                showInputField: l
                            },
                            theme: t,
                            isLoading: N,
                            onSendMessage: l ? M : void 0,
                            onRecommendationClick: m,
                            className: "h-full"
                        })
                    ]
                })
            }),
            !T && /* @__PURE__ */ e.jsxs("button", {
                onClick: k,
                className: w("w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700", "text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200", "flex items-center justify-center relative"),
                "aria-label": "Open chat",
                children: [
                    /* @__PURE__ */ e.jsx("svg", {
                        className: "w-6 h-6",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /* @__PURE__ */ e.jsx("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                        })
                    }),
                    !A && /* @__PURE__ */ e.jsx("div", {
                        className: "absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"
                    })
                ]
            }),
            T && /* @__PURE__ */ e.jsx("div", {
                className: "absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm",
                children: "Powered by AdMesh"
            })
        ]
    });
}, We = ({ title: r, theme: t, collapsible: s = !1, isCollapsed: d = !1, onToggle: n, onSearch: a, showSearch: i = !1, className: m })=>{
    const [g, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""), [l, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), [c, p] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const N = ()=>{
            p(window.innerWidth < 640);
        };
        return N(), window.addEventListener("resize", N), ()=>window.removeEventListener("resize", N);
    }, []);
    const u = (N)=>{
        const j = N.target.value;
        x(j), a == null || a(j);
    }, y = ()=>{
        x(""), a == null || a("");
    }, f = w("admesh-sidebar-header", "flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800", m), b = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: f,
        style: b,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex items-center justify-between mb-3",
                children: [
                    /* @__PURE__ */ e.jsx("h3", {
                        className: "text-lg font-semibold text-gray-900 dark:text-gray-100 truncate",
                        children: r
                    }),
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-2",
                        children: [
                            c && n && /* @__PURE__ */ e.jsx("button", {
                                onClick: n,
                                className: "p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden",
                                title: "Close sidebar",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-4 h-4 text-gray-600 dark:text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M6 18L18 6M6 6l12 12"
                                    })
                                })
                            }),
                            s && /* @__PURE__ */ e.jsx("button", {
                                onClick: n,
                                className: "p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block",
                                title: d ? "Expand sidebar" : "Collapse sidebar",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: w("w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200", {
                                        "rotate-180": d
                                    }),
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M15 19l-7-7 7-7"
                                    })
                                })
                            })
                        ]
                    })
                ]
            }),
            i && !d && /* @__PURE__ */ e.jsxs("div", {
                className: "relative",
                children: [
                    /* @__PURE__ */ e.jsxs("div", {
                        className: w("relative flex items-center transition-all duration-200", {
                            "ring-2 ring-blue-500 dark:ring-blue-400": l
                        }),
                        children: [
                            /* @__PURE__ */ e.jsx("div", {
                                className: "absolute left-3 pointer-events-none",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-4 h-4 text-gray-400 dark:text-gray-500",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    })
                                })
                            }),
                            /* @__PURE__ */ e.jsx("input", {
                                type: "text",
                                value: g,
                                onChange: u,
                                onFocus: ()=>h(!0),
                                onBlur: ()=>h(!1),
                                placeholder: "Search recommendations...",
                                className: w("w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg", "placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100", "focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent", "transition-all duration-200")
                            }),
                            g && /* @__PURE__ */ e.jsx("button", {
                                onClick: y,
                                className: "absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors",
                                title: "Clear search",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-3 h-3 text-gray-400 dark:text-gray-500",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M6 18L18 6M6 6l12 12"
                                    })
                                })
                            })
                        ]
                    }),
                    g && /* @__PURE__ */ e.jsx("div", {
                        className: "mt-2 text-xs text-gray-500 dark:text-gray-400",
                        children: "Search results will be filtered in real-time"
                    })
                ]
            }),
            !d && /* @__PURE__ */ e.jsxs("div", {
                className: "mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",
                children: [
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-1",
                        children: [
                            /* @__PURE__ */ e.jsx("div", {
                                className: "w-2 h-2 bg-green-500 rounded-full"
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                children: "Live recommendations"
                            })
                        ]
                    }),
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-1",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-3 h-3",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                })
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                children: "AI-powered"
                            })
                        ]
                    })
                ]
            })
        ]
    });
}, Oe = ({ recommendations: r, displayMode: t, theme: s, maxRecommendations: d, onRecommendationClick: n, className: a })=>{
    const [i, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), [g, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("all"), l = d ? r.slice(0, d) : r, c = (()=>{
        switch(g){
            case "top":
                return l.filter((f)=>f.intent_match_score >= 0.8).slice(0, 5);
            case "recent":
                return l.slice(0, 3);
            default:
                return l;
        }
    })(), p = w("admesh-sidebar-content", "flex flex-col h-full", a), u = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0, y = ()=>{
        if (c.length === 0) return /* @__PURE__ */ e.jsxs("div", {
            className: "flex-1 flex flex-col items-center justify-center p-6 text-center",
            children: [
                /* @__PURE__ */ e.jsx("div", {
                    className: "w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4",
                    children: /* @__PURE__ */ e.jsx("svg", {
                        className: "w-8 h-8 text-gray-400 dark:text-gray-500",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /* @__PURE__ */ e.jsx("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        })
                    })
                }),
                /* @__PURE__ */ e.jsx("h4", {
                    className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                    children: "No recommendations found"
                }),
                /* @__PURE__ */ e.jsx("p", {
                    className: "text-xs text-gray-500 dark:text-gray-400",
                    children: "Try adjusting your search or filters"
                })
            ]
        });
        switch(t){
            case "recommendations":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: c.map((f, b)=>/* @__PURE__ */ e.jsx(V, {
                            recommendation: f,
                            theme: s,
                            compact: !0,
                            showReason: !0,
                            onClick: n
                        }, f.ad_id || b))
                });
            case "history":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-2",
                    children: c.map((f, b)=>/* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-2 h-2 bg-gray-400 rounded-full flex-shrink-0"
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex-1 min-w-0",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                            children: f.title
                                        }),
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400",
                                            children: [
                                                Math.round(f.intent_match_score * 100),
                                                "% match"
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }, f.ad_id || b))
                });
            case "favorites":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: c.slice(0, 3).map((f, b)=>/* @__PURE__ */ e.jsxs("div", {
                            className: "relative",
                            children: [
                                /* @__PURE__ */ e.jsx(V, {
                                    recommendation: f,
                                    theme: s,
                                    compact: !0,
                                    showReason: !1,
                                    onClick: n
                                }),
                                /* @__PURE__ */ e.jsx("button", {
                                    className: "absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-3 h-3 text-yellow-500",
                                        fill: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                                        })
                                    })
                                })
                            ]
                        }, f.ad_id || b))
                });
            case "mixed":
                return /* @__PURE__ */ e.jsxs("div", {
                    className: "space-y-4",
                    children: [
                        c[0] && /* @__PURE__ */ e.jsxs("div", {
                            children: [
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",
                                    children: "Top Pick"
                                }),
                                /* @__PURE__ */ e.jsx(ee, {
                                    recommendation: c[0],
                                    theme: s,
                                    showMatchScore: !0,
                                    showBadges: !0,
                                    onClick: n,
                                    className: "text-xs"
                                })
                            ]
                        }),
                        c.slice(1).length > 0 && /* @__PURE__ */ e.jsxs("div", {
                            children: [
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",
                                    children: "More Options"
                                }),
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "space-y-2",
                                    children: c.slice(1, 4).map((f, b)=>/* @__PURE__ */ e.jsx(V, {
                                            recommendation: f,
                                            theme: s,
                                            compact: !0,
                                            showReason: !1,
                                            onClick: n
                                        }, f.ad_id || b))
                                })
                            ]
                        })
                    ]
                });
            default:
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: c.map((f, b)=>/* @__PURE__ */ e.jsx(V, {
                            recommendation: f,
                            theme: s,
                            compact: !0,
                            showReason: !0,
                            onClick: n
                        }, f.ad_id || b))
                });
        }
    };
    return /* @__PURE__ */ e.jsxs("div", {
        className: p,
        style: u,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900",
                children: [
                    /* @__PURE__ */ e.jsxs("button", {
                        onClick: ()=>x("all"),
                        className: w("flex-1 px-3 py-2 text-xs font-medium transition-colors", g === "all" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),
                        children: [
                            "All (",
                            r.length,
                            ")"
                        ]
                    }),
                    /* @__PURE__ */ e.jsx("button", {
                        onClick: ()=>x("top"),
                        className: w("flex-1 px-3 py-2 text-xs font-medium transition-colors", g === "top" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),
                        children: "Top"
                    }),
                    /* @__PURE__ */ e.jsx("button", {
                        onClick: ()=>x("recent"),
                        className: w("flex-1 px-3 py-2 text-xs font-medium transition-colors", g === "recent" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),
                        children: "Recent"
                    })
                ]
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "flex-1 overflow-y-auto p-4 min-h-0",
                style: {
                    WebkitOverflowScrolling: "touch",
                    // Smooth scrolling on iOS
                    overscrollBehavior: "contain"
                },
                children: y()
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",
                children: /* @__PURE__ */ e.jsxs("div", {
                    className: "flex items-center justify-between text-xs",
                    children: [
                        /* @__PURE__ */ e.jsxs("span", {
                            className: "text-gray-500 dark:text-gray-400",
                            children: [
                                c.length,
                                " recommendation",
                                c.length !== 1 ? "s" : ""
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("button", {
                            onClick: ()=>m(!i),
                            className: "text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors",
                            children: "Filters"
                        })
                    ]
                })
            })
        ]
    });
}, He = ({ recommendations: r, config: t, theme: s, title: d = "Recommendations", isOpen: n = !0, onToggle: a, onRecommendationClick: i, onSearch: m, // onFilter,
className: g, containerMode: x = !1 })=>{
    const [l, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(t.defaultCollapsed || !1), [c, p] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(""), [u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({}), [y, f] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const k = ()=>{
            f(window.innerWidth < 640);
        };
        return k(), window.addEventListener("resize", k), ()=>window.removeEventListener("resize", k);
    }, []), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (y && n && !l && !x) {
            const k = window.getComputedStyle(document.body).overflow;
            return document.body.style.overflow = "hidden", document.body.style.position = "fixed", document.body.style.width = "100%", ()=>{
                document.body.style.overflow = k, document.body.style.position = "", document.body.style.width = "";
            };
        }
    }, [
        y,
        n,
        l,
        x
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (t.autoRefresh && t.refreshInterval) {
            const k = setInterval(()=>{
                console.log("Auto-refreshing recommendations...");
            }, t.refreshInterval);
            return ()=>clearInterval(k);
        }
    }, [
        t.autoRefresh,
        t.refreshInterval
    ]);
    const b = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        let k = [
            ...r
        ];
        if (c.trim()) {
            const M = c.toLowerCase();
            k = k.filter((R)=>{
                var $;
                return R.title.toLowerCase().includes(M) || R.reason.toLowerCase().includes(M) || (($ = R.keywords) == null ? void 0 : $.some((B)=>B.toLowerCase().includes(M)));
            });
        }
        return u.categories && u.categories.length > 0 && (k = k.filter((M)=>{
            var R;
            return (R = M.categories) == null ? void 0 : R.some(($)=>{
                var B;
                return (B = u.categories) == null ? void 0 : B.includes($);
            });
        })), u.hasFreeTier && (k = k.filter((M)=>M.has_free_tier)), u.hasTrial && (k = k.filter((M)=>M.trial_days && M.trial_days > 0)), u.minMatchScore !== void 0 && (k = k.filter((M)=>M.intent_match_score >= u.minMatchScore)), k.sort((M, R)=>R.intent_match_score - M.intent_match_score), t.maxRecommendations && (k = k.slice(0, t.maxRecommendations)), k;
    }, [
        r,
        c,
        u,
        t.maxRecommendations
    ]), N = ()=>{
        t.collapsible && (h(!l), a == null || a());
    }, j = (k)=>{
        p(k), m == null || m(k);
    }, _ = w("admesh-sidebar", "flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out", (()=>{
        if (l) return "w-12";
        switch(t.size){
            case "sm":
                return "w-full sm:w-64 max-w-[90vw] sm:max-w-sm";
            case "md":
                return "w-full sm:w-80 max-w-[90vw] sm:max-w-md";
            case "lg":
                return "w-full sm:w-96 max-w-[90vw] sm:max-w-lg";
            case "xl":
                return "w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl";
            default:
                return "w-full sm:w-80 max-w-[90vw] sm:max-w-md";
        }
    })(), {
        "border-r": t.position === "left",
        "border-l": t.position === "right",
        // Use fixed positioning for full-screen mode, relative for container mode
        // Improved mobile positioning with proper viewport handling
        "fixed top-0 bottom-0 z-[9999]": !x,
        "relative h-full": x,
        "left-0": t.position === "left" && !x,
        "right-0": t.position === "right" && !x,
        // Better mobile transform handling
        "transform -translate-x-full": t.position === "left" && !n && !x,
        "transform translate-x-full": t.position === "right" && !n && !x,
        // Mobile-specific improvements
        "min-h-0": !0,
        // Prevent height issues on mobile
        "overflow-hidden": !x
    }, g), T = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return !n && !t.collapsible ? null : /* @__PURE__ */ e.jsxs(e.Fragment, {
        children: [
            n && !l && /* @__PURE__ */ e.jsx("div", {
                className: w("bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300", x ? "absolute inset-0" : "fixed inset-0"),
                onClick: ()=>a == null ? void 0 : a(),
                style: {
                    // Ensure overlay covers the entire viewport on mobile
                    position: x ? "absolute" : "fixed",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    touchAction: "none"
                }
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: _,
                style: T,
                "data-admesh-theme": s == null ? void 0 : s.mode,
                "data-sidebar-position": t.position,
                "data-sidebar-size": t.size,
                "data-mobile-open": y && n && !l ? "true" : "false",
                "data-container-mode": x ? "true" : "false",
                children: [
                    t.showHeader !== !1 && /* @__PURE__ */ e.jsx(We, {
                        title: d,
                        theme: s,
                        collapsible: t.collapsible,
                        isCollapsed: l,
                        onToggle: N,
                        onSearch: t.showSearch ? j : void 0,
                        showSearch: t.showSearch && !l
                    }),
                    !l && /* @__PURE__ */ e.jsx(Oe, {
                        recommendations: b,
                        displayMode: t.displayMode,
                        theme: s,
                        maxRecommendations: t.maxRecommendations,
                        onRecommendationClick: i,
                        className: "flex-1 overflow-hidden min-h-0"
                    }),
                    l && t.collapsible && /* @__PURE__ */ e.jsxs("div", {
                        className: "flex-1 flex flex-col items-center justify-center p-2",
                        children: [
                            /* @__PURE__ */ e.jsx("button", {
                                onClick: N,
                                className: "p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors",
                                title: "Expand sidebar",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-5 h-5 text-gray-600 dark:text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M9 5l7 7-7 7"
                                    })
                                })
                            }),
                            /* @__PURE__ */ e.jsx("div", {
                                className: "mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap",
                                children: b.length
                            })
                        ]
                    }),
                    !l && /* @__PURE__ */ e.jsx("div", {
                        className: "p-3 border-t border-gray-200 dark:border-slate-700",
                        children: /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-400 dark:text-gray-500 text-center",
                            children: "Powered by AdMesh"
                        })
                    })
                ]
            })
        ]
    });
}, qe = ({ recommendations: r, trigger: t, theme: s, title: d = "AI Recommendations", position: n = "bottom-right", size: a = "md", autoShow: i = !0, showDelay: m = 1e3, onRecommendationClick: g, onDismiss: x, className: l })=>{
    const [h, c] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1), [p, u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (i && r.length > 0) {
            const A = setTimeout(()=>{
                c(!0), u(!0);
            }, m);
            return ()=>clearTimeout(A);
        }
    }, [
        i,
        r.length,
        m
    ]);
    const y = ()=>{
        c(!1), x == null || x();
    }, f = ()=>{
        switch(a){
            case "sm":
                return "w-72 max-h-80";
            case "md":
                return "w-80 max-h-96";
            case "lg":
                return "w-96 max-h-[28rem]";
            default:
                return "w-80 max-h-96";
        }
    }, b = ()=>{
        switch(n){
            case "bottom-right":
                return "bottom-4 right-4";
            case "bottom-left":
                return "bottom-4 left-4";
            case "top-right":
                return "top-4 right-4";
            case "top-left":
                return "top-4 left-4";
            default:
                return "bottom-4 right-4";
        }
    };
    if (!h || r.length === 0) return null;
    const N = w("admesh-auto-recommendation-widget", "fixed z-50 transition-all duration-500 ease-out", b(), f(), {
        "opacity-0 scale-95 translate-y-2": !p,
        "opacity-100 scale-100 translate-y-0": p
    }, l), j = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsx("div", {
        className: N,
        style: j,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 10V3L4 14h7v7l9-11h-7z"
                                        })
                                    })
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    children: [
                                        /* @__PURE__ */ e.jsx("h3", {
                                            className: "font-semibold text-sm",
                                            children: d
                                        }),
                                        t && /* @__PURE__ */ e.jsxs("p", {
                                            className: "text-xs text-blue-100 truncate max-w-48",
                                            children: [
                                                'Based on: "',
                                                t,
                                                '"'
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("button", {
                            onClick: y,
                            className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                            "aria-label": "Dismiss recommendations",
                            children: /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M6 18L18 6M6 6l12 12"
                                })
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-2 mb-3",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                    children: [
                                        r.length,
                                        " intelligent match",
                                        r.length > 1 ? "es" : "",
                                        " found"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx(ge, {
                            recommendations: r,
                            config: {
                                displayMode: "inline",
                                context: "assistant",
                                maxRecommendations: 3,
                                showPoweredBy: !1,
                                autoShow: !0,
                                delayMs: 200
                            },
                            theme: s,
                            onRecommendationClick: g
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700",
                    children: /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /* @__PURE__ */ e.jsx("span", {
                                className: "text-xs text-gray-500 dark:text-gray-400",
                                children: "Powered by AdMesh"
                            }),
                            /* @__PURE__ */ e.jsx("button", {
                                onClick: y,
                                className: "text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",
                                children: "Dismiss"
                            })
                        ]
                    })
                })
            ]
        })
    });
}, Ge = "0.2.1", Je = {
    trackingEnabled: !0,
    debug: !1,
    theme: {
        mode: "light",
        accentColor: "#2563eb"
    }
};
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Star)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",
            key: "r04s7s"
        }
    ]
];
const Star = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("star", __iconNode);
;
 //# sourceMappingURL=star.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-ssr] (ecmascript) <export default as Star>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Star": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Zap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",
            key: "1xq2db"
        }
    ]
];
const Zap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("zap", __iconNode);
;
 //# sourceMappingURL=zap.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-ssr] (ecmascript) <export default as Zap>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Zap": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$zap$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/zap.js [app-ssr] (ecmascript)");
}}),

};

//# sourceMappingURL=node_modules_ec60506c._.js.map