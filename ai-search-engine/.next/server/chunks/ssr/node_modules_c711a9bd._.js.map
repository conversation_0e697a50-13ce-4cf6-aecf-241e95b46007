{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "file": "external-link.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/external-link.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 3h6v6', key: '1q9fwt' }],\n  ['path', { d: 'M10 14 21 3', key: 'gplh6r' }],\n  ['path', { d: 'M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6', key: 'a6xqqp' }],\n];\n\n/**\n * @component @name ExternalLink\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgM2g2djYiIC8+CiAgPHBhdGggZD0iTTEwIDE0IDIxIDMiIC8+CiAgPHBhdGggZD0iTTE4IDEzdjZhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOGEyIDIgMCAwIDEgMi0yaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/external-link\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ExternalLink = createLucideIcon('external-link', __iconNode);\n\nexport default ExternalLink;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3F;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "file": "lightbulb.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/lightbulb.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5',\n      key: '1gvzjb',\n    },\n  ],\n  ['path', { d: 'M9 18h6', key: 'x1upvd' }],\n  ['path', { d: 'M10 22h4', key: 'ceow96' }],\n];\n\n/**\n * @component @name Lightbulb\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/lightbulb\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lightbulb = createLucideIcon('lightbulb', __iconNode);\n\nexport default Lightbulb;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "file": "moon.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/moon.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z', key: 'a7tn18' }],\n];\n\n/**\n * @component @name Moon\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM2E2IDYgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/moon\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Moon = createLucideIcon('moon', __iconNode);\n\nexport default Moon;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACrE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "file": "sun.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/sun.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'M12 20v2', key: '1lh1kg' }],\n  ['path', { d: 'm4.93 4.93 1.41 1.41', key: '149t6j' }],\n  ['path', { d: 'm17.66 17.66 1.41 1.41', key: 'ptbguv' }],\n  ['path', { d: 'M2 12h2', key: '1t8f8n' }],\n  ['path', { d: 'M20 12h2', key: '1q8mjw' }],\n  ['path', { d: 'm6.34 17.66-1.41 1.41', key: '1m8zz5' }],\n  ['path', { d: 'm19.07 4.93-1.41 1.41', key: '1shlcs' }],\n];\n\n/**\n * @component @name Sun\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDIwdjIiIC8+CiAgPHBhdGggZD0ibTQuOTMgNC45MyAxLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0ibTE3LjY2IDE3LjY2IDEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJNMiAxMmgyIiAvPgogIDxwYXRoIGQ9Ik0yMCAxMmgyIiAvPgogIDxwYXRoIGQ9Im02LjM0IDE3LjY2LTEuNDEgMS40MSIgLz4KICA8cGF0aCBkPSJtMTkuMDcgNC45My0xLjQxIDEuNDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sun\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sun = createLucideIcon('sun', __iconNode);\n\nexport default Sun;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACrD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('x', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,EAAK,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "file": "index.mjs", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/node_modules/react/cjs/react-jsx-runtime.production.js", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/node_modules/react/cjs/react-jsx-runtime.development.js", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/node_modules/react/jsx-runtime.js", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/node_modules/classnames/index.js", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/hooks/useAdMeshTracker.ts", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshLinkTracker.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshProductCard.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshCompareTable.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshBadge.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/hooks/useAdMeshStyles.ts", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshLayout.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSimpleAd.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshInlineRecommendation.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshConversationSummary.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshCitationReference.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshCitationUnit.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshConversationalUnit.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshChatMessage.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshChatInput.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshChatInterface.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshFloatingChat.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSidebarHeader.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSidebarContent.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshSidebar.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/components/AdMeshAutoRecommendationWidget.tsx", "file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/admesh-ui-sdk/src/index.ts"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\n\tfunction classNames () {\n\t\tvar classes = '';\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (arg) {\n\t\t\t\tclasses = appendClass(classes, parseValue(arg));\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction parseValue (arg) {\n\t\tif (typeof arg === 'string' || typeof arg === 'number') {\n\t\t\treturn arg;\n\t\t}\n\n\t\tif (typeof arg !== 'object') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (Array.isArray(arg)) {\n\t\t\treturn classNames.apply(null, arg);\n\t\t}\n\n\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\treturn arg.toString();\n\t\t}\n\n\t\tvar classes = '';\n\n\t\tfor (var key in arg) {\n\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\tclasses = appendClass(classes, key);\n\t\t\t}\n\t\t}\n\n\t\treturn classes;\n\t}\n\n\tfunction appendClass (value, newClass) {\n\t\tif (!newClass) {\n\t\t\treturn value;\n\t\t}\n\t\n\t\tif (value) {\n\t\t\treturn value + ' ' + newClass;\n\t\t}\n\t\n\t\treturn value + newClass;\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "import { useState, useCallback, useMemo } from 'react';\nimport type { TrackingData, UseAdMeshTrackerReturn } from '../types/index';\n\n// Default tracking endpoint - can be overridden via config\nconst DEFAULT_TRACKING_URL = 'https://api.useadmesh.com/track';\n\ninterface TrackingConfig {\n  apiBaseUrl?: string;\n  enabled?: boolean;\n  debug?: boolean;\n  retryAttempts?: number;\n  retryDelay?: number;\n}\n\n// Global config that can be set by the consuming application\nlet globalConfig: TrackingConfig = {\n  apiBaseUrl: DEFAULT_TRACKING_URL,\n  enabled: true,\n  debug: false,\n  retryAttempts: 3,\n  retryDelay: 1000\n};\n\nexport const setAdMeshTrackerConfig = (config: Partial<TrackingConfig>) => {\n  globalConfig = { ...globalConfig, ...config };\n};\n\nexport const useAdMeshTracker = (config?: Partial<TrackingConfig>): UseAdMeshTrackerReturn => {\n  const [isTracking, setIsTracking] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mergedConfig = useMemo(() => ({ ...globalConfig, ...config }), [config]);\n\n  const log = useCallback((message: string, data?: unknown) => {\n    if (mergedConfig.debug) {\n      console.log(`[AdMesh Tracker] ${message}`, data);\n    }\n  }, [mergedConfig.debug]);\n\n  const sendTrackingEvent = useCallback(async (\n    eventType: 'click' | 'view' | 'conversion',\n    data: TrackingData\n  ): Promise<void> => {\n    if (!mergedConfig.enabled) {\n      log('Tracking disabled, skipping event', { eventType, data });\n      return;\n    }\n\n    if (!data.adId || !data.admeshLink) {\n      const errorMsg = 'Missing required tracking data: adId and admeshLink are required';\n      log(errorMsg, data);\n      setError(errorMsg);\n      return;\n    }\n\n    setIsTracking(true);\n    setError(null);\n\n    const payload = {\n      event_type: eventType,\n      ad_id: data.adId,\n      admesh_link: data.admeshLink,\n      product_id: data.productId,\n      user_id: data.userId,\n      session_id: data.sessionId,\n      revenue: data.revenue,\n      conversion_type: data.conversionType,\n      metadata: data.metadata,\n      timestamp: new Date().toISOString(),\n      user_agent: navigator.userAgent,\n      referrer: document.referrer,\n      page_url: window.location.href\n    };\n\n    log(`Sending ${eventType} event`, payload);\n\n    let lastError: Error | null = null;\n    \n    for (let attempt = 1; attempt <= (mergedConfig.retryAttempts || 3); attempt++) {\n      try {\n        const response = await fetch(`${mergedConfig.apiBaseUrl}/events`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(payload),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const result = await response.json();\n        log(`${eventType} event tracked successfully`, result);\n        setIsTracking(false);\n        return;\n\n      } catch (err) {\n        lastError = err as Error;\n        log(`Attempt ${attempt} failed for ${eventType} event`, err);\n        \n        if (attempt < (mergedConfig.retryAttempts || 3)) {\n          await new Promise(resolve => \n            setTimeout(resolve, (mergedConfig.retryDelay || 1000) * attempt)\n          );\n        }\n      }\n    }\n\n    // All attempts failed\n    const errorMsg = `Failed to track ${eventType} event after ${mergedConfig.retryAttempts} attempts: ${lastError?.message}`;\n    log(errorMsg, lastError);\n    setError(errorMsg);\n    setIsTracking(false);\n  }, [mergedConfig, log]);\n\n  const trackClick = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('click', data);\n  }, [sendTrackingEvent]);\n\n  const trackView = useCallback(async (data: TrackingData): Promise<void> => {\n    return sendTrackingEvent('view', data);\n  }, [sendTrackingEvent]);\n\n  const trackConversion = useCallback(async (data: TrackingData): Promise<void> => {\n    if (!data.revenue && !data.conversionType) {\n      log('Warning: Conversion tracking without revenue or conversion type', data);\n    }\n    return sendTrackingEvent('conversion', data);\n  }, [sendTrackingEvent, log]);\n\n  return {\n    trackClick,\n    trackView,\n    trackConversion,\n    isTracking,\n    error\n  };\n};\n\n// Utility function to build admesh_link with tracking parameters\nexport const buildAdMeshLink = (\n  baseLink: string, \n  adId: string, \n  additionalParams?: Record<string, string>\n): string => {\n  try {\n    const url = new URL(baseLink);\n    url.searchParams.set('ad_id', adId);\n    url.searchParams.set('utm_source', 'admesh');\n    url.searchParams.set('utm_medium', 'recommendation');\n    \n    if (additionalParams) {\n      Object.entries(additionalParams).forEach(([key, value]) => {\n        url.searchParams.set(key, value);\n      });\n    }\n    \n    return url.toString();\n  } catch (err) {\n    console.warn('[AdMesh] Invalid URL provided to buildAdMeshLink:', baseLink, err);\n    return baseLink;\n  }\n};\n\n// Helper function to extract tracking data from recommendation\nexport const extractTrackingData = (\n  recommendation: { ad_id: string; admesh_link: string; product_id: string },\n  additionalData?: Partial<TrackingData>\n): TrackingData => {\n  return {\n    adId: recommendation.ad_id,\n    admeshLink: recommendation.admesh_link,\n    productId: recommendation.product_id,\n    ...additionalData\n  };\n};\n", "import React, { useCallback, useEffect, useRef } from 'react';\nimport type { AdMeshLinkTrackerProps } from '../types/index';\nimport { useAdMeshTracker } from '../hooks/useAdMeshTracker';\n\nexport const AdMeshLinkTracker: React.FC<AdMeshLinkTrackerProps> = ({\n  adId,\n  admeshLink,\n  productId,\n  children,\n  onClick,\n  trackingData,\n  className\n}) => {\n  const { trackClick, trackView } = useAdMeshTracker();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const hasTrackedView = useRef(false);\n\n  // Track view when component becomes visible\n  useEffect(() => {\n    if (!elementRef.current || hasTrackedView.current) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting && !hasTrackedView.current) {\n            hasTrackedView.current = true;\n            trackView({\n              adId,\n              admeshLink,\n              productId,\n              ...trackingData\n            }).catch(console.error);\n          }\n        });\n      },\n      {\n        threshold: 0.5, // Track when 50% of the element is visible\n        rootMargin: '0px'\n      }\n    );\n\n    observer.observe(elementRef.current);\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [adId, admeshLink, productId, trackingData, trackView]);\n\n  const handleClick = useCallback(async (event: React.MouseEvent) => {\n    // Track the click\n    try {\n      await trackClick({\n        adId,\n        admeshLink,\n        productId,\n        ...trackingData\n      });\n    } catch (error) {\n      console.error('Failed to track click:', error);\n    }\n\n    // Call custom onClick handler if provided\n    if (onClick) {\n      onClick();\n    }\n\n    // If the children contain a link, let the browser handle navigation\n    // Otherwise, navigate programmatically\n    const target = event.target as HTMLElement;\n    const link = target.closest('a');\n    \n    if (!link) {\n      // No link found, navigate programmatically\n      window.open(admeshLink, '_blank', 'noopener,noreferrer');\n    }\n    // If there's a link, let the browser handle it naturally\n  }, [adId, admeshLink, productId, trackingData, trackClick, onClick]);\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      onClick={handleClick}\n      style={{ cursor: 'pointer' }}\n    >\n      {children}\n    </div>\n  );\n};\n\nAdMeshLinkTracker.displayName = 'AdMeshLinkTracker';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshProductCardProps, BadgeType } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshProductCard: React.FC<AdMeshProductCardProps> = ({\n  recommendation,\n  theme,\n  showMatchScore = true,\n  showBadges = true,\n  onClick,\n  className\n}) => {\n  // Generate badges based on recommendation data\n  const badges = useMemo((): BadgeType[] => {\n    const generatedBadges: BadgeType[] = [];\n    \n    // Add Top Match badge for high match scores\n    if (recommendation.intent_match_score >= 0.8) {\n      generatedBadges.push('Top Match');\n    }\n    \n    // Add Free Tier badge\n    if (recommendation.has_free_tier) {\n      generatedBadges.push('Free Tier');\n    }\n    \n    // Add Trial Available badge\n    if (recommendation.trial_days && recommendation.trial_days > 0) {\n      generatedBadges.push('Trial Available');\n    }\n    \n    // Add AI Powered badge (check if AI-related keywords exist)\n    const aiKeywords = ['ai', 'artificial intelligence', 'machine learning', 'ml', 'automation'];\n    const hasAIKeywords = recommendation.keywords?.some(keyword => \n      aiKeywords.some(ai => keyword.toLowerCase().includes(ai))\n    ) || recommendation.title.toLowerCase().includes('ai');\n    \n    if (hasAIKeywords) {\n      generatedBadges.push('AI Powered');\n    }\n    \n    return generatedBadges;\n  }, [recommendation]);\n\n  // Format match score as percentage\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  const cardClasses = classNames(\n    'admesh-component',\n    'admesh-card',\n    'relative p-3 sm:p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow cursor-pointer',\n    className\n  );\n\n  const cardStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n    '--admesh-primary-hover': theme.accentColor + 'dd', // Add some transparency for hover\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      onClick={() => onClick?.(recommendation.ad_id, recommendation.admesh_link)}\n      trackingData={{\n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score\n      }}\n      className={cardClasses}\n    >\n      <div\n        className=\"h-full flex flex-col\"\n        style={cardStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Header with badges and title */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-2\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0\">\n            {showBadges && badges.includes('Top Match') && (\n              <span className=\"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full w-fit\">\n                Top Match\n              </span>\n            )}\n            <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate\">\n              {recommendation.title}\n            </h4>\n          </div>\n\n          <div className=\"flex gap-2 flex-shrink-0\">\n            <button className=\"text-xs sm:text-sm px-2 py-1 rounded-full bg-black text-white hover:bg-gray-800 flex items-center\">\n              Visit\n              <svg className=\"ml-1 h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3\">\n          {recommendation.reason}\n        </p>\n\n        {/* Match Score */}\n        {showMatchScore && typeof recommendation.intent_match_score === \"number\" && (\n          <div className=\"mb-3\">\n            <div className=\"flex items-center justify-between text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1\">\n              <span className=\"font-medium\">Match Score</span>\n              <span className=\"font-semibold text-gray-700 dark:text-gray-300 whitespace-nowrap\">{matchScorePercentage}% match</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden\">\n              <div\n                className=\"bg-black h-1.5 transition-all duration-300 ease-out\"\n                style={{ width: `${matchScorePercentage}%` }}\n              />\n            </div>\n          </div>\n        )}\n\n        <div className=\"flex flex-wrap gap-2 text-xs mb-2\">\n          {recommendation.pricing && (\n            <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n              {recommendation.pricing}\n            </span>\n          )}\n\n          {recommendation.has_free_tier && (\n            <span className=\"flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\" />\n              </svg>\n              Free Tier\n            </span>\n          )}\n\n          {recommendation.trial_days && recommendation.trial_days > 0 && (\n            <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n              <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n              </svg>\n              {recommendation.trial_days}-day trial\n            </span>\n          )}\n        </div>\n\n        {/* Features */}\n        {recommendation.features && recommendation.features.length > 0 && (\n          <div className=\"mb-2\">\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">\n              Features:\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.features.map((feature, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  {feature}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Integrations */}\n        {recommendation.integrations && recommendation.integrations.length > 0 && (\n          <div className=\"mb-2\">\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">\n              Integrates with:\n            </div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {recommendation.integrations.map((integration, j) => (\n                <span\n                  key={j}\n                  className=\"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300\"\n                >\n                  <svg className=\"h-3 w-3 mr-0.5 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                  {integration}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Reviews summary */}\n        {recommendation.reviews_summary && (\n          <div className=\"text-xs text-gray-600 dark:text-gray-400 mt-2\">\n            {recommendation.reviews_summary}\n          </div>\n        )}\n\n        {/* Powered by AdMesh branding */}\n        <div className=\"flex justify-end mt-auto pt-2\">\n          <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n            Powered by AdMesh\n          </span>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n\nAdMeshProductCard.displayName = 'AdMeshProductCard';\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCompareTableProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCompareTable: React.FC<AdMeshCompareTableProps> = ({\n  recommendations,\n  theme,\n  maxProducts = 3,\n  showMatchScores = true,\n  showFeatures = true,\n  onProductClick,\n  className\n}) => {\n  // Limit the number of products to compare\n  const productsToCompare = useMemo(() => {\n    return recommendations.slice(0, maxProducts);\n  }, [recommendations, maxProducts]);\n\n\n\n  const containerClasses = classNames(\n    'admesh-component',\n    'admesh-compare-layout',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (productsToCompare.length === 0) {\n    return (\n      <div className={containerClasses}>\n        <div className=\"p-8 text-center text-gray-500 dark:text-gray-400\">\n          <p>No products to compare</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center gap-2 mb-2\">\n            <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            <h3 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200\">\n              Smart Comparison\n            </h3>\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {productsToCompare.length} intelligent matches found\n          </p>\n        </div>\n\n        {/* Product Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {productsToCompare.map((product, index) => (\n            <AdMeshLinkTracker\n              key={product.product_id || index}\n              adId={product.ad_id}\n              admeshLink={product.admesh_link}\n              productId={product.product_id}\n              onClick={() => onProductClick?.(product.ad_id, product.admesh_link)}\n              className=\"relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow\"\n            >\n              {/* Product Header */}\n              <div className=\"flex justify-between items-start mb-3\">\n                <div className=\"flex items-center gap-2\">\n                  {index === 0 && (\n                    <span className=\"text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full\">\n                      Top Match\n                    </span>\n                  )}\n                  <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n                    #{index + 1}\n                  </span>\n                </div>\n                {showMatchScores && (\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap\">\n                    {Math.round(product.intent_match_score * 100)}% match\n                  </div>\n                )}\n              </div>\n\n              {/* Product Title */}\n              <h4 className=\"font-semibold text-gray-800 dark:text-gray-200 mb-2\">\n                {product.title}\n              </h4>\n\n              {/* Match Score */}\n              {showMatchScores && (\n                <div className=\"mb-3\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    <span>Match Score</span>\n                    <span className=\"whitespace-nowrap\">{Math.round(product.intent_match_score * 100)}% match</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden\">\n                    <div\n                      className=\"bg-black h-1.5\"\n                      style={{ width: `${Math.round(product.intent_match_score * 100)}%` }}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Pricing and Trial Info */}\n              <div className=\"flex flex-wrap gap-2 text-xs mb-3\">\n                {product.pricing && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n                    </svg>\n                    {product.pricing}\n                  </span>\n                )}\n\n                {product.has_free_tier && (\n                  <span className=\"flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400 rounded-full\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7\" />\n                    </svg>\n                    Free Tier\n                  </span>\n                )}\n\n                {product.trial_days && product.trial_days > 0 && (\n                  <span className=\"flex items-center text-gray-600 dark:text-gray-400\">\n                    <svg className=\"h-3 w-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6\" />\n                    </svg>\n                    {product.trial_days}-day trial\n                  </span>\n                )}\n              </div>\n\n              {/* Features */}\n              {showFeatures && product.features && product.features.length > 0 && (\n                <div className=\"mb-3\">\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-1\">\n                    Key Features:\n                  </div>\n                  <div className=\"flex flex-wrap gap-1.5\">\n                    {product.features.slice(0, 4).map((feature, j) => (\n                      <span\n                        key={j}\n                        className=\"text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300\"\n                      >\n                        <svg className=\"h-3 w-3 mr-0.5 inline text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                        {feature}\n                      </span>\n                    ))}\n                    {(product.features.length || 0) > 4 && (\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400 italic\">\n                        +{product.features.length - 4} more\n                      </span>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Visit Button */}\n              <button className=\"w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto\">\n                Visit Offer\n                <svg className=\"h-3 w-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                </svg>\n              </button>\n            </AdMeshLinkTracker>\n          ))}\n        </div>\n\n        {/* Powered by AdMesh branding */}\n        <div className=\"flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50\">\n          <span className=\"flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500\">\n            <svg className=\"w-3 h-3 text-indigo-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clipRule=\"evenodd\" />\n            </svg>\n            <span className=\"font-medium\">Powered by</span>\n            <span className=\"font-semibold text-indigo-600 dark:text-indigo-400\">AdMesh</span>\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nAdMeshCompareTable.displayName = 'AdMeshCompareTable';\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshBadgeProps, BadgeType } from '../types/index';\n\n// Badge type to variant mapping\nconst badgeTypeVariants: Record<BadgeType, string> = {\n  'Top Match': 'primary',\n  'Free Tier': 'success',\n  'AI Powered': 'secondary',\n  'Popular': 'warning',\n  'New': 'primary',\n  'Trial Available': 'success'\n};\n\n// Badge type to icon mapping (using modern Unicode icons)\nconst badgeTypeIcons: Partial<Record<BadgeType, string>> = {\n  'Top Match': '★',\n  'Free Tier': '◆',\n  'AI Powered': '◉',\n  'Popular': '▲',\n  'New': '●',\n  'Trial Available': '◈'\n};\n\nexport const AdMeshBadge: React.FC<AdMeshBadgeProps> = ({\n  type,\n  variant,\n  size = 'md',\n  className\n}) => {\n  const effectiveVariant = variant || badgeTypeVariants[type] || 'secondary';\n  const icon = badgeTypeIcons[type];\n\n  const badgeClasses = classNames(\n    'admesh-component',\n    'admesh-badge',\n    `admesh-badge--${effectiveVariant}`,\n    `admesh-badge--${size}`,\n    className\n  );\n\n  return (\n    <span className={badgeClasses}>\n      {icon && <span className=\"admesh-badge__icon\">{icon}</span>}\n      <span className=\"admesh-badge__text\">{type}</span>\n    </span>\n  );\n};\n\nAdMeshBadge.displayName = 'AdMeshBadge';\n", "import { useEffect } from 'react';\n\n// CSS content as a string - this will be injected automatically\nconst ADMESH_STYLES = `\n/* AdMesh UI SDK Scoped Styles - Smart Recommendations Design */\n.admesh-component {\n  --admesh-primary: #6366f1;\n  --admesh-primary-hover: #4f46e5;\n  --admesh-secondary: #8b5cf6;\n  --admesh-accent: #06b6d4;\n  --admesh-background: #ffffff;\n  --admesh-surface: #ffffff;\n  --admesh-border: #e2e8f0;\n  --admesh-text: #0f172a;\n  --admesh-text-muted: #64748b;\n  --admesh-text-light: #94a3b8;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --admesh-radius: 0.75rem;\n  --admesh-radius-sm: 0.375rem;\n  --admesh-radius-lg: 1rem;\n  --admesh-radius-xl: 1.5rem;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] {\n  --admesh-background: #111827;\n  --admesh-surface: #1f2937;\n  --admesh-border: #374151;\n  --admesh-text: #f9fafb;\n  --admesh-text-muted: #9ca3af;\n  --admesh-text-light: #6b7280;\n  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);\n  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);\n  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);\n  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);\n}\n\n/* Layout Styles */\n.admesh-layout {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n  color: var(--admesh-text);\n  background-color: var(--admesh-background);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  box-shadow: var(--admesh-shadow);\n  border: 1px solid var(--admesh-border);\n}\n\n.admesh-layout__header {\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.admesh-layout__title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__subtitle {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n.admesh-layout__cards-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.admesh-layout__more-indicator {\n  text-align: center;\n  padding: 1rem;\n  color: var(--admesh-text-muted);\n  font-size: 0.875rem;\n}\n\n.admesh-layout__empty {\n  text-align: center;\n  padding: 3rem 1rem;\n}\n\n.admesh-layout__empty-content h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.5rem;\n}\n\n.admesh-layout__empty-content p {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n}\n\n/* Product Card Styles */\n.admesh-product-card {\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  padding: 1.5rem;\n  transition: all 0.2s ease-in-out;\n  position: relative;\n  overflow: hidden;\n}\n\n.admesh-product-card:hover {\n  box-shadow: var(--admesh-shadow-lg);\n  transform: translateY(-2px);\n  border-color: var(--admesh-primary);\n}\n\n.admesh-product-card__header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__title {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: var(--admesh-text);\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.admesh-product-card__reason {\n  font-size: 0.875rem;\n  color: var(--admesh-text-muted);\n  line-height: 1.5;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score {\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__match-score-label {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.75rem;\n  color: var(--admesh-text-muted);\n  margin-bottom: 0.25rem;\n}\n\n.admesh-product-card__match-score-bar {\n  width: 100%;\n  height: 0.375rem;\n  background-color: var(--admesh-border);\n  border-radius: var(--admesh-radius-sm);\n  overflow: hidden;\n}\n\n.admesh-product-card__match-score-fill {\n  height: 100%;\n  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);\n  border-radius: var(--admesh-radius-sm);\n  transition: width 0.3s ease-in-out;\n}\n\n.admesh-product-card__badges {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__badge {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n  padding: 0.25rem 0.5rem;\n  background-color: var(--admesh-primary);\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: var(--admesh-radius-sm);\n}\n\n.admesh-product-card__badge--secondary {\n  background-color: var(--admesh-secondary);\n}\n\n.admesh-product-card__keywords {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-bottom: 1rem;\n}\n\n.admesh-product-card__keyword {\n  padding: 0.125rem 0.375rem;\n  background-color: var(--admesh-border);\n  color: var(--admesh-text-muted);\n  font-size: 0.75rem;\n  border-radius: var(--admesh-radius-sm);\n}\n\n/* Dark mode specific enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__keyword {\n  background-color: #4b5563;\n  color: #d1d5db;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card:hover {\n  border-color: var(--admesh-primary);\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-product-card__button:hover {\n  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));\n}\n\n.admesh-product-card__footer {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 1.5rem;\n}\n\n/* Mobile-specific sidebar improvements */\n@media (max-width: 640px) {\n  .admesh-sidebar {\n    /* Ensure proper mobile viewport handling */\n    height: 100vh !important;\n    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */\n    max-height: 100vh !important;\n    max-height: 100dvh !important;\n    width: 100vw !important;\n    max-width: 90vw !important;\n    overflow: hidden !important;\n  }\n\n  .admesh-sidebar.relative {\n    height: 100% !important;\n    width: 100% !important;\n    max-width: 100% !important;\n  }\n\n  /* Improve touch scrolling */\n  .admesh-sidebar .overflow-y-auto {\n    -webkit-overflow-scrolling: touch !important;\n    overscroll-behavior: contain !important;\n    scroll-behavior: smooth !important;\n  }\n\n  /* Prevent body scroll when sidebar is open */\n  body:has(.admesh-sidebar[data-mobile-open=\"true\"]) {\n    overflow: hidden !important;\n    position: fixed !important;\n    width: 100% !important;\n  }\n}\n\n/* Tablet improvements */\n@media (min-width: 641px) and (max-width: 1024px) {\n  .admesh-sidebar {\n    max-width: 400px !important;\n  }\n}\n\n/* Mobile responsiveness improvements for all components */\n@media (max-width: 640px) {\n  /* Product cards mobile optimization */\n  .admesh-card {\n    padding: 0.75rem !important;\n    margin-bottom: 0.75rem !important;\n  }\n\n  /* Inline recommendations mobile optimization */\n  .admesh-inline-recommendation {\n    padding: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  /* Conversation summary mobile optimization */\n  .admesh-conversation-summary {\n    padding: 1rem !important;\n  }\n\n  /* Percentage text mobile improvements */\n  .admesh-component .text-xs {\n    font-size: 0.75rem !important;\n    line-height: 1rem !important;\n  }\n\n  .admesh-component .text-sm {\n    font-size: 0.875rem !important;\n    line-height: 1.25rem !important;\n  }\n\n  /* Button mobile improvements */\n  .admesh-component button {\n    padding: 0.375rem 0.75rem !important;\n    font-size: 0.75rem !important;\n    min-height: 2rem !important;\n    touch-action: manipulation !important;\n  }\n\n  /* Badge mobile improvements */\n  .admesh-component .rounded-full {\n    padding: 0.25rem 0.5rem !important;\n    font-size: 0.625rem !important;\n    line-height: 1rem !important;\n  }\n\n  /* Progress bar mobile improvements */\n  .admesh-component .bg-gray-200,\n  .admesh-component .bg-slate-600 {\n    height: 0.25rem !important;\n  }\n\n  /* Flex layout mobile improvements */\n  .admesh-component .flex {\n    flex-wrap: wrap !important;\n  }\n\n  .admesh-component .gap-2 {\n    gap: 0.375rem !important;\n  }\n\n  .admesh-component .gap-3 {\n    gap: 0.5rem !important;\n  }\n}\n\n.admesh-product-card__button {\n  display: inline-flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1.5rem;\n  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: none;\n  border-radius: var(--admesh-radius);\n  cursor: pointer;\n  transition: all 0.2s ease-in-out;\n  text-decoration: none;\n}\n\n.admesh-product-card__button:hover {\n  transform: translateY(-1px);\n  box-shadow: var(--admesh-shadow-lg);\n}\n\n/* Utility Classes */\n.admesh-text-xs { font-size: 0.75rem; }\n.admesh-text-sm { font-size: 0.875rem; }\n.admesh-text-base { font-size: 1rem; }\n.admesh-text-lg { font-size: 1.125rem; }\n.admesh-text-xl { font-size: 1.25rem; }\n\n.admesh-font-medium { font-weight: 500; }\n.admesh-font-semibold { font-weight: 600; }\n.admesh-font-bold { font-weight: 700; }\n\n.admesh-text-muted { color: var(--admesh-text-muted); }\n\n/* Comparison Table Styles */\n.admesh-compare-table {\n  width: 100%;\n  border-collapse: collapse;\n  background-color: var(--admesh-surface);\n  border: 1px solid var(--admesh-border);\n  border-radius: var(--admesh-radius);\n  overflow: hidden;\n}\n\n.admesh-compare-table th,\n.admesh-compare-table td {\n  padding: 0.75rem;\n  text-align: left;\n  border-bottom: 1px solid var(--admesh-border);\n}\n\n.admesh-compare-table th {\n  background-color: var(--admesh-background);\n  font-weight: 600;\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table td {\n  color: var(--admesh-text);\n  font-size: 0.875rem;\n}\n\n.admesh-compare-table tr:hover {\n  background-color: var(--admesh-border);\n}\n\n/* Dark mode table enhancements */\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table th {\n  background-color: #374151;\n}\n\n.admesh-component[data-admesh-theme=\"dark\"] .admesh-compare-table tr:hover {\n  background-color: #4b5563;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .admesh-layout {\n    padding: 1rem;\n  }\n\n  .admesh-layout__cards-grid {\n    grid-template-columns: 1fr;\n    gap: 0.75rem;\n  }\n\n  .admesh-product-card {\n    padding: 1rem;\n  }\n\n  .admesh-compare-table {\n    font-size: 0.75rem;\n  }\n\n  .admesh-compare-table th,\n  .admesh-compare-table td {\n    padding: 0.5rem;\n  }\n}\n`;\n\nlet stylesInjected = false;\n\nexport const useAdMeshStyles = () => {\n  useEffect(() => {\n    if (stylesInjected) return;\n\n    // Create and inject styles\n    const styleElement = document.createElement('style');\n    styleElement.id = 'admesh-ui-sdk-styles';\n    styleElement.textContent = ADMESH_STYLES;\n    \n    // Check if styles are already injected\n    if (!document.getElementById('admesh-ui-sdk-styles')) {\n      document.head.appendChild(styleElement);\n      stylesInjected = true;\n    }\n\n    // Cleanup function\n    return () => {\n      const existingStyle = document.getElementById('admesh-ui-sdk-styles');\n      if (existingStyle && document.head.contains(existingStyle)) {\n        document.head.removeChild(existingStyle);\n        stylesInjected = false;\n      }\n    };\n  }, []);\n};\n", "import React, { useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshLayoutProps, IntentType, AdMeshRecommendation } from '../types/index';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCompareTable } from './AdMeshCompareTable';\nimport { useAdMeshStyles } from '../hooks/useAdMeshStyles';\n\n// Layout selection logic based on intent type and data characteristics\nconst selectOptimalLayout = (\n  recommendations: AdMeshRecommendation[],\n  intentType?: IntentType,\n  autoLayout?: boolean\n): 'cards' | 'compare' | 'list' => {\n  if (!autoLayout && intentType) {\n    // Use explicit intent type mapping\n    switch (intentType) {\n      case 'compare_products':\n        return 'compare';\n      case 'best_for_use_case':\n      case 'trial_demo':\n      case 'budget_conscious':\n        return 'cards';\n      default:\n        return 'cards';\n    }\n  }\n\n  // Auto-layout logic based on data characteristics\n  const productCount = recommendations.length;\n  \n  // If we have 2-4 products with features, use comparison table\n  if (productCount >= 2 && productCount <= 4) {\n    const hasFeatures = recommendations.some(rec => rec.features && rec.features.length > 0);\n    const hasPricing = recommendations.some(rec => rec.pricing);\n    \n    if (hasFeatures || hasPricing) {\n      return 'compare';\n    }\n  }\n  \n  // Default to cards layout\n  return 'cards';\n};\n\nexport const AdMeshLayout: React.FC<AdMeshLayoutProps> = ({\n  recommendations,\n  intentType,\n  theme,\n  maxDisplayed = 6,\n  showMatchScores = true,\n  showFeatures = true,\n  autoLayout = true,\n  onProductClick,\n  onTrackView,\n  className\n}) => {\n  // Auto-inject styles\n  useAdMeshStyles();\n\n  // Limit recommendations to display\n  const displayRecommendations = useMemo(() => {\n    return recommendations.slice(0, maxDisplayed);\n  }, [recommendations, maxDisplayed]);\n\n  // Determine the optimal layout\n  const layout = useMemo(() => {\n    return selectOptimalLayout(displayRecommendations, intentType, autoLayout);\n  }, [displayRecommendations, intentType, autoLayout]);\n\n  const containerClasses = classNames(\n    'admesh-component',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (displayRecommendations.length === 0) {\n    return (\n      <div className={containerClasses}>\n        <div className=\"admesh-layout__empty\">\n          <div className=\"admesh-layout__empty-content\">\n            <div className=\"flex items-center justify-center mb-3\">\n              <svg className=\"w-8 h-8 text-indigo-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <h3 className=\"admesh-text-lg admesh-font-semibold admesh-text-muted\">\n              No smart recommendations found\n            </h3>\n            <p className=\"admesh-text-sm admesh-text-muted\">\n              Try refining your search or check back later for new matches.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div \n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {layout === 'compare' ? (\n        <AdMeshCompareTable\n          recommendations={displayRecommendations}\n          theme={theme}\n          maxProducts={Math.min(displayRecommendations.length, 4)}\n          showMatchScores={showMatchScores}\n          showFeatures={showFeatures}\n          onProductClick={onProductClick}\n        />\n      ) : (\n        <div className=\"space-y-4\">\n          {displayRecommendations.map((recommendation, index) => (\n            <AdMeshProductCard\n              key={recommendation.product_id || recommendation.ad_id || index}\n              recommendation={recommendation}\n              theme={theme}\n              showMatchScore={showMatchScores}\n              showBadges={true}\n              maxKeywords={3}\n              onClick={onProductClick}\n              onTrackView={onTrackView}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nAdMeshLayout.displayName = 'AdMeshLayout';\n", "import React from 'react';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport interface AdMeshSimpleAdProps {\n  /** Product recommendation data */\n  recommendation: AdMeshRecommendation;\n  /** Theme configuration */\n  theme?: AdMeshTheme;\n  /** Custom CSS class name */\n  className?: string;\n  /** Callback when the ad is clicked */\n  onClick?: (adId: string, admeshLink: string) => void;\n  /** Show \"powered by AdMesh\" branding */\n  showPoweredBy?: boolean;\n  /** Variation style for the ad */\n  variation?: 'question' | 'statement';\n}\n\n/**\n * AdMeshSimpleAd - A one-line ad unit with product name, simple description, and link\n *\n * Perfect for clean, unobtrusive product recommendations that blend naturally into content.\n * Supports two variations:\n * - Question: \"Looking for payment solutions for your business? Try Stripe\"\n * - Statement: \"Stripe is offering best payment solutions for small business, visit\"\n */\nexport const AdMeshSimpleAd: React.FC<AdMeshSimpleAdProps> = ({\n  recommendation,\n  theme = { mode: 'light' },\n  className = '',\n  onClick,\n  showPoweredBy = true,\n  variation = 'question'\n}) => {\n  const handleClick = () => {\n    onClick?.(recommendation.ad_id, recommendation.admesh_link);\n  };\n\n  // Extract product name from title\n  const productName = recommendation.title;\n  \n  // Generate simple description based on variation\n  const getAdText = () => {\n    if (variation === 'question') {\n      // Extract category from keywords or use generic term\n      const category = recommendation.keywords?.[0]?.toLowerCase() || 'solutions';\n      return `Looking for ${category} for your business? Try ${productName}`;\n    } else {\n      // Statement variation\n      const benefit = recommendation.reason?.split('.')[0] || `offering best solutions for your business`;\n      return `${productName} is ${benefit.toLowerCase()}, visit`;\n    }\n  };\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      onClick={handleClick}\n      trackingData={{\n        title: recommendation.title,\n        variation: variation,\n        component: 'simple_ad'\n      }}\n      className={`admesh-simple-ad ${className}`}\n    >\n      <div\n        data-admesh-theme={theme.mode}\n        style={{\n          padding: '12px 16px',\n          borderRadius: '8px',\n          border: `1px solid ${theme.mode === 'dark' ? '#374151' : '#e5e7eb'}`,\n          backgroundColor: theme.mode === 'dark' ? '#1f2937' : '#ffffff',\n          fontSize: '14px',\n          lineHeight: '1.5',\n          fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n        }}\n      >\n        {/* Main ad content */}\n        <div style={{ marginBottom: showPoweredBy ? '8px' : '0' }}>\n          <span\n            style={{\n              color: theme.mode === 'dark' ? '#f3f4f6' : '#374151',\n              marginRight: '4px'\n            }}\n          >\n            {getAdText()}\n          </span>\n          <span\n            style={{\n              color: theme.accentColor || '#2563eb',\n              textDecoration: 'underline',\n              cursor: 'pointer',\n              fontSize: 'inherit',\n              fontFamily: 'inherit'\n            }}\n          >\n            {productName}\n          </span>\n        </div>\n\n        {/* Powered by AdMesh */}\n        {showPoweredBy && (\n          <div\n            style={{\n              fontSize: '11px',\n              color: theme.mode === 'dark' ? '#9ca3af' : '#6b7280',\n              textAlign: 'right' as const\n            }}\n          >\n            powered by <strong>AdMesh</strong>\n          </div>\n        )}\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n\nexport default AdMeshSimpleAd;\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshInlineRecommendationProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshInlineRecommendation: React.FC<AdMeshInlineRecommendationProps> = ({\n  recommendation,\n  theme,\n  compact = false,\n  showReason = true,\n  onClick,\n  className\n}) => {\n  const matchScorePercentage = Math.round(recommendation.intent_match_score * 100);\n\n  const containerClasses = classNames(\n    'admesh-inline-recommendation',\n    'group cursor-pointer transition-all duration-200',\n    {\n      'p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700': !compact,\n      'p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30': compact,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <AdMeshLinkTracker\n      adId={recommendation.ad_id}\n      admeshLink={recommendation.admesh_link}\n      productId={recommendation.product_id}\n      onClick={() => onClick?.(recommendation.ad_id, recommendation.admesh_link)}\n      trackingData={{\n        title: recommendation.title,\n        matchScore: recommendation.intent_match_score\n      }}\n      className={containerClasses}\n    >\n      <div\n        className=\"flex items-start gap-3\"\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n      >\n        {/* Icon/Badge */}\n        <div className=\"flex-shrink-0 mt-0.5\">\n          {recommendation.intent_match_score >= 0.8 ? (\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n          ) : (\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row\">\n            <h4 className={classNames(\n              'font-medium transition-colors duration-200 flex-shrink-0',\n              'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n              'cursor-pointer hover:underline',\n              compact ? 'text-sm sm:text-base' : 'text-base sm:text-lg'\n            )}>\n              {recommendation.title}\n            </h4>\n            \n            {/* Match score badge */}\n            {recommendation.intent_match_score >= 0.7 && (\n              <span className={classNames(\n                'inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap',\n                recommendation.intent_match_score >= 0.8\n                  ? 'bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100'\n                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'\n              )}>\n                {matchScorePercentage}% match\n              </span>\n            )}\n          </div>\n\n          {/* Reason/Description */}\n          {showReason && recommendation.reason && (\n            <p className={classNames(\n              'text-gray-600 dark:text-gray-400 line-clamp-2',\n              compact ? 'text-xs' : 'text-sm'\n            )}>\n              {recommendation.reason}\n            </p>\n          )}\n\n          {/* Features/Keywords */}\n          {!compact && recommendation.keywords && recommendation.keywords.length > 0 && (\n            <div className=\"flex flex-wrap gap-1 mt-2\">\n              {recommendation.keywords.slice(0, 3).map((keyword, index) => (\n                <span\n                  key={index}\n                  className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300\"\n                >\n                  {keyword}\n                </span>\n              ))}\n              {recommendation.keywords.length > 3 && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  +{recommendation.keywords.length - 3} more\n                </span>\n              )}\n            </div>\n          )}\n\n          {/* Pricing/Trial info */}\n          {!compact && (recommendation.has_free_tier || recommendation.trial_days) && (\n            <div className=\"flex items-center gap-2 mt-2\">\n              {recommendation.has_free_tier && (\n                <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100\">\n                  Free tier\n                </span>\n              )}\n              {recommendation.trial_days && recommendation.trial_days > 0 && (\n                <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400\">\n                  {recommendation.trial_days}-day trial\n                </span>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* Arrow indicator */}\n        <div className=\"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <svg \n            className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" \n            fill=\"none\" \n            stroke=\"currentColor\" \n            viewBox=\"0 0 24 24\"\n          >\n            <path \n              strokeLinecap=\"round\" \n              strokeLinejoin=\"round\" \n              strokeWidth={2} \n              d=\"M9 5l7 7-7 7\" \n            />\n          </svg>\n        </div>\n      </div>\n    </AdMeshLinkTracker>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationSummaryProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshConversationSummary: React.FC<AdMeshConversationSummaryProps> = ({\n  recommendations,\n  conversationSummary,\n  theme,\n  showTopRecommendations = 3,\n  onRecommendationClick,\n  onStartNewConversation,\n  className\n}) => {\n  const topRecommendations = recommendations\n    .sort((a, b) => b.intent_match_score - a.intent_match_score)\n    .slice(0, showTopRecommendations);\n\n  const containerClasses = classNames(\n    'admesh-conversation-summary',\n    'bg-white dark:bg-black',\n    'rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6',\n    'font-sans', // Standardize font family\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Header */}\n      <div className=\"flex items-center gap-3 mb-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n        </div>\n        <div className=\"min-w-0 flex-1\">\n          <h3 className=\"text-base sm:text-lg font-semibold text-black dark:text-white\">\n            Conversation Summary\n          </h3>\n          <p className=\"text-xs sm:text-sm text-gray-600 dark:text-gray-300\">\n            Here's what we discussed and found for you\n          </p>\n        </div>\n      </div>\n\n      {/* Summary Text */}\n      <div className=\"mb-6\">\n        <div className=\"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\n          <p className=\"text-gray-800 dark:text-gray-200 leading-relaxed\">\n            {conversationSummary}\n          </p>\n        </div>\n      </div>\n\n      {/* Top Recommendations */}\n      {topRecommendations.length > 0 && (\n        <div className=\"mb-6\">\n          <div className=\"flex items-center gap-2 mb-3\">\n            <svg className=\"w-5 h-5 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <h4 className=\"font-medium text-black dark:text-white\">\n              Top Recommendations\n            </h4>\n          </div>\n          \n          <div className=\"space-y-2\">\n            {topRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                {/* Ranking badge */}\n                <div className=\"absolute -left-2 top-2 z-10\">\n                  <div className={classNames(\n                    'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',\n                    index === 0 ? 'bg-black dark:bg-white text-white dark:text-black' :\n                    index === 1 ? 'bg-gray-600 dark:bg-gray-400 text-white dark:text-black' :\n                    'bg-gray-800 dark:bg-gray-200 text-white dark:text-black'\n                  )}>\n                    {index + 1}\n                  </div>\n                </div>\n                \n                <div className=\"ml-4\">\n                  <AdMeshInlineRecommendation\n                    recommendation={recommendation}\n                    theme={theme}\n                    compact={true}\n                    showReason={true}\n                    onClick={onRecommendationClick}\n                  />\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Additional Insights */}\n      {recommendations.length > showTopRecommendations && (\n        <div className=\"mb-6\">\n          <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center gap-2\">\n              <svg className=\"w-4 h-4 text-black dark:text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-800 dark:text-gray-200\">\n                {recommendations.length - showTopRecommendations} additional recommendation{recommendations.length - showTopRecommendations > 1 ? 's' : ''} available\n              </span>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex flex-col sm:flex-row gap-3\">\n        {onStartNewConversation && (\n          <button\n            onClick={onStartNewConversation}\n            className=\"flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n            Start New Conversation\n          </button>\n        )}\n        \n        <button\n          onClick={() => {\n            if (topRecommendations.length > 0) {\n              onRecommendationClick?.(topRecommendations[0].ad_id, topRecommendations[0].admesh_link);\n            }\n          }}\n          className=\"flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n          </svg>\n          View Top Pick\n        </button>\n      </div>\n\n      {/* Powered by AdMesh */}\n      <div className=\"flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n          Powered by AdMesh\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationReferenceProps } from '../types/index';\nimport { AdMeshLinkTracker } from './AdMeshLinkTracker';\n\nexport const AdMeshCitationReference: React.FC<AdMeshCitationReferenceProps> = ({\n  recommendation,\n  citationNumber,\n  citationStyle = 'numbered',\n  theme,\n  showTooltip = true,\n  onClick,\n  onHover,\n  className\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n    onHover?.(recommendation);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n  };\n\n  const handleClick = () => {\n    onClick?.(recommendation.ad_id, recommendation.admesh_link);\n  };\n\n  // Generate citation display based on style\n  const getCitationDisplay = () => {\n    switch (citationStyle) {\n      case 'bracketed':\n        return `[${citationNumber}]`;\n      case 'superscript':\n        return citationNumber.toString();\n      case 'numbered':\n      default:\n        return citationNumber.toString();\n    }\n  };\n\n  const citationClasses = classNames(\n    'admesh-citation-reference',\n    'inline-flex items-center justify-center',\n    'cursor-pointer transition-all duration-200',\n    'text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300',\n    'font-medium',\n    {\n      // Numbered style (default)\n      'w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50': citationStyle === 'numbered',\n      \n      // Bracketed style\n      'px-1 text-sm hover:underline': citationStyle === 'bracketed',\n      \n      // Superscript style\n      'text-xs align-super hover:underline': citationStyle === 'superscript',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <span className=\"relative inline-block\">\n      <AdMeshLinkTracker\n        adId={recommendation.ad_id}\n        admeshLink={recommendation.admesh_link}\n        productId={recommendation.product_id}\n        onClick={handleClick}\n        trackingData={{\n          title: recommendation.title,\n          matchScore: recommendation.intent_match_score,\n          citationNumber,\n          citationStyle\n        }}\n        className={citationClasses}\n      >\n        <span\n          style={containerStyle}\n          data-admesh-theme={theme?.mode}\n          onMouseEnter={handleMouseEnter}\n          onMouseLeave={handleMouseLeave}\n        >\n          {getCitationDisplay()}\n        </span>\n      </AdMeshLinkTracker>\n\n      {/* Tooltip */}\n      {showTooltip && isHovered && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50\">\n          <div className=\"bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs\">\n            <div className=\"font-semibold mb-1\">{recommendation.title}</div>\n            {recommendation.reason && (\n              <div className=\"text-gray-300 dark:text-gray-600 text-xs\">\n                {recommendation.reason.length > 100 \n                  ? `${recommendation.reason.substring(0, 100)}...` \n                  : recommendation.reason\n                }\n              </div>\n            )}\n            {recommendation.intent_match_score >= 0.7 && (\n              <div className=\"text-green-400 dark:text-green-600 text-xs mt-1\">\n                {Math.round(recommendation.intent_match_score * 100)}% match\n              </div>\n            )}\n            <div className=\"text-gray-400 dark:text-gray-500 text-xs mt-1 italic\">\n              Click to visit product page\n            </div>\n            {/* Tooltip arrow */}\n            <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100\"></div>\n          </div>\n        </div>\n      )}\n    </span>\n  );\n};\n", "import React, { useState, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshCitationUnitProps, AdMeshRecommendation } from '../types/index';\nimport { AdMeshCitationReference } from './AdMeshCitationReference';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\n\nexport const AdMeshCitationUnit: React.FC<AdMeshCitationUnitProps> = ({\n  recommendations,\n  conversationText,\n  theme,\n  showCitationList = true,\n  citationStyle = 'numbered',\n  onRecommendationClick,\n  onCitationHover,\n  className\n}) => {\n  const [hoveredRecommendation, setHoveredRecommendation] = useState<AdMeshRecommendation | null>(null);\n\n  // Process conversation text to insert citations\n  const processedContent = useMemo(() => {\n    if (!conversationText || recommendations.length === 0) {\n      return { text: conversationText, citationMap: new Map() };\n    }\n\n    let processedText = conversationText;\n    const citationMap = new Map();\n    \n    // Sort recommendations by intent match score (highest first)\n    const sortedRecommendations = [...recommendations]\n      .sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Find mentions of product titles in the text and replace with citations\n    sortedRecommendations.forEach((recommendation, index) => {\n      const citationNumber = index + 1;\n      const title = recommendation.title;\n      \n      // Create citation reference\n      citationMap.set(citationNumber, recommendation);\n      \n      // Look for exact title matches (case insensitive)\n      const titleRegex = new RegExp(`\\\\b${title.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n      \n      // Replace first occurrence with citation\n      if (titleRegex.test(processedText)) {\n        processedText = processedText.replace(titleRegex, (match) => {\n          return `${match}{{CITATION_${citationNumber}}}`;\n        });\n      } else {\n        // If no exact match, try to find a good insertion point\n        // Look for related keywords or add at the end of relevant sentences\n        const keywords = recommendation.keywords || [];\n        let inserted = false;\n        \n        for (const keyword of keywords) {\n          const keywordRegex = new RegExp(`\\\\b${keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}\\\\b`, 'gi');\n          if (keywordRegex.test(processedText) && !inserted) {\n            processedText = processedText.replace(keywordRegex, (match) => {\n              inserted = true;\n              return `${match}{{CITATION_${citationNumber}}}`;\n            });\n            break;\n          }\n        }\n        \n        // If still no insertion point found, add citation at the end\n        if (!inserted) {\n          processedText += `{{CITATION_${citationNumber}}}`;\n        }\n      }\n    });\n\n    return { text: processedText, citationMap };\n  }, [conversationText, recommendations]);\n\n  // Render text with embedded citations\n  const renderTextWithCitations = () => {\n    const { text, citationMap } = processedContent;\n    const parts = text.split(/(\\{\\{CITATION_\\d+\\}\\})/);\n\n    return parts.map((part, index) => {\n      const citationMatch = part.match(/\\{\\{CITATION_(\\d+)\\}\\}/);\n\n      if (citationMatch) {\n        const citationNumber = parseInt(citationMatch[1]);\n        const recommendation = citationMap.get(citationNumber);\n\n        if (recommendation) {\n          return (\n            <AdMeshCitationReference\n              key={`citation-${citationNumber}-${index}`}\n              recommendation={recommendation}\n              citationNumber={citationNumber}\n              citationStyle={citationStyle}\n              theme={theme}\n              showTooltip={true}\n              onClick={onRecommendationClick}\n              onHover={(rec) => {\n                setHoveredRecommendation(rec);\n                onCitationHover?.(rec);\n              }}\n            />\n          );\n        }\n      }\n\n      return <span key={index}>{part}</span>;\n    });\n  };\n\n  const containerClasses = classNames(\n    'admesh-citation-unit',\n    'space-y-4',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Main conversation text with embedded citations */}\n      <div className=\"admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed\">\n        {renderTextWithCitations()}\n      </div>\n\n      {/* Citation list/references */}\n      {showCitationList && recommendations.length > 0 && (\n        <div className=\"admesh-citation-list\">\n          <div className=\"border-t border-gray-200 dark:border-slate-700 pt-4\">\n            <h4 className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\" />\n              </svg>\n              References\n            </h4>\n            \n            <div className=\"space-y-2\">\n              {recommendations\n                .sort((a, b) => b.intent_match_score - a.intent_match_score)\n                .map((recommendation, index) => (\n                  <div \n                    key={recommendation.ad_id || index}\n                    className={classNames(\n                      'flex items-start gap-3 p-2 rounded-lg transition-colors duration-200',\n                      {\n                        'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800': \n                          hoveredRecommendation?.ad_id === recommendation.ad_id,\n                        'hover:bg-gray-50 dark:hover:bg-slate-800/50': \n                          hoveredRecommendation?.ad_id !== recommendation.ad_id\n                      }\n                    )}\n                  >\n                    {/* Citation number */}\n                    <div className=\"flex-shrink-0 mt-1\">\n                      <AdMeshCitationReference\n                        recommendation={recommendation}\n                        citationNumber={index + 1}\n                        citationStyle={citationStyle}\n                        theme={theme}\n                        showTooltip={false}\n                        onClick={onRecommendationClick}\n                      />\n                    </div>\n                    \n                    {/* Recommendation details */}\n                    <div className=\"flex-1 min-w-0\">\n                      <AdMeshInlineRecommendation\n                        recommendation={recommendation}\n                        theme={theme}\n                        compact={true}\n                        showReason={false}\n                        onClick={onRecommendationClick}\n                      />\n                    </div>\n                  </div>\n                ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshConversationalUnitProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshConversationSummary } from './AdMeshConversationSummary';\nimport { AdMeshProductCard } from './AdMeshProductCard';\nimport { AdMeshCitationUnit } from './AdMeshCitationUnit';\n\nexport const AdMeshConversationalUnit: React.FC<AdMeshConversationalUnitProps> = ({\n  recommendations,\n  config,\n  theme,\n  conversationSummary,\n  sessionId,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(config.autoShow !== false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  useEffect(() => {\n    if (config.delayMs && config.delayMs > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, config.delayMs);\n      return () => clearTimeout(timer);\n    } else {\n      setHasAnimated(true);\n    }\n  }, [config.delayMs]);\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const maxRecommendations = config.maxRecommendations || 3;\n  const displayRecommendations = recommendations.slice(0, maxRecommendations);\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    onRecommendationClick?.(adId, admeshLink);\n  };\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Render based on display mode\n  const renderContent = () => {\n    switch (config.displayMode) {\n      case 'summary':\n        return conversationSummary ? (\n          <AdMeshConversationSummary\n            recommendations={displayRecommendations}\n            conversationSummary={conversationSummary}\n            theme={theme}\n            showTopRecommendations={maxRecommendations}\n            onRecommendationClick={handleRecommendationClick}\n            onStartNewConversation={onDismiss}\n          />\n        ) : null;\n\n      case 'inline':\n        return (\n          <div className=\"space-y-2\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'minimal':\n        return displayRecommendations.length > 0 ? (\n          <div className=\"admesh-minimal-unit\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {displayRecommendations.length} intelligent match{displayRecommendations.length > 1 ? 'es' : ''} found\n              </span>\n            </div>\n            <AdMeshInlineRecommendation\n              recommendation={displayRecommendations[0]}\n              theme={theme}\n              compact={true}\n              showReason={false}\n              onClick={handleRecommendationClick}\n            />\n            {displayRecommendations.length > 1 && (\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                +{displayRecommendations.length - 1} more recommendation{displayRecommendations.length > 2 ? 's' : ''}\n              </div>\n            )}\n          </div>\n        ) : null;\n\n      case 'citation':\n        return conversationSummary ? (\n          <AdMeshCitationUnit\n            recommendations={displayRecommendations}\n            conversationText={conversationSummary}\n            theme={theme}\n            showCitationList={true}\n            citationStyle=\"numbered\"\n            onRecommendationClick={handleRecommendationClick}\n          />\n        ) : null;\n\n      case 'floating':\n        return (\n          <div className=\"admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4\">\n            <div className=\"flex justify-between items-start mb-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <span className=\"text-sm font-semibold text-gray-800 dark:text-gray-200\">\n                  Recommended for you\n                </span>\n              </div>\n              {onDismiss && (\n                <button\n                  onClick={handleDismiss}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                  aria-label=\"Dismiss recommendations\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              )}\n            </div>\n            <div className=\"space-y-2\">\n              {displayRecommendations.map((recommendation, index) => (\n                <AdMeshInlineRecommendation\n                  key={recommendation.ad_id || index}\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={handleRecommendationClick}\n                />\n              ))}\n            </div>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {displayRecommendations.map((recommendation, index) => (\n              <AdMeshProductCard\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                showMatchScore={false}\n                showBadges={true}\n                onClick={handleRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-conversational-unit',\n    'transition-all duration-300 ease-in-out',\n    {\n      'opacity-0 translate-y-2': !hasAnimated,\n      'opacity-100 translate-y-0': hasAnimated,\n      'fixed bottom-4 right-4 max-w-sm z-50': config.displayMode === 'floating',\n      'my-3': config.displayMode === 'inline',\n      'mt-4 pt-4 border-t border-gray-200 dark:border-slate-700': config.displayMode === 'summary',\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n      data-admesh-context={config.context}\n      data-session-id={sessionId}\n    >\n      {renderContent()}\n      \n      {/* Powered by AdMesh branding */}\n      {config.showPoweredBy !== false && (\n        <div className=\"flex justify-end mt-2\">\n          <span className=\"text-xs text-gray-400 dark:text-gray-500\">\n            Powered by AdMesh\n          </span>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatMessageProps } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport const AdMeshChatMessage: React.FC<AdMeshChatMessageProps> = ({\n  message,\n  theme,\n  onRecommendationClick,\n  className\n}) => {\n  const isUser = message.role === 'user';\n  const isAssistant = message.role === 'assistant';\n\n  const messageClasses = classNames(\n    'admesh-chat-message',\n    'flex items-start gap-3',\n    {\n      'flex-row-reverse': isUser,\n    },\n    className\n  );\n\n  const bubbleClasses = classNames(\n    'max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm',\n    {\n      'bg-gradient-to-r from-blue-600 to-indigo-600 text-white': isUser,\n      'bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100': isAssistant,\n      'bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100': message.role === 'system',\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const formatTime = (timestamp: Date) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  return (\n    <div\n      className={messageClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Avatar */}\n      {!isUser && (\n        <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {isUser && (\n        <div className=\"w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0\">\n          <svg className=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n          </svg>\n        </div>\n      )}\n\n      {/* Message Content */}\n      <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'} flex-1`}>\n        {/* Message Bubble */}\n        <div className={bubbleClasses}>\n          <div className=\"whitespace-pre-wrap break-words\">\n            {message.content}\n          </div>\n        </div>\n\n        {/* Timestamp */}\n        <div className={classNames(\n          'text-xs text-gray-500 dark:text-gray-400 mt-1',\n          { 'text-right': isUser }\n        )}>\n          {formatTime(message.timestamp)}\n        </div>\n\n        {/* Recommendations */}\n        {message.recommendations && message.recommendations.length > 0 && (\n          <div className=\"mt-3 w-full max-w-lg\">\n            {/* Recommendations Header */}\n            <div className=\"flex items-center gap-2 mb-3\">\n              <svg className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {message.recommendations.length} recommendation{message.recommendations.length > 1 ? 's' : ''} found\n              </span>\n            </div>\n\n            {/* Recommendations Display */}\n            <AdMeshConversationalUnit\n              recommendations={message.recommendations}\n              config={{\n                displayMode: 'inline',\n                context: 'chat',\n                maxRecommendations: 3,\n                showPoweredBy: false,\n                autoShow: true,\n                delayMs: 300\n              }}\n              theme={theme}\n              onRecommendationClick={onRecommendationClick}\n              className=\"bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700\"\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useRef, KeyboardEvent } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInputProps } from '../types/index';\n\nexport const AdMeshChatInput: React.FC<AdMeshChatInputProps> = ({\n  placeholder = \"Type your message...\",\n  disabled = false,\n  suggestions = [],\n  theme,\n  onSendMessage,\n  className\n}) => {\n  const [message, setMessage] = useState('');\n  const [showSuggestions, setShowSuggestions] = useState(false);\n  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);\n  const inputRef = useRef<HTMLTextAreaElement>(null);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    const value = e.target.value;\n    setMessage(value);\n\n    // Filter suggestions based on input\n    if (value.trim() && suggestions.length > 0) {\n      const filtered = suggestions.filter(suggestion =>\n        suggestion.toLowerCase().includes(value.toLowerCase())\n      );\n      setFilteredSuggestions(filtered);\n      setShowSuggestions(filtered.length > 0);\n    } else {\n      setShowSuggestions(false);\n    }\n\n    // Auto-resize textarea\n    if (inputRef.current) {\n      inputRef.current.style.height = 'auto';\n      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;\n    }\n  };\n\n  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSend();\n    }\n  };\n\n  const handleSend = () => {\n    const trimmedMessage = message.trim();\n    if (trimmedMessage && !disabled && onSendMessage) {\n      onSendMessage(trimmedMessage);\n      setMessage('');\n      setShowSuggestions(false);\n      \n      // Reset textarea height\n      if (inputRef.current) {\n        inputRef.current.style.height = 'auto';\n      }\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setMessage(suggestion);\n    setShowSuggestions(false);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-chat-input',\n    'relative',\n    className\n  );\n\n  const inputClasses = classNames(\n    'w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600',\n    'bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100',\n    'placeholder-gray-500 dark:placeholder-gray-400',\n    'focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n    'transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600',\n    'pr-12 pl-4 py-3 text-sm leading-5',\n    {\n      'opacity-50 cursor-not-allowed': disabled,\n    }\n  );\n\n  const sendButtonClasses = classNames(\n    'absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200',\n    'flex items-center justify-center',\n    {\n      'bg-blue-600 hover:bg-blue-700 text-white': message.trim() && !disabled,\n      'bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed': !message.trim() || disabled,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Suggestions Dropdown */}\n      {showSuggestions && filteredSuggestions.length > 0 && (\n        <div className=\"absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10\">\n          {filteredSuggestions.slice(0, 5).map((suggestion, index) => (\n            <button\n              key={index}\n              onClick={() => handleSuggestionClick(suggestion)}\n              className=\"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg\"\n            >\n              {suggestion}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Input Container */}\n      <div className=\"relative\">\n        <textarea\n          ref={inputRef}\n          value={message}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          disabled={disabled}\n          rows={1}\n          className={inputClasses}\n          style={{ minHeight: '44px', maxHeight: '120px' }}\n        />\n\n        {/* Send Button */}\n        <button\n          onClick={handleSend}\n          disabled={!message.trim() || disabled}\n          className={sendButtonClasses}\n          aria-label=\"Send message\"\n        >\n          <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n          </svg>\n        </button>\n      </div>\n\n      {/* Helper Text */}\n      <div className=\"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400\">\n        <span>Press Enter to send, Shift+Enter for new line</span>\n        <span className={classNames(\n          'transition-opacity duration-200',\n          { 'opacity-0': message.length < 100 }\n        )}>\n          {message.length}/500\n        </span>\n      </div>\n    </div>\n  );\n};\n", "import React, { useEffect, useRef } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshChatInterfaceProps } from '../types/index';\nimport { AdMeshChatMessage } from './AdMeshChatMessage';\nimport { AdMeshChatInput } from './AdMeshChatInput';\n\nexport const AdMeshChatInterface: React.FC<AdMeshChatInterfaceProps> = ({\n  messages,\n  config,\n  theme,\n  isLoading = false,\n  onSendMessage,\n  onRecommendationClick,\n  className\n}) => {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const messagesContainerRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const containerClasses = classNames(\n    'admesh-chat-interface',\n    'flex flex-col h-full bg-white dark:bg-slate-900',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  // Limit messages if maxMessages is set\n  const displayMessages = config.maxMessages \n    ? messages.slice(-config.maxMessages)\n    : messages;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Messages Area */}\n      <div \n        ref={messagesContainerRef}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\"\n      >\n        {displayMessages.length === 0 ? (\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4\">\n              <svg className=\"w-8 h-8 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\">\n              Welcome to AdMesh AI\n            </h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 max-w-xs\">\n              Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!\n            </p>\n          </div>\n        ) : (\n          <>\n            {displayMessages.map((message) => (\n              <AdMeshChatMessage\n                key={message.id}\n                message={message}\n                theme={theme}\n                onRecommendationClick={onRecommendationClick}\n              />\n            ))}\n\n            {/* Typing Indicator */}\n            {isLoading && config.enableTypingIndicator !== false && (\n              <div className=\"flex items-start gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div className=\"bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </>\n        )}\n      </div>\n\n      {/* Quick Suggestions */}\n      {config.enableSuggestions && config.suggestions && config.suggestions.length > 0 && messages.length === 0 && (\n        <div className=\"px-4 pb-2\">\n          <div className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">Quick suggestions:</div>\n          <div className=\"flex flex-wrap gap-2\">\n            {config.suggestions.slice(0, 3).map((suggestion, index) => (\n              <button\n                key={index}\n                onClick={() => onSendMessage?.(suggestion)}\n                className=\"px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors\"\n              >\n                {suggestion}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Input Area */}\n      {config.showInputField !== false && onSendMessage && (\n        <div className=\"border-t border-gray-200 dark:border-slate-700 p-4\">\n          <AdMeshChatInput\n            placeholder={config.placeholder || \"Ask me about products, tools, or services...\"}\n            disabled={isLoading}\n            suggestions={config.suggestions}\n            theme={theme}\n            onSendMessage={onSendMessage}\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshFloatingChatProps, ChatMessage } from '../types/index';\nimport { AdMeshChatInterface } from './AdMeshChatInterface';\n\nexport const AdMeshFloatingChat: React.FC<AdMeshFloatingChatProps> = ({\n  config,\n  theme,\n  title = 'AI Assistant',\n  subtitle = 'Get personalized recommendations',\n  isOpen: controlledIsOpen,\n  onToggle,\n  onSendMessage,\n  onRecommendationClick,\n  autoRecommendations,\n  autoRecommendationTrigger,\n  showInputField = true,\n  autoShowRecommendations = false,\n  onAutoRecommendationDismiss,\n  className\n}) => {\n  const [internalIsOpen, setInternalIsOpen] = useState(config.autoOpen || false);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasInteracted, setHasInteracted] = useState(false);\n\n  const isOpen = controlledIsOpen !== undefined ? controlledIsOpen : internalIsOpen;\n\n  // Initialize with welcome message\n  useEffect(() => {\n    if (config.showWelcomeMessage && config.welcomeMessage && messages.length === 0) {\n      const welcomeMessage: ChatMessage = {\n        id: 'welcome',\n        role: 'assistant',\n        content: config.welcomeMessage,\n        timestamp: new Date(),\n      };\n      setMessages([welcomeMessage]);\n    }\n  }, [config.showWelcomeMessage, config.welcomeMessage, messages.length]);\n\n  // Handle auto-recommendations\n  useEffect(() => {\n    if (autoRecommendations && autoRecommendations.length > 0 && autoShowRecommendations) {\n      const autoMessage: ChatMessage = {\n        id: `auto-${Date.now()}`,\n        role: 'assistant',\n        content: autoRecommendationTrigger\n          ? `Based on \"${autoRecommendationTrigger}\", here are some relevant recommendations:`\n          : 'I found some relevant recommendations for you:',\n        timestamp: new Date(),\n        recommendations: autoRecommendations,\n      };\n\n      // Auto-open the chat and show recommendations\n      if (controlledIsOpen === undefined) {\n        setInternalIsOpen(true);\n      }\n\n      // Add the auto-recommendation message\n      setMessages(prev => {\n        // Avoid duplicating auto-recommendations\n        const hasAutoMessage = prev.some(msg => msg.id.startsWith('auto-'));\n        if (hasAutoMessage) {\n          return prev.map(msg =>\n            msg.id.startsWith('auto-') ? autoMessage : msg\n          );\n        }\n        return [...prev, autoMessage];\n      });\n    }\n  }, [autoRecommendations, autoShowRecommendations, autoRecommendationTrigger, controlledIsOpen]);\n\n  const handleToggle = () => {\n    if (onToggle) {\n      onToggle();\n    } else {\n      setInternalIsOpen(!internalIsOpen);\n    }\n    setHasInteracted(true);\n  };\n\n  const handleSendMessage = async (messageContent: string) => {\n    if (!onSendMessage) return;\n\n    // Add user message\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: messageContent,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setIsLoading(true);\n\n    try {\n      // Get AI response\n      const assistantMessage = await onSendMessage(messageContent);\n      setMessages(prev => [...prev, assistantMessage]);\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage: ChatMessage = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date(),\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Get chat dimensions based on size\n  const getChatDimensions = () => {\n    switch (config.size) {\n      case 'sm': return 'w-80 h-96';\n      case 'md': return 'w-96 h-[32rem]';\n      case 'lg': return 'w-[28rem] h-[36rem]';\n      case 'xl': return 'w-[32rem] h-[40rem]';\n      default: return 'w-96 h-[32rem]';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (config.position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  const containerClasses = classNames(\n    'admesh-floating-chat',\n    'fixed z-50 transition-all duration-300 ease-in-out',\n    getPositionClasses(),\n    className\n  );\n\n  const chatClasses = classNames(\n    'bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden',\n    getChatDimensions(),\n    {\n      'opacity-0 scale-95 pointer-events-none': !isOpen,\n      'opacity-100 scale-100': isOpen,\n    }\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Chat Interface */}\n      <div className={chatClasses}>\n        {isOpen && (\n          <>\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-sm\">{title}</h3>\n                  <p className=\"text-xs text-blue-100\">{subtitle}</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                {/* Dismiss auto-recommendations button */}\n                {autoRecommendations && autoRecommendations.length > 0 && onAutoRecommendationDismiss && (\n                  <button\n                    onClick={() => {\n                      onAutoRecommendationDismiss();\n                      setMessages(prev => prev.filter(msg => !msg.id.startsWith('auto-')));\n                    }}\n                    className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                    aria-label=\"Dismiss recommendations\"\n                    title=\"Dismiss recommendations\"\n                  >\n                    <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\" />\n                    </svg>\n                  </button>\n                )}\n\n                <button\n                  onClick={handleToggle}\n                  className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n                  aria-label=\"Close chat\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Chat Interface */}\n            <AdMeshChatInterface\n              messages={messages}\n              config={{\n                ...config,\n                showInputField: showInputField\n              }}\n              theme={theme}\n              isLoading={isLoading}\n              onSendMessage={showInputField ? handleSendMessage : undefined}\n              onRecommendationClick={onRecommendationClick}\n              className=\"h-full\"\n            />\n          </>\n        )}\n      </div>\n\n      {/* Chat Toggle Button */}\n      {!isOpen && (\n        <button\n          onClick={handleToggle}\n          className={classNames(\n            'w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700',\n            'text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200',\n            'flex items-center justify-center relative'\n          )}\n          aria-label=\"Open chat\"\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n          \n          {/* Notification dot for new users */}\n          {!hasInteracted && (\n            <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\"></div>\n          )}\n        </button>\n      )}\n\n      {/* Powered by AdMesh */}\n      {isOpen && (\n        <div className=\"absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm\">\n          Powered by AdMesh\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarHeaderProps } from '../types/index';\n\nexport const AdMeshSidebarHeader: React.FC<AdMeshSidebarHeaderProps> = ({\n  title,\n  theme,\n  collapsible = false,\n  isCollapsed = false,\n  onToggle,\n  onSearch,\n  showSearch = false,\n  className\n}) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSearchFocused, setIsSearchFocused] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    onSearch?.(value);\n  };\n\n  const handleSearchClear = () => {\n    setSearchQuery('');\n    onSearch?.('');\n  };\n\n  const headerClasses = classNames(\n    'admesh-sidebar-header',\n    'flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={headerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Title and Toggle */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 truncate\">\n          {title}\n        </h3>\n\n        <div className=\"flex items-center gap-2\">\n          {/* Mobile close button */}\n          {isMobile && onToggle && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden\"\n              title=\"Close sidebar\"\n            >\n              <svg\n                className=\"w-4 h-4 text-gray-600 dark:text-gray-400\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          )}\n\n          {/* Desktop collapse button */}\n          {collapsible && (\n            <button\n              onClick={onToggle}\n              className=\"p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block\"\n              title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n            >\n              <svg\n                className={classNames(\n                  'w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200',\n                  { 'rotate-180': isCollapsed }\n                )}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n              </svg>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Search Bar */}\n      {showSearch && !isCollapsed && (\n        <div className=\"relative\">\n          <div className={classNames(\n            'relative flex items-center transition-all duration-200',\n            {\n              'ring-2 ring-blue-500 dark:ring-blue-400': isSearchFocused,\n            }\n          )}>\n            {/* Search Icon */}\n            <div className=\"absolute left-3 pointer-events-none\">\n              <svg className=\"w-4 h-4 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n\n            {/* Search Input */}\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={handleSearchChange}\n              onFocus={() => setIsSearchFocused(true)}\n              onBlur={() => setIsSearchFocused(false)}\n              placeholder=\"Search recommendations...\"\n              className={classNames(\n                'w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg',\n                'placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100',\n                'focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent',\n                'transition-all duration-200'\n              )}\n            />\n\n            {/* Clear Button */}\n            {searchQuery && (\n              <button\n                onClick={handleSearchClear}\n                className=\"absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors\"\n                title=\"Clear search\"\n              >\n                <svg className=\"w-3 h-3 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n          </div>\n\n          {/* Search Results Count */}\n          {searchQuery && (\n            <div className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\n              Search results will be filtered in real-time\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Quick Stats */}\n      {!isCollapsed && (\n        <div className=\"mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400\">\n          <div className=\"flex items-center gap-1\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n            <span>Live recommendations</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n            <span>AI-powered</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarContentProps } from '../types/index';\nimport { AdMeshInlineRecommendation } from './AdMeshInlineRecommendation';\nimport { AdMeshProductCard } from './AdMeshProductCard';\n\nexport const AdMeshSidebarContent: React.FC<AdMeshSidebarContentProps> = ({\n  recommendations,\n  displayMode,\n  theme,\n  maxRecommendations,\n  onRecommendationClick,\n  className\n}) => {\n  const [showFilters, setShowFilters] = useState(false);\n  const [activeTab, setActiveTab] = useState<'all' | 'top' | 'recent'>('all');\n\n  const displayRecommendations = maxRecommendations \n    ? recommendations.slice(0, maxRecommendations)\n    : recommendations;\n\n  const getTabRecommendations = () => {\n    switch (activeTab) {\n      case 'top':\n        return displayRecommendations\n          .filter(rec => rec.intent_match_score >= 0.8)\n          .slice(0, 5);\n      case 'recent':\n        return displayRecommendations.slice(0, 3);\n      default:\n        return displayRecommendations;\n    }\n  };\n\n  const tabRecommendations = getTabRecommendations();\n\n  const contentClasses = classNames(\n    'admesh-sidebar-content',\n    'flex flex-col h-full',\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  const renderRecommendations = () => {\n    if (tabRecommendations.length === 0) {\n      return (\n        <div className=\"flex-1 flex flex-col items-center justify-center p-6 text-center\">\n          <div className=\"w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400 dark:text-gray-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </div>\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\n            No recommendations found\n          </h4>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            Try adjusting your search or filters\n          </p>\n        </div>\n      );\n    }\n\n    switch (displayMode) {\n      case 'recommendations':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n\n      case 'history':\n        return (\n          <div className=\"space-y-2\">\n            {tabRecommendations.map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full flex-shrink-0\"></div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\n                    {recommendation.title}\n                  </div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {Math.round(recommendation.intent_match_score * 100)}% match\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'favorites':\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.slice(0, 3).map((recommendation, index) => (\n              <div key={recommendation.ad_id || index} className=\"relative\">\n                <AdMeshInlineRecommendation\n                  recommendation={recommendation}\n                  theme={theme}\n                  compact={true}\n                  showReason={false}\n                  onClick={onRecommendationClick}\n                />\n                <button className=\"absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors\">\n                  <svg className=\"w-3 h-3 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n                  </svg>\n                </button>\n              </div>\n            ))}\n          </div>\n        );\n\n      case 'mixed':\n        return (\n          <div className=\"space-y-4\">\n            {/* Top recommendation as card */}\n            {tabRecommendations[0] && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  Top Pick\n                </h4>\n                <AdMeshProductCard\n                  recommendation={tabRecommendations[0]}\n                  theme={theme}\n                  showMatchScore={true}\n                  showBadges={true}\n                  onClick={onRecommendationClick}\n                  className=\"text-xs\"\n                />\n              </div>\n            )}\n\n            {/* Other recommendations as inline */}\n            {tabRecommendations.slice(1).length > 0 && (\n              <div>\n                <h4 className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2\">\n                  More Options\n                </h4>\n                <div className=\"space-y-2\">\n                  {tabRecommendations.slice(1, 4).map((recommendation, index) => (\n                    <AdMeshInlineRecommendation\n                      key={recommendation.ad_id || index}\n                      recommendation={recommendation}\n                      theme={theme}\n                      compact={true}\n                      showReason={false}\n                      onClick={onRecommendationClick}\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"space-y-3\">\n            {tabRecommendations.map((recommendation, index) => (\n              <AdMeshInlineRecommendation\n                key={recommendation.ad_id || index}\n                recommendation={recommendation}\n                theme={theme}\n                compact={true}\n                showReason={true}\n                onClick={onRecommendationClick}\n              />\n            ))}\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div\n      className={contentClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      {/* Tabs */}\n      <div className=\"flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900\">\n        <button\n          onClick={() => setActiveTab('all')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'all'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          All ({recommendations.length})\n        </button>\n        <button\n          onClick={() => setActiveTab('top')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'top'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Top\n        </button>\n        <button\n          onClick={() => setActiveTab('recent')}\n          className={classNames(\n            'flex-1 px-3 py-2 text-xs font-medium transition-colors',\n            activeTab === 'recent'\n              ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'\n              : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n          )}\n        >\n          Recent\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 min-h-0\" style={{\n        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS\n        overscrollBehavior: 'contain' // Prevent scroll chaining on mobile\n      }}>\n        {renderRecommendations()}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800\">\n        <div className=\"flex items-center justify-between text-xs\">\n          <span className=\"text-gray-500 dark:text-gray-400\">\n            {tabRecommendations.length} recommendation{tabRecommendations.length !== 1 ? 's' : ''}\n          </span>\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors\"\n          >\n            Filters\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "import React, { useState, useEffect, useMemo } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshSidebarProps, SidebarFilters } from '../types/index';\nimport { AdMeshSidebarHeader } from './AdMeshSidebarHeader';\nimport { AdMeshSidebarContent } from './AdMeshSidebarContent';\n\nexport const AdMeshSidebar: React.FC<AdMeshSidebarProps> = ({\n  recommendations,\n  config,\n  theme,\n  title = 'Recommendations',\n  isOpen = true,\n  onToggle,\n  onRecommendationClick,\n  onSearch,\n  // onFilter,\n  className,\n  containerMode = false // New prop for demo/container integration\n}) => {\n  const [isCollapsed, setIsCollapsed] = useState(config.defaultCollapsed || false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [filters] = useState<SidebarFilters>({});\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Mobile detection\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 640);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Prevent body scroll on mobile when sidebar is open\n  useEffect(() => {\n    if (isMobile && isOpen && !isCollapsed && !containerMode) {\n      const originalStyle = window.getComputedStyle(document.body).overflow;\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n\n      return () => {\n        document.body.style.overflow = originalStyle;\n        document.body.style.position = '';\n        document.body.style.width = '';\n      };\n    }\n  }, [isMobile, isOpen, isCollapsed, containerMode]);\n\n  // Handle auto-refresh if enabled\n  useEffect(() => {\n    if (config.autoRefresh && config.refreshInterval) {\n      const interval = setInterval(() => {\n        // Trigger a refresh - in a real app this would refetch recommendations\n        console.log('Auto-refreshing recommendations...');\n      }, config.refreshInterval);\n\n      return () => clearInterval(interval);\n    }\n  }, [config.autoRefresh, config.refreshInterval]);\n\n  // Filter recommendations based on search and filters\n  const filteredRecommendations = useMemo(() => {\n    let filtered = [...recommendations];\n\n    // Apply search filter\n    if (searchQuery.trim()) {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(rec => \n        rec.title.toLowerCase().includes(query) ||\n        rec.reason.toLowerCase().includes(query) ||\n        rec.keywords?.some(keyword => keyword.toLowerCase().includes(query))\n      );\n    }\n\n    // Apply category filter\n    if (filters.categories && filters.categories.length > 0) {\n      filtered = filtered.filter(rec => \n        rec.categories?.some(cat => filters.categories?.includes(cat))\n      );\n    }\n\n    // Apply free tier filter\n    if (filters.hasFreeTier) {\n      filtered = filtered.filter(rec => rec.has_free_tier);\n    }\n\n    // Apply trial filter\n    if (filters.hasTrial) {\n      filtered = filtered.filter(rec => rec.trial_days && rec.trial_days > 0);\n    }\n\n    // Apply minimum match score filter\n    if (filters.minMatchScore !== undefined) {\n      filtered = filtered.filter(rec => rec.intent_match_score >= filters.minMatchScore!);\n    }\n\n    // Sort by match score (highest first)\n    filtered.sort((a, b) => b.intent_match_score - a.intent_match_score);\n\n    // Limit results\n    if (config.maxRecommendations) {\n      filtered = filtered.slice(0, config.maxRecommendations);\n    }\n\n    return filtered;\n  }, [recommendations, searchQuery, filters, config.maxRecommendations]);\n\n  const handleToggle = () => {\n    if (config.collapsible) {\n      setIsCollapsed(!isCollapsed);\n      onToggle?.();\n    }\n  };\n\n  const handleSearch = (query: string) => {\n    setSearchQuery(query);\n    onSearch?.(query);\n  };\n\n  // const handleFilter = (newFilters: SidebarFilters) => {\n  //   setFilters(newFilters);\n  //   onFilter?.(newFilters);\n  // };\n\n  // Get sidebar width based on size with mobile responsiveness\n  const getSidebarWidth = () => {\n    if (isCollapsed) return 'w-12';\n\n    // On mobile, always use full width with proper constraints\n    switch (config.size) {\n      case 'sm': return 'w-full sm:w-64 max-w-[90vw] sm:max-w-sm';\n      case 'md': return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n      case 'lg': return 'w-full sm:w-96 max-w-[90vw] sm:max-w-lg';\n      case 'xl': return 'w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl';\n      default: return 'w-full sm:w-80 max-w-[90vw] sm:max-w-md';\n    }\n  };\n\n  const sidebarClasses = classNames(\n    'admesh-sidebar',\n    'flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out',\n    getSidebarWidth(),\n    {\n      'border-r': config.position === 'left',\n      'border-l': config.position === 'right',\n      // Use fixed positioning for full-screen mode, relative for container mode\n      // Improved mobile positioning with proper viewport handling\n      'fixed top-0 bottom-0 z-[9999]': !containerMode,\n      'relative h-full': containerMode,\n      'left-0': config.position === 'left' && !containerMode,\n      'right-0': config.position === 'right' && !containerMode,\n      // Better mobile transform handling\n      'transform -translate-x-full': config.position === 'left' && !isOpen && !containerMode,\n      'transform translate-x-full': config.position === 'right' && !isOpen && !containerMode,\n      // Mobile-specific improvements\n      'min-h-0': true, // Prevent height issues on mobile\n      'overflow-hidden': !containerMode, // Prevent scroll issues\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  if (!isOpen && !config.collapsible) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Overlay for mobile - show in both modes on small screens */}\n      {isOpen && !isCollapsed && (\n        <div\n          className={classNames(\n            \"bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300\",\n            containerMode ? \"absolute inset-0\" : \"fixed inset-0\"\n          )}\n          onClick={() => onToggle?.()}\n          style={{\n            // Ensure overlay covers the entire viewport on mobile\n            position: containerMode ? 'absolute' : 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            touchAction: 'none', // Prevent scrolling behind overlay\n          }}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div\n        className={sidebarClasses}\n        style={containerStyle}\n        data-admesh-theme={theme?.mode}\n        data-sidebar-position={config.position}\n        data-sidebar-size={config.size}\n        data-mobile-open={isMobile && isOpen && !isCollapsed ? 'true' : 'false'}\n        data-container-mode={containerMode ? 'true' : 'false'}\n      >\n        {/* Header */}\n        {config.showHeader !== false && (\n          <AdMeshSidebarHeader\n            title={title}\n            theme={theme}\n            collapsible={config.collapsible}\n            isCollapsed={isCollapsed}\n            onToggle={handleToggle}\n            onSearch={config.showSearch ? handleSearch : undefined}\n            showSearch={config.showSearch && !isCollapsed}\n          />\n        )}\n\n        {/* Content */}\n        {!isCollapsed && (\n          <AdMeshSidebarContent\n            recommendations={filteredRecommendations}\n            displayMode={config.displayMode}\n            theme={theme}\n            maxRecommendations={config.maxRecommendations}\n            onRecommendationClick={onRecommendationClick}\n            className=\"flex-1 overflow-hidden min-h-0\"\n          />\n        )}\n\n        {/* Collapsed state indicator */}\n        {isCollapsed && config.collapsible && (\n          <div className=\"flex-1 flex flex-col items-center justify-center p-2\">\n            <button\n              onClick={handleToggle}\n              className=\"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors\"\n              title=\"Expand sidebar\"\n            >\n              <svg className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </button>\n            <div className=\"mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap\">\n              {filteredRecommendations.length}\n            </div>\n          </div>\n        )}\n\n        {/* Powered by AdMesh */}\n        {!isCollapsed && (\n          <div className=\"p-3 border-t border-gray-200 dark:border-slate-700\">\n            <div className=\"text-xs text-gray-400 dark:text-gray-500 text-center\">\n              Powered by AdMesh\n            </div>\n          </div>\n        )}\n      </div>\n    </>\n  );\n};\n", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport type { AdMeshRecommendation, AdMeshTheme } from '../types/index';\nimport { AdMeshConversationalUnit } from './AdMeshConversationalUnit';\n\nexport interface AdMeshAutoRecommendationWidgetProps {\n  recommendations: AdMeshRecommendation[];\n  trigger?: string; // The query/context that triggered recommendations\n  theme?: AdMeshTheme;\n  title?: string;\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';\n  size?: 'sm' | 'md' | 'lg';\n  autoShow?: boolean;\n  showDelay?: number; // Delay before showing (ms)\n  onRecommendationClick?: (adId: string, admeshLink: string) => void;\n  onDismiss?: () => void;\n  className?: string;\n}\n\nexport const AdMeshAutoRecommendationWidget: React.FC<AdMeshAutoRecommendationWidgetProps> = ({\n  recommendations,\n  trigger,\n  theme,\n  title = 'AI Recommendations',\n  position = 'bottom-right',\n  size = 'md',\n  autoShow = true,\n  showDelay = 1000,\n  onRecommendationClick,\n  onDismiss,\n  className\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [hasAnimated, setHasAnimated] = useState(false);\n\n  // Auto-show with delay\n  useEffect(() => {\n    if (autoShow && recommendations.length > 0) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n        setHasAnimated(true);\n      }, showDelay);\n\n      return () => clearTimeout(timer);\n    }\n  }, [autoShow, recommendations.length, showDelay]);\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    onDismiss?.();\n  };\n\n  // Get widget dimensions based on size\n  const getWidgetDimensions = () => {\n    switch (size) {\n      case 'sm': return 'w-72 max-h-80';\n      case 'md': return 'w-80 max-h-96';\n      case 'lg': return 'w-96 max-h-[28rem]';\n      default: return 'w-80 max-h-96';\n    }\n  };\n\n  // Get position classes\n  const getPositionClasses = () => {\n    switch (position) {\n      case 'bottom-right': return 'bottom-4 right-4';\n      case 'bottom-left': return 'bottom-4 left-4';\n      case 'top-right': return 'top-4 right-4';\n      case 'top-left': return 'top-4 left-4';\n      default: return 'bottom-4 right-4';\n    }\n  };\n\n  if (!isVisible || recommendations.length === 0) {\n    return null;\n  }\n\n  const containerClasses = classNames(\n    'admesh-auto-recommendation-widget',\n    'fixed z-50 transition-all duration-500 ease-out',\n    getPositionClasses(),\n    getWidgetDimensions(),\n    {\n      'opacity-0 scale-95 translate-y-2': !hasAnimated,\n      'opacity-100 scale-100 translate-y-0': hasAnimated,\n    },\n    className\n  );\n\n  const containerStyle = theme?.accentColor ? {\n    '--admesh-primary': theme.accentColor,\n  } as React.CSSProperties : undefined;\n\n  return (\n    <div\n      className={containerClasses}\n      style={containerStyle}\n      data-admesh-theme={theme?.mode}\n    >\n      <div className=\"bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-sm\">{title}</h3>\n              {trigger && (\n                <p className=\"text-xs text-blue-100 truncate max-w-48\">\n                  Based on: \"{trigger}\"\n                </p>\n              )}\n            </div>\n          </div>\n          <button\n            onClick={handleDismiss}\n            className=\"p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors\"\n            aria-label=\"Dismiss recommendations\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600\">\n          {/* Recommendations count */}\n          <div className=\"flex items-center gap-2 mb-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {recommendations.length} intelligent match{recommendations.length > 1 ? 'es' : ''} found\n            </span>\n          </div>\n\n          {/* Recommendations */}\n          <AdMeshConversationalUnit\n            recommendations={recommendations}\n            config={{\n              displayMode: 'inline',\n              context: 'assistant',\n              maxRecommendations: 3,\n              showPoweredBy: false,\n              autoShow: true,\n              delayMs: 200\n            }}\n            theme={theme}\n            onRecommendationClick={onRecommendationClick}\n          />\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Powered by AdMesh\n            </span>\n            <button\n              onClick={handleDismiss}\n              className=\"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors\"\n            >\n              Dismiss\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n", "// AdMesh UI SDK - Main Entry Point\n\n// Export all components\nexport {\n  AdMeshProductCard,\n  AdMeshCompareTable,\n  AdMeshBadge,\n  AdMeshLayout,\n  AdMeshLinkTracker,\n  AdMeshSimpleAd,\n  AdMeshConversationSummary,\n  AdMeshCitationUnit,\n  AdMeshInlineRecommendation,\n  AdMeshConversationalUnit,\n  AdMeshCitationReference,\n  AdMeshFloatingChat,\n  AdMeshChatInterface,\n  AdMeshChatMessage,\n  AdMeshChatInput,\n  AdMeshSidebar,\n  AdMeshSidebarHeader,\n  AdMeshSidebarContent,\n  AdMeshAutoRecommendationWidget\n} from './components';\n\n// Export hooks\nexport {\n  useAdMeshTracker,\n  setAdMeshTrackerConfig,\n  buildAdMeshLink,\n  extractTrackingData\n} from './hooks/useAdMeshTracker';\n\nexport {\n  useAdMeshStyles\n} from './hooks/useAdMeshStyles';\n\n// Export types\nexport type {\n  AdMeshRecommendation,\n  AdMeshTheme,\n  IntentType,\n  BadgeType,\n  BadgeVariant,\n  BadgeSize,\n  TrackingData,\n  AdMeshProductCardProps,\n  AdMeshCompareTableProps,\n  AdMeshBadgeProps,\n  AdMeshLayoutProps,\n  AdMeshLinkTrackerProps,\n  AdMeshSimpleAdProps,\n  UseAdMeshTrackerReturn,\n  AgentRecommendationResponse,\n  AdMeshConfig,\n  ConversationalDisplayMode,\n  ConversationContext,\n  ConversationalAdConfig,\n  AdMeshConversationSummaryProps,\n  AdMeshCitationUnitProps,\n  AdMeshInlineRecommendationProps,\n  AdMeshChatInputProps,\n  AdMeshChatMessageProps,\n  AdMeshChatInterfaceProps,\n  AdMeshFloatingChatProps,\n  AdMeshCitationReferenceProps,\n  AdMeshConversationalUnitProps,\n  ChatMessage,\n  SidebarPosition,\n  SidebarSize,\n  SidebarDisplayMode,\n  AdMeshSidebarConfig,\n  AdMeshSidebarProps,\n  SidebarFilters,\n  AdMeshSidebarHeaderProps,\n  AdMeshSidebarContentProps,\n\n} from './types/index';\n\n// Version info\nexport const VERSION = '0.2.1';\n\n// Default configuration\nexport const DEFAULT_CONFIG = {\n  trackingEnabled: true,\n  debug: false,\n  theme: {\n    mode: 'light' as const,\n    accentColor: '#2563eb'\n  }\n};\n"], "names": ["REACT_ELEMENT_TYPE", "REACT_FRAGMENT_TYPE", "jsxProd", "type", "config", "<PERSON><PERSON><PERSON>", "key", "propName", "reactJsxRuntime_production", "getComponentNameFromType", "REACT_CLIENT_REFERENCE", "REACT_PROFILER_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_ACTIVITY_TYPE", "REACT_PORTAL_TYPE", "REACT_CONTEXT_TYPE", "REACT_CONSUMER_TYPE", "REACT_FORWARD_REF_TYPE", "innerType", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "testStringCoercion", "value", "checkKeyStringCoercion", "JSCompiler_inline_result", "JSCompiler_temp_const", "JSCompiler_inline_result$jscomp$0", "getTaskName", "name", "get<PERSON>wner", "dispatcher", "ReactSharedInternals", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasOwnProperty", "getter", "defineKeyPropWarningGetter", "props", "displayName", "warnAboutAccessingKey", "specialPropKeyWarningShown", "elementRefGetterWithDeprecationWarning", "componentName", "didWarnAboutElementRef", "ReactElement", "self", "source", "owner", "debugStack", "debugTask", "jsxDEVImpl", "isStaticChildren", "children", "isArrayImpl", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "k", "didWarnAboutKeySpread", "node", "React", "require$$0", "createTask", "callStackForError", "unknownOwnerDebugStack", "unknownOwnerDebugTask", "reactJsxRuntime_development", "trackActualOwner", "jsxRuntimeModule", "require$$1", "hasOwn", "classNames", "classes", "arg", "appendClass", "parseValue", "newClass", "module", "DEFAULT_TRACKING_URL", "globalConfig", "setAdMeshTrackerConfig", "useAdMeshTracker", "isTracking", "setIsTracking", "useState", "error", "setError", "mergedConfig", "useMemo", "log", "useCallback", "message", "data", "sendTrackingEvent", "eventType", "errorMsg", "payload", "lastError", "attempt", "response", "result", "err", "resolve", "trackClick", "trackView", "trackConversion", "buildAdMeshLink", "baseLink", "adId", "additionalParams", "url", "extractTrackingData", "recommendation", "additionalData", "AdMeshLinkTracker", "admeshLink", "productId", "onClick", "trackingData", "className", "elementRef", "useRef", "hasTrackedView", "useEffect", "observer", "entries", "entry", "handleClick", "event", "jsx", "AdMeshProductCard", "theme", "showMatchScore", "showBadges", "badges", "generatedBadges", "aiKeywords", "_a", "keyword", "ai", "matchScorePercentage", "cardClasses", "cardStyle", "jsxs", "feature", "j", "integration", "AdMeshCompareTable", "recommendations", "maxProducts", "showMatchScores", "showFeatures", "onProductClick", "productsToCompare", "containerClasses", "containerStyle", "product", "index", "badgeTypeVariants", "badgeTypeIcons", "AdMeshBadge", "variant", "size", "effectiveVariant", "icon", "badgeClasses", "ADMESH_STYLES", "stylesInjected", "useAdMeshStyles", "styleElement", "existingStyle", "selectOptimalLayout", "intentType", "autoLayout", "productCount", "hasFeatures", "rec", "hasPricing", "AdMeshLayout", "maxDisplayed", "onTrackView", "displayRecommendations", "layout", "AdMeshSimpleAd", "showPoweredBy", "variation", "productName", "getAdText", "_b", "benefit", "_c", "AdMeshInlineRecommendation", "compact", "showReason", "AdMeshConversationSummary", "conversation<PERSON><PERSON><PERSON><PERSON>", "showTopRecommendations", "onRecommendationClick", "onStartNewConversation", "topRecommendations", "a", "b", "AdMeshCitationReference", "citationNumber", "citationStyle", "showTooltip", "onHover", "isHovered", "setIsHovered", "handleMouseEnter", "handleMouseLeave", "getCitationDisplay", "citationClasses", "AdMeshCitationUnit", "conversationText", "showCitationList", "onCitationHover", "hoveredRecommendation", "setHoveredRecommendation", "processedContent", "processedText", "citationMap", "title", "titleRegex", "match", "keywords", "inserted", "keywordRegex", "renderTextWithCitations", "text", "part", "citationMatch", "AdMeshConversationalUnit", "sessionId", "on<PERSON><PERSON><PERSON>", "isVisible", "setIsVisible", "hasAnimated", "setHasAnimated", "timer", "maxRecommendations", "handleRecommendationClick", "handle<PERSON><PERSON><PERSON>", "renderContent", "AdMeshChatMessage", "isUser", "isAssistant", "messageClasses", "bubbleClasses", "formatTime", "timestamp", "AdMeshChatInput", "placeholder", "disabled", "suggestions", "onSendMessage", "setMessage", "showSuggestions", "setShowSuggestions", "filteredSuggestions", "setFilteredSuggestions", "inputRef", "handleInputChange", "e", "filtered", "suggestion", "handleKeyDown", "handleSend", "trimmedMessage", "handleSuggestionClick", "inputClasses", "sendButtonClasses", "AdMeshChatInterface", "messages", "isLoading", "messagesEndRef", "messagesContainerRef", "displayMessages", "Fragment", "AdMeshFloatingChat", "subtitle", "controlledIsOpen", "onToggle", "autoRecommendations", "autoRecommendationTrigger", "showInputField", "autoShowRecommendations", "onAutoRecommendationDismiss", "internalIsOpen", "setInternalIsOpen", "setMessages", "setIsLoading", "hasInteracted", "setHasInteracted", "isOpen", "welcomeMessage", "autoMessage", "prev", "msg", "handleToggle", "handleSendMessage", "messageContent", "userMessage", "assistant<PERSON><PERSON><PERSON>", "errorMessage", "getChatDimensions", "chatClasses", "AdMeshSidebarHeader", "collapsible", "isCollapsed", "onSearch", "showSearch", "searchQuery", "setSearch<PERSON>uery", "isSearchFocused", "setIsSearchFocused", "isMobile", "setIsMobile", "checkMobile", "handleSearchChange", "handleSearchClear", "headerClasses", "AdMeshSidebarContent", "displayMode", "showFilters", "setShowFilters", "activeTab", "setActiveTab", "tabRecommendations", "contentClasses", "renderRecommendations", "AdMeshSidebar", "containerMode", "setIsCollapsed", "filters", "originalStyle", "interval", "filteredRecommendations", "query", "cat", "handleSearch", "sidebarClasses", "AdMeshAutoRecommendationWidget", "trigger", "position", "autoShow", "showDelay", "getWidgetDimensions", "getPositionClasses", "VERSION", "DEFAULT_CONFIG"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWA,IAAIA,IAAqB,OAAO,GAAA,CAAI,4BAA4B,GAC9DC,IAAsB,OAAO,GAAA,CAAI,gBAAgB;IACnD,SAASC,EAAQC,CAAAA,EAAMC,CAAAA,EAAQC,CAAAA,EAAU;QACvC,IAAIC,IAAM;QAGV,IAFWD,MAAX,KAAA,KAAA,CAAwBC,IAAM,KAAKD,CAAAA,GACxBD,EAAO,GAAA,KAAlB,KAAA,KAAA,CAA0BE,IAAM,KAAKF,EAAO,GAAA,GACxC,SAASA,GAAQ;YACnBC,IAAW,CAAA;YACX,IAAA,IAASE,KAAYH,EACTG,MAAV,SAAA,CAAuBF,CAAAA,CAASE,CAAQ,CAAA,GAAIH,CAAAA,CAAOG,CAAQ,CAAA;QAAA,MACxD,CAAAF,IAAWD;QAClB,OAAAA,IAASC,EAAS,GAAA,EACX;YACL,UAAUL;YACV,MAAMG;YACN,KAAKG;YACL,KAAgBF,MAAX,KAAA,IAAoBA,IAAS;YAClC,OAAOC;QAAA;IAEX;IACA,OAAAG,EAAA,QAAA,GAAmBP,GACnBO,EAAA,GAAA,GAAcN,GACdM,EAAA,IAAA,GAAeN,GAAAA;;;;;;;;;;;;;0BCtBE,QAAQ,IAAI,wCAA7B,gBACG,WAAY;QACX,SAASO,EAAyBN,CAAAA,EAAM;YACtC,IAAYA,KAAR,KAAc,CAAA,OAAO;YACzB,IAAmB,OAAOA,KAAtB,YACF,OAAOA,EAAK,QAAA,KAAaO,IACrB,OACAP,EAAK,WAAA,IAAeA,EAAK,IAAA,IAAQ;YACvC,IAAiB,OAAOA,KAApB,SAA0B,CAAA,OAAOA;YACrC,OAAQA,GAAI;gBACV,KAAKF;oBACH,OAAO;gBACT,KAAKU;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAO;YACjB;YACM,IAAiB,OAAOZ,KAApB,UACF,OACgB,OAAOA,EAAK,GAAA,IAAzB,YACC,QAAQ,KAAA,CACN,sHAEJA,EAAK,QAAA,EACf;gBACU,KAAKa;oBACH,OAAO;gBACT,KAAKC;oBACH,OAAA,CAAQd,EAAK,WAAA,IAAe,SAAA,IAAa;gBAC3C,KAAKe;oBACH,OAAA,CAAQf,EAAK,QAAA,CAAS,WAAA,IAAe,SAAA,IAAa;gBACpD,KAAKgB;oBACH,IAAIC,IAAYjB,EAAK,MAAA;oBACrB,OAAAA,IAAOA,EAAK,WAAA,EACZA,KAAAA,CACIA,IAAOiB,EAAU,WAAA,IAAeA,EAAU,IAAA,IAAQ,IACnDjB,IAAcA,MAAP,KAAc,gBAAgBA,IAAO,MAAM,YAAA,GAC9CA;gBACT,KAAKkB;oBACH,OACGD,IAAYjB,EAAK,WAAA,IAAe,MACxBiB,MAAT,OACIA,IACAX,EAAyBN,EAAK,IAAI,KAAK;gBAE/C,KAAKmB;oBACHF,IAAYjB,EAAK,QAAA,EACjBA,IAAOA,EAAK,KAAA;oBACZ,IAAI;wBACF,OAAOM,EAAyBN,EAAKiB,CAAS,CAAC;oBAAA,EAAA,OACrC,CAAA;YACxB;YACM,OAAO;QACb;QACI,SAASG,EAAmBC,CAAAA,EAAO;YACjC,OAAO,KAAKA;QAClB;QACI,SAASC,EAAuBD,CAAAA,EAAO;YACrC,IAAI;gBACFD,EAAmBC,CAAK;gBACxB,IAAIE,IAA2B,CAAA;YAAA,EAAA,OACrB;gBACVA,IAA2B,CAAA;YACnC;YACM,IAAIA,GAA0B;gBAC5BA,IAA2B;gBAC3B,IAAIC,IAAwBD,EAAyB,KAAA,EACjDE,IACc,OAAO,UAAtB,cACC,OAAO,WAAA,IACPJ,CAAAA,CAAM,OAAO,WAAW,CAAA,IAC1BA,EAAM,WAAA,CAAY,IAAA,IAClB;gBACF,OAAAG,EAAsB,IAAA,CACpBD,GACA,4GACAE,IAEKL,EAAmBC,CAAK;YACvC;QACA;QACI,SAASK,EAAY1B,CAAAA,EAAM;YACzB,IAAIA,MAASF,EAAqB,CAAA,OAAO;YACzC,IACe,OAAOE,KAApB,YACSA,MAAT,QACAA,EAAK,QAAA,KAAamB,GAElB,OAAO;YACT,IAAI;gBACF,IAAIQ,IAAOrB,EAAyBN,CAAI;gBACxC,OAAO2B,IAAO,MAAMA,IAAO,MAAM;YAAA,EAAA,OACvB;gBACV,OAAO;YACf;QACA;QACI,SAASC,IAAW;YAClB,IAAIC,IAAaC,EAAqB,CAAA;YACtC,OAAgBD,MAAT,OAAsB,OAAOA,EAAW,QAAA,CAAQ;QAC7D;QACI,SAASE,IAAe;YACtB,OAAO,MAAM,uBAAuB;QAC1C;QACI,SAASC,EAAY/B,CAAAA,EAAQ;YAC3B,IAAIgC,EAAe,IAAA,CAAKhC,GAAQ,KAAK,GAAG;gBACtC,IAAIiC,IAAS,OAAO,wBAAA,CAAyBjC,GAAQ,KAAK,EAAE,GAAA;gBAC5D,IAAIiC,KAAUA,EAAO,cAAA,CAAgB,CAAA,OAAO,CAAA;YACpD;YACM,OAAkBjC,EAAO,GAAA,KAAlB,KAAA;QACb;QACI,SAASkC,EAA2BC,CAAAA,EAAOC,CAAAA,EAAa;YACtD,SAASC,IAAwB;gBAC/BC,KAAAA,CACIA,IAA6B,CAAA,GAC/B,QAAQ,KAAA,CACN,2OACAF,EACZ;YACA;YACMC,EAAsB,cAAA,GAAiB,CAAA,GACvC,OAAO,cAAA,CAAeF,GAAO,OAAO;gBAClC,KAAKE;gBACL,cAAc,CAAA;YACtB,CAAO;QACP;QACI,SAASE,IAAyC;YAChD,IAAIC,IAAgBnC,EAAyB,IAAA,CAAK,IAAI;YACtD,OAAAoC,CAAAA,CAAuBD,CAAa,CAAA,IAAA,CAChCC,CAAAA,CAAuBD,CAAa,CAAA,GAAI,CAAA,GAC1C,QAAQ,KAAA,CACN,8IACV,GACMA,IAAgB,IAAA,CAAK,KAAA,CAAM,GAAA,EACTA,MAAX,KAAA,IAA2BA,IAAgB;QACxD;QACI,SAASE,EACP3C,CAAAA,EACAG,CAAAA,EACAyC,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAV,CAAAA,EACAW,EAAAA,EACAC,EAAAA,EACA;YACA,OAAAJ,IAAOR,EAAM,GAAA,EACbpC,IAAO;gBACL,UAAUH;gBACV,MAAMG;gBACN,KAAKG;gBACL,OAAOiC;gBACP,QAAQU;YAAA,GAAA,CAEWF,MAAX,KAAA,IAAkBA,IAAO,IAAA,MAAnC,OACI,OAAO,cAAA,CAAe5C,GAAM,OAAO;gBACjC,YAAY,CAAA;gBACZ,KAAKwC;YAAA,CACN,IACD,OAAO,cAAA,CAAexC,GAAM,OAAO;gBAAE,YAAY,CAAA;gBAAI,OAAO;YAAA,CAAM,GACtEA,EAAK,MAAA,GAAS,CAAA,GACd,OAAO,cAAA,CAAeA,EAAK,MAAA,EAAQ,aAAa;gBAC9C,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAO;YACf,CAAO,GACD,OAAO,cAAA,CAAeA,GAAM,cAAc;gBACxC,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAO;YACf,CAAO,GACD,OAAO,cAAA,CAAeA,GAAM,eAAe;gBACzC,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAO+C;YACf,CAAO,GACD,OAAO,cAAA,CAAe/C,GAAM,cAAc;gBACxC,cAAc,CAAA;gBACd,YAAY,CAAA;gBACZ,UAAU,CAAA;gBACV,OAAOgD;YACf,CAAO,GACD,OAAO,MAAA,IAAA,CAAW,OAAO,MAAA,CAAOhD,EAAK,KAAK,GAAG,OAAO,MAAA,CAAOA,CAAI,CAAA,GACxDA;QACb;QACI,SAASiD,EACPjD,CAAAA,EACAC,CAAAA,EACAC,CAAAA,EACAgD,CAAAA,EACAL,CAAAA,EACAD,CAAAA,EACAG,EAAAA,EACAC,EAAAA,EACA;YACA,IAAIG,IAAWlD,EAAO,QAAA;YACtB,IAAekD,MAAX,KAAA,GACF,IAAID,GACF,IAAIE,GAAYD,CAAQ,GAAG;gBACzB,IACED,IAAmB,GACnBA,IAAmBC,EAAS,MAAA,EAC5BD,IAEAG,EAAkBF,CAAAA,CAASD,CAAgB,CAAC;gBAC9C,OAAO,MAAA,IAAU,OAAO,MAAA,CAAOC,CAAQ;YAAA,OAEvC,QAAQ,KAAA,CACN;iBAEDE,EAAkBF,CAAQ;YACjC,IAAIlB,EAAe,IAAA,CAAKhC,GAAQ,KAAK,GAAG;gBACtCkD,IAAW7C,EAAyBN,CAAI;gBACxC,IAAIsD,IAAO,OAAO,IAAA,CAAKrD,CAAM,EAAE,MAAA,CAAO,SAAUsD,EAAAA,EAAG;oBACjD,OAAiBA,OAAV;gBACjB,CAAS;gBACDL,IACE,IAAII,EAAK,MAAA,GACL,oBAAoBA,EAAK,IAAA,CAAK,SAAS,IAAI,WAC3C,kBACNE,EAAAA,CAAsBL,IAAWD,CAAgB,CAAA,IAAA,CAC7CI,IACA,IAAIA,EAAK,MAAA,GAAS,MAAMA,EAAK,IAAA,CAAK,SAAS,IAAI,WAAW,MAC5D,QAAQ,KAAA,CACN,CAAA;;;;;iCAAA,CAAA,EACAJ,GACAC,GACAG,GACAH,IAEDK,EAAAA,CAAsBL,IAAWD,CAAgB,CAAA,GAAI,CAAA,CAAA;YAChE;YAMM,IALAC,IAAW,MACAjD,MAAX,KAAA,KAAA,CACGoB,EAAuBpB,CAAQ,GAAIiD,IAAW,KAAKjD,CAAAA,GACtD8B,EAAY/B,CAAM,KAAA,CACfqB,EAAuBrB,EAAO,GAAG,GAAIkD,IAAW,KAAKlD,EAAO,GAAA,GAC3D,SAASA,GAAQ;gBACnBC,IAAW,CAAA;gBACX,IAAA,IAASE,MAAYH,EACTG,OAAV,SAAA,CAAuBF,CAAAA,CAASE,EAAQ,CAAA,GAAIH,CAAAA,CAAOG,EAAQ,CAAA;YAAA,MACxD,CAAAF,IAAWD;YAClB,OAAAkD,KACEhB,EACEjC,GACe,OAAOF,KAAtB,aACIA,EAAK,WAAA,IAAeA,EAAK,IAAA,IAAQ,YACjCA,IAED2C,EACL3C,GACAmD,GACAP,GACAC,GACAjB,EAAQ,GACR1B,GACA6C,IACAC;QAER;QACI,SAASK,EAAkBI,CAAAA,EAAM;YAClB,OAAOA,KAApB,YACWA,MAAT,QACAA,EAAK,QAAA,KAAa5D,KAClB4D,EAAK,MAAA,IAAA,CACJA,EAAK,MAAA,CAAO,SAAA,GAAY,CAAA;QACjC;QACI,IAAIC,0MAAQC,UAAAA,EACV9D,IAAqB,OAAO,GAAA,CAAI,4BAA4B,GAC5DgB,IAAoB,OAAO,GAAA,CAAI,cAAc,GAC7Cf,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACjDW,IAAyB,OAAO,GAAA,CAAI,mBAAmB,GACvDD,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GAE/CO,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACnDD,IAAqB,OAAO,GAAA,CAAI,eAAe,GAC/CE,IAAyB,OAAO,GAAA,CAAI,mBAAmB,GACvDN,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACjDC,IAA2B,OAAO,GAAA,CAAI,qBAAqB,GAC3DO,IAAkB,OAAO,GAAA,CAAI,YAAY,GACzCC,IAAkB,OAAO,GAAA,CAAI,YAAY,GACzCP,IAAsB,OAAO,GAAA,CAAI,gBAAgB,GACjDL,IAAyB,OAAO,GAAA,CAAI,wBAAwB,GAC5DuB,IACE4B,EAAM,+DAAA,EACRzB,IAAiB,OAAO,SAAA,CAAU,cAAA,EAClCmB,KAAc,MAAM,OAAA,EACpBQ,IAAa,QAAQ,UAAA,GACjB,QAAQ,UAAA,GACR,WAAY;YACV,OAAO;QAAA;QAEfF,IAAQ;YACN,4BAA4B,SAAUG,CAAAA,EAAmB;gBACvD,OAAOA,EAAiB;YAChC;QAAA;QAEI,IAAItB,GACAG,IAAyB,CAAA,GACzBoB,IAAyBJ,CAAAA,CAAM,0BAA0B,CAAA,CAAE,IAAA,CAC7DA,GACA3B,MAEEgC,IAAwBH,EAAWlC,EAAYK,CAAY,CAAC,GAC5DyB,KAAwB,CAAA;QAC5BQ,EAAA,QAAA,GAAmBlE,GACnBkE,EAAA,GAAA,GAAc,SAAUhE,CAAAA,EAAMC,CAAAA,EAAQC,CAAAA,EAAU2C,CAAAA,EAAQD,CAAAA,EAAM;YAC5D,IAAIqB,IACF,MAAMnC,EAAqB,0BAAA;YAC7B,OAAOmB,EACLjD,GACAC,GACAC,GACA,CAAA,GACA2C,GACAD,GACAqB,IACI,MAAM,uBAAuB,IAC7BH,GACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;WAGvDC,EAAA,IAAA,GAAe,SAAUhE,CAAAA,EAAMC,CAAAA,EAAQC,CAAAA,EAAU2C,CAAAA,EAAQD,CAAAA,EAAM;YAC7D,IAAIqB,IACF,MAAMnC,EAAqB,0BAAA;YAC7B,OAAOmB,EACLjD,GACAC,GACAC,GACA,CAAA,GACA2C,GACAD,GACAqB,IACI,MAAM,uBAAuB,IAC7BH,GACJG,IAAmBL,EAAWlC,EAAY1B,CAAI,CAAC,IAAI+D;;IAG3D,EAAG,CAAA,GAAA;;;;0BCnWC,QAAQ,IAAI,aAAa,eAC3BG,EAAA,UAAiBP,GAAA,sBAEjBO,EAAA,OAAA,GAAiBC,GAAA,CAAA,GAAA,EAAA,OAAA;;;;;;;;;;;;QCEnB,CAAC,WAAY;YAGZ,IAAIC,IAAS,CAAA,EAAG,cAAA;YAEhB,SAASC,IAAc;gBAGtB,IAAA,IAFIC,IAAU,IAEL,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;oBAC1C,IAAIC,IAAM,SAAA,CAAU,CAAC,CAAA;oBACjBA,KAAAA,CACHD,IAAUE,EAAYF,GAASG,EAAWF,CAAG,CAAC,CAAA;gBAElD;gBAEE,OAAOD;YACT;YAEC,SAASG,EAAYF,CAAAA,EAAK;gBACzB,IAAI,OAAOA,KAAQ,YAAY,OAAOA,KAAQ,UAC7C,OAAOA;gBAGR,IAAI,OAAOA,KAAQ,UAClB,OAAO;gBAGR,IAAI,MAAM,OAAA,CAAQA,CAAG,GACpB,OAAOF,EAAW,KAAA,CAAM,MAAME,CAAG;gBAGlC,IAAIA,EAAI,QAAA,KAAa,OAAO,SAAA,CAAU,QAAA,IAAY,CAACA,EAAI,QAAA,CAAS,QAAA,CAAQ,EAAG,QAAA,CAAS,eAAe,GAClG,OAAOA,EAAI,QAAA,CAAQ;gBAGpB,IAAID,IAAU;gBAEd,IAAA,IAASnE,KAAOoE,EACXH,EAAO,IAAA,CAAKG,GAAKpE,CAAG,KAAKoE,CAAAA,CAAIpE,CAAG,CAAA,IAAA,CACnCmE,IAAUE,EAAYF,GAASnE,CAAG,CAAA;gBAIpC,OAAOmE;YACT;YAEC,SAASE,EAAanD,CAAAA,EAAOqD,CAAAA,EAAU;gBACtC,OAAKA,IAIDrD,IACIA,IAAQ,MAAMqD,IAGfrD,IAAQqD,IAPPrD;YAQV;YAEsCsD,EAAO,OAAA,GAAA,CAC3CN,EAAW,OAAA,GAAUA,GACrBM,EAAAA,OAAAA,GAAiBN,CAAAA,IAOjB,OAAO,UAAA,GAAaA;QAEtB,CAAA;;;;kCCxEMO,KAAuB;AAW7B,IAAIC,KAA+B;IACjC,YAAYD;IACZ,SAAS,CAAA;IACT,OAAO,CAAA;IACP,eAAe;IACf,YAAY;AACd;AAEO,MAAME,KAAyB,CAAC7E,MAAoC;IACzE4E,KAAe;QAAE,GAAGA,EAAAA;QAAc,GAAG5E,CAAAA;IAAA;AACvC,GAEa8E,KAAmB,CAAC9E,MAA6D;IAC5F,MAAM,CAAC+E,GAAYC,CAAa,CAAA,6MAAIC,WAAAA,EAAS,CAAA,CAAK,GAC5C,CAACC,GAAOC,CAAQ,CAAA,wNAAIF,EAAwB,IAAI,GAEhDG,wNAAeC,EAAQ,IAAA,CAAO;YAAE,GAAGT,EAAAA;YAAc,GAAG5E,CAAAA;QAAA,CAAA,GAAW;QAACA,CAAM;KAAC,GAEvEsF,4NAAMC,EAAY,CAACC,GAAiBC,MAAmB;QACvDL,EAAa,KAAA,IACf,QAAQ,GAAA,CAAI,CAAA,iBAAA,EAAoBI,CAAO,EAAA,EAAIC,CAAI;IACjD,GACC;QAACL,EAAa,KAAK;KAAC,GAEjBM,IAAoBH,wNAAAA,EAAY,OACpCI,GACAF,MACkB;QAClB,IAAI,CAACL,EAAa,OAAA,EAAS;YACzBE,EAAI,qCAAqC;gBAAE,WAAAK;gBAAW,MAAAF;YAAA,CAAM;YAC5D;QAAA;QAGF,IAAI,CAACA,EAAK,IAAA,IAAQ,CAACA,EAAK,UAAA,EAAY;YAClC,MAAMG,IAAW;YACjBN,EAAIM,GAAUH,CAAI,GAClBN,EAASS,CAAQ;YACjB;QAAA;QAGFZ,EAAc,CAAA,CAAI,GAClBG,EAAS,IAAI;QAEb,MAAMU,IAAU;YACd,YAAYF;YACZ,OAAOF,EAAK,IAAA;YACZ,aAAaA,EAAK,UAAA;YAClB,YAAYA,EAAK,SAAA;YACjB,SAASA,EAAK,MAAA;YACd,YAAYA,EAAK,SAAA;YACjB,SAASA,EAAK,OAAA;YACd,iBAAiBA,EAAK,cAAA;YACtB,UAAUA,EAAK,QAAA;YACf,WAAA,AAAW,aAAA,GAAA,IAAI,KAAA,EAAO,WAAA,CAAA;YACtB,YAAY,UAAU,SAAA;YACtB,UAAU,SAAS,QAAA;YACnB,UAAU,OAAO,QAAA,CAAS,IAAA;QAAA;QAG5BH,EAAI,CAAA,QAAA,EAAWK,CAAS,CAAA,MAAA,CAAA,EAAUE,CAAO;QAEzC,IAAIC,IAA0B;QAE9B,IAAA,IAASC,IAAU,GAAGA,KAAAA,CAAYX,EAAa,aAAA,IAAiB,CAAA,GAAIW,IAClE,IAAI;YACF,MAAMC,IAAW,MAAM,MAAM,GAAGZ,EAAa,UAAU,CAAA,OAAA,CAAA,EAAW;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAAA;gBAElB,MAAM,KAAK,SAAA,CAAUS,CAAO;YAAA,CAC7B;YAED,IAAI,CAACG,EAAS,EAAA,EACZ,MAAM,IAAI,MAAM,CAAA,KAAA,EAAQA,EAAS,MAAM,CAAA,EAAA,EAAKA,EAAS,UAAU,EAAE;YAGnE,MAAMC,IAAS,MAAMD,EAAS,IAAA,CAAA;YAC9BV,EAAI,GAAGK,CAAS,CAAA,2BAAA,CAAA,EAA+BM,CAAM,GACrDjB,EAAc,CAAA,CAAK;YACnB;QAAA,EAAA,OAEOkB,GAAK;YACZJ,IAAYI,GACZZ,EAAI,CAAA,QAAA,EAAWS,CAAO,CAAA,YAAA,EAAeJ,CAAS,CAAA,MAAA,CAAA,EAAUO,CAAG,GAEvDH,IAAAA,CAAWX,EAAa,aAAA,IAAiB,CAAA,KAC3C,MAAM,IAAI,QAAQ,CAAA,IAChB,WAAWe,GAAAA,CAAUf,EAAa,UAAA,IAAc,GAAA,IAAQW,CAAO;QAEnE;QAKJ,MAAMH,IAAW,CAAA,gBAAA,EAAmBD,CAAS,CAAA,aAAA,EAAgBP,EAAa,aAAa,CAAA,WAAA,EAAcU,KAAA,OAAA,KAAA,IAAAA,EAAW,OAAO,EAAA;QACvHR,EAAIM,GAAUE,CAAS,GACvBX,EAASS,CAAQ,GACjBZ,EAAc,CAAA,CAAK;IAAA,GAClB;QAACI;QAAcE,CAAG;KAAC,GAEhBc,4NAAab,EAAY,OAAOE,IAC7BC,EAAkB,SAASD,CAAI,GACrC;QAACC,CAAiB;KAAC,GAEhBW,4NAAYd,EAAY,OAAOE,IAC5BC,EAAkB,QAAQD,CAAI,GACpC;QAACC,CAAiB;KAAC,GAEhBY,4NAAkBf,EAAY,OAAOE,IAAAA,CACrC,CAACA,EAAK,OAAA,IAAW,CAACA,EAAK,cAAA,IACzBH,EAAI,mEAAmEG,CAAI,GAEtEC,EAAkB,cAAcD,CAAI,CAAA,GAC1C;QAACC;QAAmBJ,CAAG;KAAC;IAE3B,OAAO;QACL,YAAAc;QACA,WAAAC;QACA,iBAAAC;QACA,YAAAvB;QACA,OAAAG;IAAA;AAEJ,GAGaqB,KAAkB,CAC7BC,GACAC,GACAC,MACW;IACX,IAAI;QACF,MAAMC,IAAM,IAAI,IAAIH,CAAQ;QAC5B,OAAAG,EAAI,YAAA,CAAa,GAAA,CAAI,SAASF,CAAI,GAClCE,EAAI,YAAA,CAAa,GAAA,CAAI,cAAc,QAAQ,GAC3CA,EAAI,YAAA,CAAa,GAAA,CAAI,cAAc,gBAAgB,GAE/CD,KACF,OAAO,OAAA,CAAQA,CAAgB,EAAE,OAAA,CAAQ,CAAC,CAACxG,GAAKkB,CAAK,CAAA,KAAM;YACzDuF,EAAI,YAAA,CAAa,GAAA,CAAIzG,GAAKkB,CAAK;QAAA,CAChC,GAGIuF,EAAI,QAAA,CAAA;IAAS,EAAA,OACbT,GAAK;QACZ,OAAA,QAAQ,IAAA,CAAK,qDAAqDM,GAAUN,CAAG,GACxEM;IAAA;AAEX,GAGaI,KAAsB,CACjCC,GACAC,IAAAA,CAEO;QACL,MAAMD,EAAe,KAAA;QACrB,YAAYA,EAAe,WAAA;QAC3B,WAAWA,EAAe,UAAA;QAC1B,GAAGC,CAAAA;IAAA,CAAA,GC1KMC,IAAsD,CAAC,EAClE,MAAAN,CAAAA,EACA,YAAAO,CAAAA,EACA,WAAAC,CAAAA,EACA,UAAA/D,CAAAA,EACA,SAAAgE,CAAAA,EACA,cAAAC,CAAAA,EACA,WAAAC,CAAAA,EACF,KAAM;IACJ,MAAM,EAAE,YAAAhB,CAAAA,EAAY,WAAAC,CAAAA,CAAA,CAAA,GAAcvB,GAAA,GAC5BuC,6MAAaC,UAAAA,EAAuB,IAAI,GACxCC,uNAAiBD,EAAO,CAAA,CAAK;0NAGnCE,EAAU,MAAM;QACd,IAAI,CAACH,EAAW,OAAA,IAAWE,EAAe,OAAA,CAAS,CAAA;QAEnD,MAAME,IAAW,IAAI,qBACnB,CAACC,MAAY;YACXA,EAAQ,OAAA,CAAQ,CAACC,MAAU;gBACrBA,EAAM,cAAA,IAAkB,CAACJ,EAAe,OAAA,IAAA,CAC1CA,EAAe,OAAA,GAAU,CAAA,GACzBlB,EAAU;oBACR,MAAAI;oBACA,YAAAO;oBACA,WAAAC;oBACA,GAAGE,CAAAA;gBAAA,CACJ,EAAE,KAAA,CAAM,QAAQ,KAAK,CAAA;YACxB,CACD;QAAA,GAEH;YACE,WAAW;YAAA,2CAAA;YACX,YAAY;QAAA;QAIhB,OAAAM,EAAS,OAAA,CAAQJ,EAAW,OAAO,GAE5B,MAAM;YACXI,EAAS,UAAA,CAAA;QAAW;IACtB,GACC;QAAChB;QAAMO;QAAYC;QAAWE;QAAcd,CAAS;KAAC;IAEzD,MAAMuB,4NAAcrC,EAAY,OAAOsC,MAA4B;QAEjE,IAAI;YACF,MAAMzB,EAAW;gBACf,MAAAK;gBACA,YAAAO;gBACA,WAAAC;gBACA,GAAGE,CAAAA;YAAA,CACJ;QAAA,EAAA,OACMjC,GAAO;YACd,QAAQ,KAAA,CAAM,0BAA0BA,CAAK;QAAA;QAI3CgC,KACFA,EAAA,GAKaW,EAAM,MAAA,CACD,OAAA,CAAQ,GAAG,KAI7B,OAAO,IAAA,CAAKb,GAAY,UAAU,qBAAqB;IACzD,GAEC;QAACP;QAAMO;QAAYC;QAAWE;QAAcf;QAAYc,CAAO;KAAC;IAEnE,OACEY,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,KAAKT;QACL,WAAAD;QACA,SAASQ;QACT,OAAO;YAAE,QAAQ;QAAA;QAEhB,UAAA1E;IAAA;AAGP;AAEA6D,EAAkB,WAAA,GAAc;ACrFzB,MAAMgB,KAAsD,CAAC,EAClE,gBAAAlB,CAAAA,EACA,OAAAmB,CAAAA,EACA,gBAAAC,IAAiB,CAAA,CAAA,EACjB,YAAAC,IAAa,CAAA,CAAA,EACb,SAAAhB,CAAAA,EACA,WAAAE,CAAAA,EACF,KAAM;IAEJ,MAAMe,8MAAS9C,UAAAA,EAAQ,MAAmB;;QACxC,MAAM+C,IAA+B,CAAA,CAAA;QAGjCvB,EAAe,kBAAA,IAAsB,OACvCuB,EAAgB,IAAA,CAAK,WAAW,GAI9BvB,EAAe,aAAA,IACjBuB,EAAgB,IAAA,CAAK,WAAW,GAI9BvB,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KAC3DuB,EAAgB,IAAA,CAAK,iBAAiB;QAIxC,MAAMC,IAAa;YAAC;YAAM;YAA2B;YAAoB;YAAM,YAAY;SAAA;QAK3F,OAAA,CAAA,CAAA,CAJsBC,IAAAzB,EAAe,QAAA,KAAf,OAAA,KAAA,IAAAyB,EAAyB,IAAA,CAAK,CAAAC,IAClDF,EAAW,IAAA,CAAK,CAAAG,IAAMD,EAAQ,WAAA,CAAA,EAAc,QAAA,CAASC,CAAE,CAAC,EAAA,KACrD3B,EAAe,KAAA,CAAM,WAAA,CAAA,EAAc,QAAA,CAAS,IAAI,CAAA,KAGnDuB,EAAgB,IAAA,CAAK,YAAY,GAG5BA;IAAA,GACN;QAACvB,CAAc;KAAC,GAGb4B,IAAuB,KAAK,KAAA,CAAM5B,EAAe,kBAAA,GAAqB,GAAG,GAEzE6B,IAActE,EAClB,oBACA,eACA,kKACAgD,IAGIuB,IAAYX,KAAA,QAAAA,EAAO,WAAA,GAAc;QACrC,oBAAoBA,EAAM,WAAA;QAC1B,0BAA0BA,EAAM,WAAA,GAAc;IAAA,IACrB,KAAA;IAE3B,OACEF,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;QACC,MAAMF,EAAe,KAAA;QACrB,YAAYA,EAAe,WAAA;QAC3B,WAAWA,EAAe,UAAA;QAC1B,SAAS,IAAMK,KAAA,OAAA,KAAA,IAAAA,EAAUL,EAAe,KAAA,EAAOA,EAAe,WAAA;QAC9D,cAAc;YACZ,OAAOA,EAAe,KAAA;YACtB,YAAYA,EAAe,kBAAA;QAAA;QAE7B,WAAW6B;QAEX,UAAAE,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YACC,WAAU;YACV,OAAOD;YACP,qBAAmBX,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;YAG1B,UAAA;gBAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAAV,KAAcC,EAAO,QAAA,CAAS,WAAW,KAAA,aAAA,GAAA,EAAA,GAAA,CACvC,QAAA;oCAAK,WAAU;oCAA2E,UAAA;gCAAA,CAE3F;gCAEFL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCACX,UAAA,EAAe,KAAA;gCAAA,CAClB;6BAAA;wBAAA,CACF;wBAAA,aAAA,GAAA,EAAA,GAAA,CAEC,OAAA;4BAAI,WAAU;4BACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;gCAAO,WAAU;gCAAoG,UAAA;oCAAA;oCAEpHd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA+E;oCAAA,CACtJ;iCAAA;4BAAA,CACF;wBAAA,CACF;qBAAA;gBAAA,CACF;gBAEAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;oBAAE,WAAU;oBACV,UAAA,EAAe,MAAA;gBAAA,CAClB;gBAGCG,KAAkB,OAAOpB,EAAe,kBAAA,IAAuB,YAC9D+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAc,UAAA;gCAAA,CAAW;gCACzCc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAoE,UAAA;wCAAAH;wCAAqB;qCAAA;gCAAA,CAAO;6BAAA;wBAAA,CAClH;wBACAX,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAGW,CAAoB,CAAA,CAAA,CAAA;gCAAA;4BAAI;wBAC7C,CACF;qBAAA;gBAAA,CACF;gBAGFG,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA;wBAAA/B,EAAe,OAAA,IACd+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA4I;gCAAA,CACnN;gCACCjB,EAAe,OAAA;6BAAA;wBAAA,CAClB;wBAGDA,EAAe,aAAA,IACd+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA6I;gCAAA,CACpN;gCAAM;6BAAA;wBAAA,CAER;wBAGDjB,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KACxD+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACd,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA4D;gCAAA,CACnI;gCACCjB,EAAe,UAAA;gCAAW;6BAAA;wBAAA,CAC7B;qBAAA;gBAAA,CAEJ;gBAGCA,EAAe,QAAA,IAAYA,EAAe,QAAA,CAAS,MAAA,GAAS,KAC3D+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAgD,UAAA;wBAAA,CAE/D;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA,EAAe,QAAA,CAAS,GAAA,CAAI,CAACe,GAASC,IACrCF,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAEC,WAAU;oCAEV,UAAA;wCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAsC,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAC7F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;4CAAA,CAAgD;wCAAA,CACvH;wCACCe;qCAAA;gCAAA,GANIC;wBAQR,CACH;qBAAA;gBAAA,CACF;gBAIDjC,EAAe,YAAA,IAAgBA,EAAe,YAAA,CAAa,MAAA,GAAS,KACnE+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAgD,UAAA;wBAAA,CAE/D;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA,EAAe,YAAA,CAAa,GAAA,CAAI,CAACiB,GAAaD,IAC7CF,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAEC,WAAU;oCAEV,UAAA;wCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAC/E,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;4CAAA,CAAyJ;wCAAA,CAChO;wCACCiB;qCAAA;gCAAA,GANID;wBAQR,CACH;qBAAA;gBAAA,CACF;gBAIDjC,EAAe,eAAA,IACdiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAe,eAAA;gBAAA,CAClB;gBAIFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wBAAK,WAAU;wBAA2C,UAAA;oBAAA,CAE3D;gBAAA,CACF;aAAA;QAAA;IACF;AAGN;AAEAC,GAAkB,WAAA,GAAc;AC9MzB,MAAMiB,KAAwD,CAAC,EACpE,iBAAAC,CAAAA,EACA,OAAAjB,CAAAA,EACA,aAAAkB,IAAc,CAAA,EACd,iBAAAC,IAAkB,CAAA,CAAA,EAClB,cAAAC,IAAe,CAAA,CAAA,EACf,gBAAAC,CAAAA,EACA,WAAAjC,CAAAA,EACF,KAAM;IAEJ,MAAMkC,wNAAoBjE,EAAQ,IACzB4D,EAAgB,KAAA,CAAM,GAAGC,CAAW,GAC1C;QAACD;QAAiBC,CAAW;KAAC,GAI3BK,IAAmBnF,EACvB,oBACA,yBACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OAAIsB,EAAkB,MAAA,KAAW,IAE7BxB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QAAI,WAAWyB;QACd,UAAAzB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;YAAI,WAAU;YACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;gBAAE,UAAA;YAAA,CAAsB;QAAA,CAC3B;IAAA,CACF,IAKFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAWyB;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAE1B,UAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YAAI,WAAU;YAEb,UAAA;gBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAuM;gCAAA,CAC9Q;gCACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCAAyD,UAAA;gCAAA,CAEvE;6BAAA;wBAAA,CACF;wBACAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,KAAA;4BAAE,WAAU;4BACV,UAAA;gCAAAU,EAAkB,MAAA;gCAAO;6BAAA;wBAAA,CAC5B;qBAAA;gBAAA,CACF;gBAGAxB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAkB,GAAA,CAAI,CAAC2B,GAASC,IAC/Bd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC7B,GAAA;4BAEC,MAAM0C,EAAQ,KAAA;4BACd,YAAYA,EAAQ,WAAA;4BACpB,WAAWA,EAAQ,UAAA;4BACnB,SAAS,IAAMJ,KAAA,OAAA,KAAA,IAAAA,EAAiBI,EAAQ,KAAA,EAAOA,EAAQ,WAAA;4BACvD,WAAU;4BAGV,UAAA;gCAAAb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAAc,MAAU,KACT5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oDAAK,WAAU;oDAAqE,UAAA;gDAAA,CAErF;gDAEFc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oDAAK,WAAU;oDAA2C,UAAA;wDAAA;wDACvDc,IAAQ;qDAAA;gDAAA,CACZ;6CAAA;wCAAA,CACF;wCACCP,KACCP,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAA,KAAK,KAAA,CAAMa,EAAQ,kBAAA,GAAqB,GAAG;gDAAE;6CAAA;wCAAA,CAChD;qCAAA;gCAAA,CAEJ;gCAGA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCACX,UAAA,EAAQ,KAAA;gCAAA,CACX;gCAGCqB,KACCP,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAA;gDAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oDAAK,UAAA;gDAAA,CAAW;gDACjBc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oDAAK,WAAU;oDAAqB,UAAA;wDAAA,KAAK,KAAA,CAAMa,EAAQ,kBAAA,GAAqB,GAAG;wDAAE;qDAAA;gDAAA,CAAO;6CAAA;wCAAA,CAC3F;wCACA3B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,GAAG,KAAK,KAAA,CAAM2B,EAAQ,kBAAA,GAAqB,GAAG,CAAC,CAAA,CAAA,CAAA;gDAAA;4CAAI;wCACrE,CACF;qCAAA;gCAAA,CACF;gCAIFb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACZ,UAAA;wCAAAa,EAAQ,OAAA,IACPb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4CAAK,WAAU;4CACd,UAAA;gDAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;oDAAA,CAA4I;gDAAA,CACnN;gDACC2B,EAAQ,OAAA;6CAAA;wCAAA,CACX;wCAGDA,EAAQ,aAAA,IACPb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4CAAK,WAAU;4CACd,UAAA;gDAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;oDAAA,CAA6I;gDAAA,CACpN;gDAAM;6CAAA;wCAAA,CAER;wCAGD2B,EAAQ,UAAA,IAAcA,EAAQ,UAAA,GAAa,KAC1Cb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4CAAK,WAAU;4CACd,UAAA;gDAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDACtE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;oDAAA,CAA4D;gDAAA,CACnI;gDACC2B,EAAQ,UAAA;gDAAW;6CAAA;wCAAA,CACtB;qCAAA;gCAAA,CAEJ;gCAGCL,KAAgBK,EAAQ,QAAA,IAAYA,EAAQ,QAAA,CAAS,MAAA,GAAS,KAC7Db,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAgD,UAAA;wCAAA,CAE/D;wCACAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAAa,EAAQ,QAAA,CAAS,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACZ,GAASC,IAC1CF,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wDAEC,WAAU;wDAEV,UAAA;4DAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gEAAI,WAAU;gEAAsC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAC7F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;gEAAA,CAAgD;4DAAA,CACvH;4DACCe;yDAAA;oDAAA,GANIC;gDAQR,CACCW,EAAQ,QAAA,CAAS,MAAA,IAAU,CAAA,IAAK,KAChCb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oDAAK,WAAU;oDAAkD,UAAA;wDAAA;wDAC9Da,EAAQ,QAAA,CAAS,MAAA,GAAS;wDAAE;qDAAA;gDAAA,CAChC;6CAAA;wCAAA,CAEJ;qCAAA;gCAAA,CACF;gCAIFb,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;oCAAO,WAAU;oCAA2H,UAAA;wCAAA;wCAE3Id,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;4CAAA,CAA+E;wCAAA,CACtJ;qCAAA;gCAAA,CACF;6BAAA;wBAAA,GA9GK2B,EAAQ,UAAA,IAAcC;gBAgH9B,CACH;gBAAA,aAAA,GAAA,EAAA,GAAA,CAGC,OAAA;oBAAI,WAAU;oBACb,UAAAd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wBAAK,WAAU;wBACd,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAA0B,MAAK;gCAAe,SAAQ;gCACnE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,UAAS;oCAAU,GAAE;oCAAmH,UAAS;gCAAA,CAAU;4BAAA,CACnK;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAAc,UAAA;4BAAA,CAAU;4BACxCA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAAqD,UAAA;4BAAA,CAAM;yBAAA;oBAAA,CAC7E;gBAAA,CACF;aAAA;QAAA,CACF;IAAA;AAGN;AAEAkB,GAAmB,WAAA,GAAc;AChMjC,MAAMW,KAA+C;IACnD,aAAa;IACb,aAAa;IACb,cAAc;IACd,SAAW;IACX,KAAO;IACP,mBAAmB;AACrB,GAGMC,KAAqD;IACzD,aAAa;IACb,aAAa;IACb,cAAc;IACd,SAAW;IACX,KAAO;IACP,mBAAmB;AACrB,GAEaC,KAA0C,CAAC,EACtD,MAAA9J,CAAAA,EACA,SAAA+J,CAAAA,EACA,MAAAC,IAAO,IAAA,EACP,WAAA3C,CAAAA,EACF,KAAM;IACJ,MAAM4C,IAAmBF,KAAWH,EAAAA,CAAkB5J,CAAI,CAAA,IAAK,aACzDkK,IAAOL,EAAAA,CAAe7J,CAAI,CAAA,EAE1BmK,IAAe9F,EACnB,oBACA,gBACA,CAAA,cAAA,EAAiB4F,CAAgB,EAAA,EACjC,CAAA,cAAA,EAAiBD,CAAI,EAAA,EACrB3C;IAGF,OACEwB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;QAAK,WAAWsB;QACd,UAAA;YAAAD,KAAQnC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBAAK,WAAU;gBAAsB,UAAAmC;YAAAA,CAAK;YACpDnC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBAAK,WAAU;gBAAsB,UAAA/H;YAAA,CAAK;SAAA;IAAA,CAC7C;AAEJ;AAEA8J,GAAY,WAAA,GAAc;AC9C1B,MAAMM,KAAgB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAA;AA6atB,IAAIC,KAAiB,CAAA;AAEd,MAAMC,KAAkB,MAAM;0NACnC7C,EAAU,MAAM;QACd,IAAI4C,GAAgB,CAAA;QAGpB,MAAME,IAAe,SAAS,aAAA,CAAc,OAAO;QACnD,OAAAA,EAAa,EAAA,GAAK,wBAClBA,EAAa,WAAA,GAAcH,IAGtB,SAAS,cAAA,CAAe,sBAAsB,KAAA,CACjD,SAAS,IAAA,CAAK,WAAA,CAAYG,CAAY,GACtCF,KAAiB,CAAA,CAAA,GAIZ,MAAM;YACX,MAAMG,IAAgB,SAAS,cAAA,CAAe,sBAAsB;YAChEA,KAAiB,SAAS,IAAA,CAAK,QAAA,CAASA,CAAa,KAAA,CACvD,SAAS,IAAA,CAAK,WAAA,CAAYA,CAAa,GACvCH,KAAiB,CAAA,CAAA;QACnB;IACF,GACC,EAAE;AACP,GClcMI,KAAsB,CAC1BvB,GACAwB,GACAC,MACiC;IACjC,IAAI,CAACA,KAAcD,GAEjB,OAAQA,GAAA;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IAAA;IAKb,MAAME,IAAe1B,EAAgB,MAAA;IAGrC,IAAI0B,KAAgB,KAAKA,KAAgB,GAAG;QAC1C,MAAMC,IAAc3B,EAAgB,IAAA,CAAK,CAAA4B,IAAOA,EAAI,QAAA,IAAYA,EAAI,QAAA,CAAS,MAAA,GAAS,CAAC,GACjFC,IAAa7B,EAAgB,IAAA,CAAK,CAAA4B,IAAOA,EAAI,OAAO;QAE1D,IAAID,KAAeE,GACjB,OAAO;IACT;IAIF,OAAO;AACT,GAEaC,KAA4C,CAAC,EACxD,iBAAA9B,CAAAA,EACA,YAAAwB,CAAAA,EACA,OAAAzC,CAAAA,EACA,cAAAgD,IAAe,CAAA,EACf,iBAAA7B,IAAkB,CAAA,CAAA,EAClB,cAAAC,IAAe,CAAA,CAAA,EACf,YAAAsB,IAAa,CAAA,CAAA,EACb,gBAAArB,CAAAA,EACA,aAAA4B,CAAAA,EACA,WAAA7D,CAAAA,EACF,KAAM;IAEJiD,GAAA;IAGA,MAAMa,IAAyB7F,oNAAAA,EAAQ,IAC9B4D,EAAgB,KAAA,CAAM,GAAG+B,CAAY,GAC3C;QAAC/B;QAAiB+B,CAAY;KAAC,GAG5BG,wNAAS9F,EAAQ,IACdmF,GAAoBU,GAAwBT,GAAYC,CAAU,GACxE;QAACQ;QAAwBT;QAAYC,CAAU;KAAC,GAE7CnB,IAAmBnF,EACvB,oBACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OAAIkD,EAAuB,MAAA,KAAW,IAElCpD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QAAI,WAAWyB;QACd,UAAAzB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;YAAI,WAAU;YACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAA0B,MAAK;4BAAe,SAAQ;4BACnE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,UAAS;gCAAU,GAAE;gCAAmH,UAAS;4BAAA,CAAU;wBAAA,CACnK;oBAAA,CACF;oBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;wBAAG,WAAU;wBAAwD,UAAA;oBAAA,CAEtE;oBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;wBAAE,WAAU;wBAAmC,UAAA;oBAAA,CAEhD;iBAAA;YAAA,CACF;QAAA,CACF;IAAA,CACF,IAKFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAWyB;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAEzB,UAAA,MAAW,YACVF,aAAAA,GAAAA,EAAAA,GAAAA,CAACkB,IAAA;YACC,iBAAiBkC;YACjB,OAAAlD;YACA,aAAa,KAAK,GAAA,CAAIkD,EAAuB,MAAA,EAAQ,CAAC;YACtD,iBAAA/B;YACA,cAAAC;YACA,gBAAAC;QAAA,KAAA,aAAA,GAAA,EAAA,GAAA,CAGD,OAAA;YAAI,WAAU;YACZ,UAAA6B,EAAuB,GAAA,CAAI,CAACrE,GAAgB6C,IAC3C5B,aAAAA,GAAAA,EAAAA,GAAAA,CAACC,IAAA;oBAEC,gBAAAlB;oBACA,OAAAmB;oBACA,gBAAgBmB;oBAChB,YAAY,CAAA;oBACZ,aAAa;oBACb,SAASE;oBACT,aAAA4B;gBAAA,GAPKpE,EAAe,UAAA,IAAcA,EAAe,KAAA,IAAS6C;QAS7D,CACH;IAAA;AAIR;AAEAqB,GAAa,WAAA,GAAc;AC5GpB,MAAMK,KAAgD,CAAC,EAC5D,gBAAAvE,CAAAA,EACA,OAAAmB,IAAQ;IAAE,MAAM;AAAA,CAAA,EAChB,WAAAZ,IAAY,EAAA,EACZ,SAAAF,CAAAA,EACA,eAAAmE,IAAgB,CAAA,CAAA,EAChB,WAAAC,IAAY,UAAA,EACd,KAAM;IACJ,MAAM1D,IAAc,MAAM;QACxBV,KAAA,QAAAA,EAAUL,EAAe,KAAA,EAAOA,EAAe,WAAA;IAAW,GAItD0E,IAAc1E,EAAe,KAAA,EAG7B2E,IAAY,MAAM;;QACtB,IAAIF,MAAc,YAGhB,OAAO,CAAA,YAAA,EAAA,CAAA,CADUG,IAAAA,CAAAnD,IAAAzB,EAAe,QAAA,KAAf,OAAA,KAAA,IAAAyB,CAAAA,CAA0B,EAAA,KAA1B,OAAA,KAAA,IAAAmD,EAA8B,WAAA,EAAA,KAAiB,WAClC,CAAA,wBAAA,EAA2BF,CAAW,EAAA;QAC/D;YAEL,MAAMG,IAAAA,CAAAA,CAAUC,IAAA9E,EAAe,MAAA,KAAf,OAAA,KAAA,IAAA8E,EAAuB,KAAA,CAAM,IAAA,CAAK,EAAA,KAAM;YACxD,OAAO,GAAGJ,CAAW,CAAA,IAAA,EAAOG,EAAQ,WAAA,EAAa,CAAA,OAAA,CAAA;QAAA;IACnD;IAGF,OACE5D,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;QACC,MAAMF,EAAe,KAAA;QACrB,YAAYA,EAAe,WAAA;QAC3B,WAAWA,EAAe,UAAA;QAC1B,SAASe;QACT,cAAc;YACZ,OAAOf,EAAe,KAAA;YACtB,WAAAyE;YACA,WAAW;QAAA;QAEb,WAAW,CAAA,iBAAA,EAAoBlE,CAAS,EAAA;QAExC,UAAAwB,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YACC,qBAAmBZ,EAAM,IAAA;YACzB,OAAO;gBACL,SAAS;gBACT,cAAc;gBACd,QAAQ,CAAA,UAAA,EAAaA,EAAM,IAAA,KAAS,SAAS,YAAY,SAAS,EAAA;gBAClE,iBAAiBA,EAAM,IAAA,KAAS,SAAS,YAAY;gBACrD,UAAU;gBACV,YAAY;gBACZ,YAAY;YAAA;YAId,UAAA;gBAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,OAAO;wBAAE,cAAcyC,IAAgB,QAAQ;oBAAA;oBAClD,UAAA;wBAAAvD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4BACC,OAAO;gCACL,OAAOE,EAAM,IAAA,KAAS,SAAS,YAAY;gCAC3C,aAAa;4BAAA;4BAGd,UAAAwD,EAAA;wBAAU;wBAEb1D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4BACC,OAAO;gCACL,OAAOE,EAAM,WAAA,IAAe;gCAC5B,gBAAgB;gCAChB,QAAQ;gCACR,UAAU;gCACV,YAAY;4BAAA;4BAGb,UAAAuD;wBAAA;qBACH;gBAAA,CACF;gBAGCF,KACCzC,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBACC,OAAO;wBACL,UAAU;wBACV,OAAOZ,EAAM,IAAA,KAAS,SAAS,YAAY;wBAC3C,WAAW;oBAAA;oBAEd,UAAA;wBAAA;wBACYF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4BAAO,UAAA;wBAAA,CAAM;qBAAA;gBAAA;aAC3B;QAAA;IAEJ;AAGN,GCjHa8D,IAAwE,CAAC,EACpF,gBAAA/E,CAAAA,EACA,OAAAmB,CAAAA,EACA,SAAA6D,IAAU,CAAA,CAAA,EACV,YAAAC,IAAa,CAAA,CAAA,EACb,SAAA5E,CAAAA,EACA,WAAAE,CAAAA,EACF,KAAM;IACJ,MAAMqB,IAAuB,KAAK,KAAA,CAAM5B,EAAe,kBAAA,GAAqB,GAAG,GAEzE0C,IAAmBnF,EACvB,gCACA,oDACA;QACE,gJAAgJ,CAACyH;QACjJ,oEAAoEA;IAAA,GAEtEzE,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEF,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;QACC,MAAMF,EAAe,KAAA;QACrB,YAAYA,EAAe,WAAA;QAC3B,WAAWA,EAAe,UAAA;QAC1B,SAAS,IAAMK,KAAA,OAAA,KAAA,IAAAA,EAAUL,EAAe,KAAA,EAAOA,EAAe,WAAA;QAC9D,cAAc;YACZ,OAAOA,EAAe,KAAA;YACtB,YAAYA,EAAe,kBAAA;QAAA;QAE7B,WAAW0C;QAEX,UAAAX,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YACC,WAAU;YACV,OAAOY;YACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;YAG1B,UAAA;gBAAAF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAAjB,EAAe,kBAAA,IAAsB,MACpCiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;oBAAA,CAAoC,IAEnDA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;oBAAA,CAAmC;gBAAA,CAEtD;gBAGAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAW1D,EACb,4DACA,iFACA,kCACAyH,IAAU,yBAAyB;oCAElC,UAAA,EAAe,KAAA;gCAAA,CAClB;gCAGChF,EAAe,kBAAA,IAAsB,OACpC+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAWxE,EACf,2GACAyC,EAAe,kBAAA,IAAsB,MACjC,yEACA;oCAEH,UAAA;wCAAA4B;wCAAqB;qCAAA;gCAAA,CACxB;6BAAA;wBAAA,CAEJ;wBAGCqD,KAAcjF,EAAe,MAAA,IAC5BiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;4BAAE,WAAW1D,EACZ,iDACAyH,IAAU,YAAY;4BAErB,UAAA,EAAe,MAAA;wBAAA,CAClB;wBAID,CAACA,KAAWhF,EAAe,QAAA,IAAYA,EAAe,QAAA,CAAS,MAAA,GAAS,KACvE+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAA/B,EAAe,QAAA,CAAS,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAAC0B,GAASmB,IACjD5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAEC,WAAU;wCAET,UAAAS;oCAAA,GAHImB;gCAMR7C,EAAe,QAAA,CAAS,MAAA,GAAS,KAChC+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCAA2C,UAAA;wCAAA;wCACvD/B,EAAe,QAAA,CAAS,MAAA,GAAS;wCAAE;qCAAA;gCAAA,CACvC;6BAAA;wBAAA,CAEJ;wBAID,CAACgF,KAAAA,CAAYhF,EAAe,aAAA,IAAiBA,EAAe,UAAA,KAC3D+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAA/B,EAAe,aAAA,IACdiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,WAAU;oCAAwI,UAAA;gCAAA,CAExJ;gCAEDjB,EAAe,UAAA,IAAcA,EAAe,UAAA,GAAa,KACxD+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCACb,UAAA;wCAAA/B,EAAe,UAAA;wCAAW;qCAAA;gCAAA,CAC7B;6BAAA;wBAAA,CAEJ;qBAAA;gBAAA,CAEJ;gBAGAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;wBAER,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;wBAAA;oBACJ;gBACF,CACF;aAAA;QAAA;IACF;AAGN,GC5IaiE,KAAsE,CAAC,EAClF,iBAAA9C,CAAAA,EACA,qBAAA+C,CAAAA,EACA,OAAAhE,CAAAA,EACA,wBAAAiE,IAAyB,CAAA,EACzB,uBAAAC,CAAAA,EACA,wBAAAC,CAAAA,EACA,WAAA/E,CAAAA,EACF,KAAM;IACJ,MAAMgF,IAAqBnD,EACxB,IAAA,CAAK,CAACoD,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,EAC1D,KAAA,CAAM,GAAGJ,CAAsB,GAE5B1C,IAAmBnF,EACvB,+BACA,0BACA,qEACA,aAAA,0BAAA;IACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWW;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAmD,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAC1G,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAAgD;4BAAA,CACvH;wBAAA,CACF;oBAAA,CACF;oBACAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;gCAAG,WAAU;gCAAgE,UAAA;4BAAA,CAE9E;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;gCAAE,WAAU;gCAAsD,UAAA;4BAAA,CAEnE;yBAAA;oBAAA,CACF;iBAAA;YAAA,CACF;YAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;wBAAE,WAAU;wBACV,UAAAkE;oBAAA,CACH;gBAAA,CACF;YAAA,CACF;YAGCI,EAAmB,MAAA,GAAS,KAC3BxD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAqC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAC5F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA6B;4BAAA,CACpG;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;gCAAG,WAAU;gCAAyC,UAAA;4BAAA,CAEvD;yBAAA;oBAAA,CACF;oBAEAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACZ,UAAAsE,EAAmB,GAAA,CAAI,CAACvF,GAAgB6C,IACvCd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gCAAwC,WAAU;gCAEjD,UAAA;oCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAW1D,EACd,2EACAsF,MAAU,IAAI,sDACdA,MAAU,IAAI,4DACd;4CAEC,UAAAA,IAAQ;wCAAA,CACX;oCAAA,CACF;oCAEA5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;4CACC,gBAAA/E;4CACA,OAAAmB;4CACA,SAAS,CAAA;4CACT,YAAY,CAAA;4CACZ,SAASkE;wCAAA;oCACX,CACF;iCAAA;4BAAA,GArBQrF,EAAe,KAAA,IAAS6C,CAsBlC,CACD;oBAAA,CACH;iBAAA;YAAA,CACF;YAIDT,EAAgB,MAAA,GAASgD,KACxBnE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAqC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAC5F,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA4D;4BAAA,CACnI;4BACAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;gCAAK,WAAU;gCACb,UAAA;oCAAAK,EAAgB,MAAA,GAASgD;oCAAuB;oCAA2BhD,EAAgB,MAAA,GAASgD,IAAyB,IAAI,MAAM;oCAAG;iCAAA;4BAAA,CAC7I;yBAAA;oBAAA,CACF;gBAAA,CACF;YAAA,CACF;YAIFrD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACZ,UAAA;oBAAAuD,KACCvD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wBACC,SAASuD;wBACT,WAAU;wBAEV,UAAA;4BAAArE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAAgK;4BAAA,CACvO;4BAAM;yBAAA;oBAAA;oBAKVc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wBACC,SAAS,MAAM;4BACTwD,EAAmB,MAAA,GAAS,KAAA,CAC9BF,KAAA,QAAAA,EAAwBE,CAAAA,CAAmB,CAAC,CAAA,CAAE,KAAA,EAAOA,CAAAA,CAAmB,CAAC,CAAA,CAAE,WAAA,CAAA;wBAC7E;wBAEF,WAAU;wBAEV,UAAA;4BAAAtE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA+E;4BAAA,CACtJ;4BAAM;yBAAA;oBAAA;iBAER;YAAA,CACF;YAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oBAAK,WAAU;oBAA2C,UAAA;gBAAA,CAE3D;YAAA,CACF;SAAA;IAAA;AAGN,GC1JayE,KAAkE,CAAC,EAC9E,gBAAA1F,CAAAA,EACA,gBAAA2F,CAAAA,EACA,eAAAC,IAAgB,UAAA,EAChB,OAAAzE,CAAAA,EACA,aAAA0E,IAAc,CAAA,CAAA,EACd,SAAAxF,CAAAA,EACA,SAAAyF,CAAAA,EACA,WAAAvF,CAAAA,EACF,KAAM;IACJ,MAAM,CAACwF,GAAWC,CAAY,CAAA,wNAAI5H,EAAS,CAAA,CAAK,GAE1C6H,IAAmB,MAAM;QAC7BD,EAAa,CAAA,CAAI,GACjBF,KAAA,QAAAA,EAAU9F;IAAc,GAGpBkG,IAAmB,MAAM;QAC7BF,EAAa,CAAA,CAAK;IAAA,GAGdjF,IAAc,MAAM;QACxBV,KAAA,QAAAA,EAAUL,EAAe,KAAA,EAAOA,EAAe,WAAA;IAAW,GAItDmG,IAAqB,MAAM;QAC/B,OAAQP,GAAA;YACN,KAAK;gBACH,OAAO,CAAA,CAAA,EAAID,CAAc,CAAA,CAAA,CAAA;YAC3B,KAAK;gBACH,OAAOA,EAAe,QAAA,CAAA;YACxB,KAAK;YACL;gBACE,OAAOA,EAAe,QAAA,CAAA;QAAS;IACnC,GAGIS,IAAkB7I,EACtB,6BACA,2CACA,8CACA,iFACA,eACA;QAAA,2BAAA;QAEE,wJAAwJqI,MAAkB;QAAA,kBAAA;QAG1K,gCAAgCA,MAAkB;QAAA,oBAAA;QAGlD,uCAAuCA,MAAkB;IAAA,GAE3DrF,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;QAAK,WAAU;QACd,UAAA;YAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAACf,GAAA;gBACC,MAAMF,EAAe,KAAA;gBACrB,YAAYA,EAAe,WAAA;gBAC3B,WAAWA,EAAe,UAAA;gBAC1B,SAASe;gBACT,cAAc;oBACZ,OAAOf,EAAe,KAAA;oBACtB,YAAYA,EAAe,kBAAA;oBAC3B,gBAAA2F;oBACA,eAAAC;gBAAA;gBAEF,WAAWQ;gBAEX,UAAAnF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oBACC,OAAO0B;oBACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;oBAC1B,cAAc8E;oBACd,cAAcC;oBAEb,UAAAC,EAAA;gBAAmB;YACtB;YAIDN,KAAeE,KACd9E,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAsB,UAAAjB,EAAe,KAAA;wBAAA,CAAM;wBACzDA,EAAe,MAAA,IACdiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA,EAAe,MAAA,CAAO,MAAA,GAAS,MAC5B,GAAGjB,EAAe,MAAA,CAAO,SAAA,CAAU,GAAG,GAAG,CAAC,CAAA,GAAA,CAAA,GAC1CA,EAAe,MAAA;wBAAA,CAErB;wBAEDA,EAAe,kBAAA,IAAsB,OACpC+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA;gCAAA,KAAK,KAAA,CAAM/B,EAAe,kBAAA,GAAqB,GAAG;gCAAE;6BAAA;wBAAA,CACvD;wBAEFiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAuD,UAAA;wBAAA,CAEtE;wBAEAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;wBAAA,CAA6J;qBAAA;gBAAA,CAC9K;YAAA,CACF;SAAA;IAAA,CAEJ;AAEJ,GCjHaoF,KAAwD,CAAC,EACpE,iBAAAjE,CAAAA,EACA,kBAAAkE,CAAAA,EACA,OAAAnF,CAAAA,EACA,kBAAAoF,IAAmB,CAAA,CAAA,EACnB,eAAAX,IAAgB,UAAA,EAChB,uBAAAP,CAAAA,EACA,iBAAAmB,CAAAA,EACA,WAAAjG,CAAAA,EACF,KAAM;IACJ,MAAM,CAACkG,GAAuBC,CAAwB,CAAA,4MAAItI,YAAAA,EAAsC,IAAI,GAG9FuI,wNAAmBnI,EAAQ,MAAM;QACrC,IAAI,CAAC8H,KAAoBlE,EAAgB,MAAA,KAAW,GAClD,OAAO;YAAE,MAAMkE;YAAkB,aAAa,aAAA,GAAA,IAAI;QAAI;QAGxD,IAAIM,IAAgBN;QACpB,MAAMO,IAAAA,aAAAA,GAAAA,IAAkB,IAAA;QAOxB,OAJ8B,CAAC;eAAGzE,CAAe;SAAA,CAC9C,IAAA,CAAK,CAACoD,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,EAGvC,OAAA,CAAQ,CAACxF,GAAgB6C,MAAU;YACvD,MAAM8C,IAAiB9C,IAAQ,GACzBiE,IAAQ9G,EAAe,KAAA;YAG7B6G,EAAY,GAAA,CAAIlB,GAAgB3F,CAAc;YAG9C,MAAM+G,IAAa,IAAI,OAAO,CAAA,GAAA,EAAMD,EAAM,OAAA,CAAQ,uBAAuB,MAAM,CAAC,CAAA,GAAA,CAAA,EAAO,IAAI;YAG3F,IAAIC,EAAW,IAAA,CAAKH,CAAa,GAC/BA,IAAgBA,EAAc,OAAA,CAAQG,GAAY,CAACC,IAC1C,GAAGA,CAAK,CAAA,WAAA,EAAcrB,CAAc,CAAA,EAAA,CAC5C;iBACI;gBAGL,MAAMsB,IAAWjH,EAAe,QAAA,IAAY,CAAA,CAAA;gBAC5C,IAAIkH,IAAW,CAAA;gBAEf,KAAA,MAAWxF,KAAWuF,EAAU;oBAC9B,MAAME,IAAe,IAAI,OAAO,CAAA,GAAA,EAAMzF,EAAQ,OAAA,CAAQ,uBAAuB,MAAM,CAAC,CAAA,GAAA,CAAA,EAAO,IAAI;oBAC/F,IAAIyF,EAAa,IAAA,CAAKP,CAAa,KAAK,CAACM,GAAU;wBACjDN,IAAgBA,EAAc,OAAA,CAAQO,GAAc,CAACH,IAAAA,CACnDE,IAAW,CAAA,GACJ,GAAGF,CAAK,CAAA,WAAA,EAAcrB,CAAc,CAAA,EAAA,CAAA,CAC5C;wBACD;oBAAA;gBACF;gBAIGuB,KAAAA,CACHN,KAAiB,CAAA,WAAA,EAAcjB,CAAc,CAAA,EAAA,CAAA;YAC/C;QACF,CACD,GAEM;YAAE,MAAMiB;YAAe,aAAAC;QAAA;IAAY,GACzC;QAACP;QAAkBlE,CAAe;KAAC,GAGhCgF,IAA0B,MAAM;QACpC,MAAM,EAAE,MAAAC,CAAAA,EAAM,aAAAR,CAAAA,CAAA,CAAA,GAAgBF;QAG9B,OAFcU,EAAK,KAAA,CAAM,wBAAwB,EAEpC,GAAA,CAAI,CAACC,GAAMzE,MAAU;YAChC,MAAM0E,IAAgBD,EAAK,KAAA,CAAM,wBAAwB;YAEzD,IAAIC,GAAe;gBACjB,MAAM5B,IAAiB,SAAS4B,CAAAA,CAAc,CAAC,CAAC,GAC1CvH,IAAiB6G,EAAY,GAAA,CAAIlB,CAAc;gBAErD,IAAI3F,GACF,OACEiB,aAAAA,GAAAA,EAAAA,GAAAA,CAACyE,IAAA;oBAEC,gBAAA1F;oBACA,gBAAA2F;oBACA,eAAAC;oBACA,OAAAzE;oBACA,aAAa,CAAA;oBACb,SAASkE;oBACT,SAAS,CAACrB,MAAQ;wBAChB0C,EAAyB1C,CAAG,GAC5BwC,KAAA,QAAAA,EAAkBxC;oBAAG;gBACvB,GAVK,CAAA,SAAA,EAAY2B,CAAc,CAAA,CAAA,EAAI9C,CAAK,EAAA;YAa9C;YAGF,OAAO5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gBAAkB,UAAAqG;YAAA,GAARzE,CAAa;QAAA,CAChC;IAAA,GAGGH,IAAmBnF,EACvB,wBACA,aACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWW;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAAF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACZ,UAAAmG,EAAA;YAAA,CACH;YAGCb,KAAoBnE,EAAgB,MAAA,GAAS,KAC5CnB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,MAAA;4BAAG,WAAU;4BACZ,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAqI;gCAAA,CAC5M;gCAAM;6BAAA;wBAAA,CAER;wBAAA,aAAA,GAAA,EAAA,GAAA,CAEC,OAAA;4BAAI,WAAU;4BACZ,UAAAmB,EACE,IAAA,CAAK,CAACoD,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,EAC1D,GAAA,CAAI,CAACxF,GAAgB6C,IACpBd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAEC,WAAWxE,EACT,wEACA;wCACE,8EAAA,CACEkJ,KAAA,OAAA,KAAA,IAAAA,EAAuB,KAAA,MAAUzG,EAAe,KAAA;wCAClD,+CAAA,CACEyG,KAAA,OAAA,KAAA,IAAAA,EAAuB,KAAA,MAAUzG,EAAe,KAAA;oCAAA;oCAKtD,UAAA;wCAAAiB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAACyE,IAAA;gDACC,gBAAA1F;gDACA,gBAAgB6C,IAAQ;gDACxB,eAAA+C;gDACA,OAAAzE;gDACA,aAAa,CAAA;gDACb,SAASkE;4CAAA;wCAAA,CAEb;wCAGApE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;gDACC,gBAAA/E;gDACA,OAAAmB;gDACA,SAAS,CAAA;gDACT,YAAY,CAAA;gDACZ,SAASkE;4CAAA;wCACX,CACF;qCAAA;gCAAA,GAhCKrF,EAAe,KAAA,IAAS6C;wBAkChC,CACL;qBAAA;gBAAA,CACF;YAAA,CACF;SAAA;IAAA;AAIR,GCnLa2E,KAAoE,CAAC,EAChF,iBAAApF,CAAAA,EACA,QAAAjJ,CAAAA,EACA,OAAAgI,CAAAA,EACA,qBAAAgE,CAAAA,EACA,WAAAsC,CAAAA,EACA,uBAAApC,CAAAA,EACA,WAAAqC,CAAAA,EACA,WAAAnH,CAAAA,EACF,KAAM;IACJ,MAAM,CAACoH,GAAWC,CAAY,CAAA,wNAAIxJ,EAASjF,EAAO,QAAA,KAAa,CAAA,CAAK,GAC9D,CAAC0O,GAAaC,CAAc,CAAA,GAAI1J,qNAAAA,EAAS,CAAA,CAAK;IAcpD,0NAZAuC,EAAU,MAAM;QACd,IAAIxH,EAAO,OAAA,IAAWA,EAAO,OAAA,GAAU,GAAG;YACxC,MAAM4O,IAAQ,WAAW,MAAM;gBAC7BH,EAAa,CAAA,CAAI,GACjBE,EAAe,CAAA,CAAI;YAAA,GAClB3O,EAAO,OAAO;YACjB,OAAO,IAAM,aAAa4O,CAAK;QAAA,OAE/BD,EAAe,CAAA,CAAI;IACrB,GACC;QAAC3O,EAAO,OAAO;KAAC,GAEf,CAACwO,KAAavF,EAAgB,MAAA,KAAW,GAC3C,OAAO;IAGT,MAAM4F,IAAqB7O,EAAO,kBAAA,IAAsB,GAClDkL,IAAyBjC,EAAgB,KAAA,CAAM,GAAG4F,CAAkB,GAEpEC,IAA4B,CAACrI,GAAcO,MAAuB;QACtEkF,KAAA,QAAAA,EAAwBzF,GAAMO;IAAU,GAGpC+H,IAAgB,MAAM;QAC1BN,EAAa,CAAA,CAAK,GAClBF,KAAA,QAAAA;IAAY,GAIRS,IAAgB,MAAM;QAC1B,OAAQhP,EAAO,WAAA,EAAA;YACb,KAAK;gBACH,OAAOgM,IACLlE,aAAAA,GAAAA,EAAAA,GAAAA,CAACiE,IAAA;oBACC,iBAAiBb;oBACjB,qBAAAc;oBACA,OAAAhE;oBACA,wBAAwB6G;oBACxB,uBAAuBC;oBACvB,wBAAwBP;gBAAA,KAExB;YAEN,KAAK;gBACH,OACEzG,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAuB,GAAA,CAAI,CAACjB,GAAgB6C,IAC3C5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;4BAEC,gBAAA/E;4BACA,OAAAmB;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAAS8G;wBAAA,GALJjI,EAAe,KAAA,IAAS6C;gBAOhC,CACH;YAGJ,KAAK;gBACH,OAAOwB,EAAuB,MAAA,GAAS,IACrCtC,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;gCAAA,CAAmC;gCAClDc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCACb,UAAA;wCAAAsC,EAAuB,MAAA;wCAAO;wCAAmBA,EAAuB,MAAA,GAAS,IAAI,OAAO;wCAAG;qCAAA;gCAAA,CAClG;6BAAA;wBAAA,CACF;wBACApD,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;4BACC,gBAAgBV,CAAAA,CAAuB,CAAC,CAAA;4BACxC,OAAAlD;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAAS8G;wBAAA;wBAEV5D,EAAuB,MAAA,GAAS,KAC/BtC,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAgD,UAAA;gCAAA;gCAC3DsC,EAAuB,MAAA,GAAS;gCAAE;gCAAqBA,EAAuB,MAAA,GAAS,IAAI,MAAM;6BAAA;wBAAA,CACrG;qBAAA;gBAAA,CAEJ,IACE;YAEN,KAAK;gBACH,OAAOc,IACLlE,aAAAA,GAAAA,EAAAA,GAAAA,CAACoF,IAAA;oBACC,iBAAiBhC;oBACjB,kBAAkBc;oBAClB,OAAAhE;oBACA,kBAAkB,CAAA;oBAClB,eAAc;oBACd,uBAAuB8G;gBAAA,KAEvB;YAEN,KAAK;gBACH,OACElG,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;wCAAA,CAAmC;wCAClDA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,WAAU;4CAAyD,UAAA;wCAAA,CAEzE;qCAAA;gCAAA,CACF;gCACCyG,KACCzG,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;oCACC,SAASiH;oCACT,WAAU;oCACV,cAAW;oCAEX,UAAAjH,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAAuB;oCAAA,CAC9F;gCAAA;6BACF;wBAAA,CAEJ;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAA,EAAuB,GAAA,CAAI,CAACjB,GAAgB6C,IAC3C5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;oCAEC,gBAAA/E;oCACA,OAAAmB;oCACA,SAAS,CAAA;oCACT,YAAY,CAAA;oCACZ,SAAS8G;gCAAA,GALJjI,EAAe,KAAA,IAAS6C;wBAOhC,CACH;qBAAA;gBAAA,CACF;YAGJ;gBACE,OACE5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAuB,GAAA,CAAI,CAACjB,GAAgB6C,IAC3C5B,aAAAA,GAAAA,EAAAA,GAAAA,CAACC,IAAA;4BAEC,gBAAAlB;4BACA,OAAAmB;4BACA,gBAAgB,CAAA;4BAChB,YAAY,CAAA;4BACZ,SAAS8G;wBAAA,GALJjI,EAAe,KAAA,IAAS6C;gBAOhC,CACH;QAAA;IAEN,GAGIH,IAAmBnF,EACvB,8BACA,2CACA;QACE,2BAA2B,CAACsK;QAC5B,6BAA6BA;QAC7B,wCAAwC1O,EAAO,WAAA,KAAgB;QAC/D,QAAQA,EAAO,WAAA,KAAgB;QAC/B,4DAA4DA,EAAO,WAAA,KAAgB;IAAA,GAErFoH,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWW;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAC1B,uBAAqBhI,EAAO,OAAA;QAC5B,mBAAiBsO;QAEhB,UAAA;YAAAU,EAAA;YAGAhP,EAAO,aAAA,KAAkB,CAAA,KACxB8H,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oBAAK,WAAU;oBAA2C,UAAA;gBAAA,CAE3D;YAAA,CACF;SAAA;IAAA;AAIR,GC3MamH,KAAsD,CAAC,EAClE,SAAAzJ,CAAAA,EACA,OAAAwC,CAAAA,EACA,uBAAAkE,CAAAA,EACA,WAAA9E,CAAAA,EACF,KAAM;IACJ,MAAM8H,IAAS1J,EAAQ,IAAA,KAAS,QAC1B2J,IAAc3J,EAAQ,IAAA,KAAS,aAE/B4J,IAAiBhL,EACrB,uBACA,0BACA;QACE,oBAAoB8K;IAAA,GAEtB9H,IAGIiI,IAAgBjL,EACpB,qDACA;QACE,2DAA2D8K;QAC3D,kEAAkEC;QAClE,yEAAyE3J,EAAQ,IAAA,KAAS;IAAA,IAIxFgE,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA,GAErBsH,IAAa,CAACC,IACXA,EAAU,kBAAA,CAAmB,EAAA,EAAI;YAAE,MAAM;YAAW,QAAQ;QAAA,CAAW;IAGhF,OACE3G,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWwG;QACX,OAAO5F;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAGzB,UAAA;YAAA,CAACkH,KACApH,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBAAqB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;oBAC5E,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;oBAAA,CAA6B;gBAAA,CACpG;YAAA,CACF;YAGDoH,KACCpH,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBAA2C,MAAK;oBAAO,QAAO;oBAAe,SAAQ;oBAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;oBAAA,CAAsE;gBAAA,CAC7I;YAAA,CACF;YAAA,aAAA,GAAA,EAAA,IAAA,CAID,OAAA;gBAAI,WAAW,CAAA,cAAA,EAAiBoH,IAAS,cAAc,aAAa,CAAA,OAAA,CAAA;gBAEnE,UAAA;oBAAApH,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAWuH;wBACd,UAAAvH,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACZ,UAAAtC,EAAQ,OAAA;wBAAA,CACX;oBAAA,CACF;oBAGAsC,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAW1D,EACd,iDACA;4BAAE,cAAc8K;wBAAA;wBAEf,UAAAI,EAAW9J,EAAQ,SAAS;oBAAA,CAC/B;oBAGCA,EAAQ,eAAA,IAAmBA,EAAQ,eAAA,CAAgB,MAAA,GAAS,KAC3DoD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBAEb,UAAA;4BAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gCAAI,WAAU;gCACb,UAAA;oCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAA2C,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA6B;oCAAA,CACpG;oCACAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wCAAK,WAAU;wCACb,UAAA;4CAAApD,EAAQ,eAAA,CAAgB,MAAA;4CAAO;4CAAgBA,EAAQ,eAAA,CAAgB,MAAA,GAAS,IAAI,MAAM;4CAAG;yCAAA;oCAAA,CAChG;iCAAA;4BAAA,CACF;4BAGAsC,aAAAA,GAAAA,EAAAA,GAAAA,CAACuG,IAAA;gCACC,iBAAiB7I,EAAQ,eAAA;gCACzB,QAAQ;oCACN,aAAa;oCACb,SAAS;oCACT,oBAAoB;oCACpB,eAAe,CAAA;oCACf,UAAU,CAAA;oCACV,SAAS;gCAAA;gCAEX,OAAAwC;gCACA,uBAAAkE;gCACA,WAAU;4BAAA;yBACZ;oBAAA,CACF;iBAAA;YAAA,CAEJ;SAAA;IAAA;AAGN,GC7GasD,KAAkD,CAAC,EAC9D,aAAAC,IAAc,sBAAA,EACd,UAAAC,IAAW,CAAA,CAAA,EACX,aAAAC,IAAc,CAAA,CAAA,EACd,OAAA3H,CAAAA,EACA,eAAA4H,CAAAA,EACA,WAAAxI,CAAAA,EACF,KAAM;IACJ,MAAM,CAAC5B,GAASqK,CAAU,CAAA,wNAAI5K,EAAS,EAAE,GACnC,CAAC6K,GAAiBC,CAAkB,CAAA,GAAI9K,qNAAAA,EAAS,CAAA,CAAK,GACtD,CAAC+K,GAAqBC,CAAsB,CAAA,wNAAIhL,EAAmB,CAAA,CAAE,GACrEiL,8MAAW5I,SAAAA,EAA4B,IAAI,GAE3C6I,IAAoB,CAACC,MAA8C;QACvE,MAAMhP,IAAQgP,EAAE,MAAA,CAAO,KAAA;QAIvB,IAHAP,EAAWzO,CAAK,GAGZA,EAAM,IAAA,CAAA,KAAUuO,EAAY,MAAA,GAAS,GAAG;YAC1C,MAAMU,IAAWV,EAAY,MAAA,CAAO,CAAA,IAClCW,EAAW,WAAA,CAAA,EAAc,QAAA,CAASlP,EAAM,WAAA,EAAa;YAEvD6O,EAAuBI,CAAQ,GAC/BN,EAAmBM,EAAS,MAAA,GAAS,CAAC;QAAA,OAEtCN,EAAmB,CAAA,CAAK;QAItBG,EAAS,OAAA,IAAA,CACXA,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,GAAS,QAChCA,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,GAAS,GAAG,KAAK,GAAA,CAAIA,EAAS,OAAA,CAAQ,YAAA,EAAc,GAAG,CAAC,CAAA,EAAA,CAAA;IACjF,GAGIK,IAAgB,CAACH,MAA0C;QAC3DA,EAAE,GAAA,KAAQ,WAAW,CAACA,EAAE,QAAA,IAAA,CAC1BA,EAAE,cAAA,CAAA,GACFI,EAAA,CAAA;IACF,GAGIA,IAAa,MAAM;QACvB,MAAMC,IAAiBjL,EAAQ,IAAA,CAAA;QAC3BiL,KAAkB,CAACf,KAAYE,KAAAA,CACjCA,EAAca,CAAc,GAC5BZ,EAAW,EAAE,GACbE,EAAmB,CAAA,CAAK,GAGpBG,EAAS,OAAA,IAAA,CACXA,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,GAAS,MAAA,CAAA;IAEpC,GAGIQ,IAAwB,CAACJ,MAAuB;QACpDT,EAAWS,CAAU,GACrBP,EAAmB,CAAA,CAAK,GACpBG,EAAS,OAAA,IACXA,EAAS,OAAA,CAAQ,KAAA,CAAA;IACnB,GAGI3G,IAAmBnF,EACvB,qBACA,YACAgD,IAGIuJ,IAAevM,EACnB,8EACA,+DACA,kDACA,sFACA,sGACA,qCACA;QACE,iCAAiCsL;IAAA,IAI/BkB,IAAoBxM,EACxB,wEACA,oCACA;QACE,4CAA4CoB,EAAQ,IAAA,CAAA,KAAU,CAACkK;QAC/D,qFAAqF,CAAClK,EAAQ,IAAA,MAAUkK;IAAA,IAItGlG,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWW;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAGzB,UAAA;YAAA8H,KAAmBE,EAAoB,MAAA,GAAS,KAC/ClI,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACZ,UAAAkI,EAAoB,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACM,GAAY5G,IAChD5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBAEC,SAAS,IAAM4I,EAAsBJ,CAAU;wBAC/C,WAAU;wBAET,UAAAA;oBAAA,GAJI5G;YAMR,CACH;YAIFd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,YAAA;wBACC,KAAKoI;wBACL,OAAO1K;wBACP,UAAU2K;wBACV,WAAWI;wBACX,aAAAd;wBACA,UAAAC;wBACA,MAAM;wBACN,WAAWiB;wBACX,OAAO;4BAAE,WAAW;4BAAQ,WAAW;wBAAA;oBAAQ;oBAIjD7I,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBACC,SAAS0I;wBACT,UAAU,CAAChL,EAAQ,IAAA,CAAA,KAAUkK;wBAC7B,WAAWkB;wBACX,cAAW;wBAEX,UAAA9I,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;4BACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;4BAAA,CAAmC;wBAAA,CAC1G;oBAAA;iBACF;YAAA,CACF;YAGAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wBAAK,UAAA;oBAAA,CAA6C;oBACnDc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;wBAAK,WAAWxE,EACf,mCACA;4BAAE,aAAaoB,EAAQ,MAAA,GAAS;wBAAA;wBAE/B,UAAA;4BAAAA,EAAQ,MAAA;4BAAO;yBAAA;oBAAA,CAClB;iBAAA;YAAA,CACF;SAAA;IAAA;AAGN,GCzJaqL,KAA0D,CAAC,EACtE,UAAAC,CAAAA,EACA,QAAA9Q,CAAAA,EACA,OAAAgI,CAAAA,EACA,WAAA+I,IAAY,CAAA,CAAA,EACZ,eAAAnB,CAAAA,EACA,uBAAA1D,CAAAA,EACA,WAAA9E,CAAAA,EACF,KAAM;IACJ,MAAM4J,QAAiB1J,+MAAAA,EAAuB,IAAI,GAC5C2J,8MAAuB3J,SAAAA,EAAuB,IAAI;0NAGxDE,EAAU,MAAM;QACVwJ,EAAe,OAAA,IACjBA,EAAe,OAAA,CAAQ,cAAA,CAAe;YAAE,UAAU;QAAA,CAAU;IAC9D,GACC;QAACF,CAAQ;KAAC;IAEb,MAAMvH,IAAmBnF,EACvB,yBACA,mDACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA,GAGrBkJ,IAAkBlR,EAAO,WAAA,GAC3B8Q,EAAS,KAAA,CAAM,CAAC9Q,EAAO,WAAW,IAClC8Q;IAEJ,OACElI,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWW;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAAF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBACC,KAAKmJ;gBACL,WAAU;gBAET,UAAA,EAAgB,MAAA,KAAW,IAC1BrI,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAA2C,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA6B;4BAAA,CACpG;wBAAA,CACF;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;4BAAG,WAAU;4BAA8D,UAAA;wBAAA,CAE5E;wBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;4BAAE,WAAU;4BAAoD,UAAA;wBAAA,CAEjE;qBAAA;gBAAA,CACF,IAEAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAAuI,EAAAA,QAAAA,EAAA;oBACG,UAAA;wBAAAD,EAAgB,GAAA,CAAI,CAAC1L,IACpBsC,aAAAA,GAAAA,EAAAA,GAAAA,CAACmH,IAAA;gCAEC,SAAAzJ;gCACA,OAAAwC;gCACA,uBAAAkE;4BAAA,GAHK1G,EAAQ,EAAA;wBAQhBuL,KAAa/Q,EAAO,qBAAA,KAA0B,CAAA,KAC7C4I,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCAC5E,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA6B;oCAAA,CACpG;gCAAA,CACF;gCAAA,aAAA,GAAA,EAAA,GAAA,CACC,OAAA;oCAAI,WAAU;oCACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wCAAI,WAAU;wCACb,UAAA;4CAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;4CAAA,CAAmE;4CAClFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,gBAAgB;gDAAA;4CAAA,CAAU;4CACrHA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAmE,OAAO;oDAAE,gBAAgB;gDAAA;4CAAO,CAAG;yCAAA;oCAAA,CACvH;gCAAA,CACF;6BAAA;wBAAA,CACF;wBAGFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,KAAKkJ;wBAAA,CAAgB;qBAAA;gBAAA,CAC5B;YAAA;YAKHhR,EAAO,iBAAA,IAAqBA,EAAO,WAAA,IAAeA,EAAO,WAAA,CAAY,MAAA,GAAS,KAAK8Q,EAAS,MAAA,KAAW,KACtGlI,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAAgD,UAAA;oBAAA,CAAkB;oBACjFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACZ,UAAA9H,EAAO,WAAA,CAAY,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACsQ,GAAY5G,IAC/C5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCAEC,SAAS,IAAM8H,KAAA,OAAA,KAAA,IAAAA,EAAgBU;gCAC/B,WAAU;gCAET,UAAAA;4BAAA,GAJI5G;oBAMR,CACH;iBAAA;YAAA,CACF;YAID1J,EAAO,cAAA,KAAmB,CAAA,KAAS4P,KAClC9H,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC0H,IAAA;oBACC,aAAaxP,EAAO,WAAA,IAAe;oBACnC,UAAU+Q;oBACV,aAAa/Q,EAAO,WAAA;oBACpB,OAAAgI;oBACA,eAAA4H;gBAAA;YACF,CACF;SAAA;IAAA;AAIR,GC9HawB,KAAwD,CAAC,EACpE,QAAApR,CAAAA,EACA,OAAAgI,CAAAA,EACA,OAAA2F,IAAQ,cAAA,EACR,UAAA0D,IAAW,kCAAA,EACX,QAAQC,CAAAA,EACR,UAAAC,CAAAA,EACA,eAAA3B,CAAAA,EACA,uBAAA1D,CAAAA,EACA,qBAAAsF,CAAAA,EACA,2BAAAC,CAAAA,EACA,gBAAAC,IAAiB,CAAA,CAAA,EACjB,yBAAAC,IAA0B,CAAA,CAAA,EAC1B,6BAAAC,CAAAA,EACA,WAAAxK,CAAAA,EACF,KAAM;IACJ,MAAM,CAACyK,GAAgBC,CAAiB,CAAA,wNAAI7M,EAASjF,EAAO,QAAA,IAAY,CAAA,CAAK,GACvE,CAAC8Q,GAAUiB,CAAW,CAAA,wNAAI9M,EAAwB,CAAA,CAAE,GACpD,CAAC8L,GAAWiB,CAAY,CAAA,4MAAI/M,YAAAA,EAAS,CAAA,CAAK,GAC1C,CAACgN,GAAeC,CAAgB,CAAA,wNAAIjN,EAAS,CAAA,CAAK,GAElDkN,IAASb,MAAqB,KAAA,IAAYA,IAAmBO;8MAGnErK,YAAAA,EAAU,MAAM;QACd,IAAIxH,EAAO,kBAAA,IAAsBA,EAAO,cAAA,IAAkB8Q,EAAS,MAAA,KAAW,GAAG;YAC/E,MAAMsB,IAA8B;gBAClC,IAAI;gBACJ,MAAM;gBACN,SAASpS,EAAO,cAAA;gBAChB,WAAA,aAAA,GAAA,IAAe,KAAA;YAAK;YAEtB+R,EAAY;gBAACK,CAAc;aAAC;QAAA;IAC9B,GACC;QAACpS,EAAO,kBAAA;QAAoBA,EAAO,cAAA;QAAgB8Q,EAAS,MAAM;KAAC,4MAGtEtJ,aAAAA,EAAU,MAAM;QACd,IAAIgK,KAAuBA,EAAoB,MAAA,GAAS,KAAKG,GAAyB;YACpF,MAAMU,IAA2B;gBAC/B,IAAI,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAA,CAAK,EAAA;gBACtB,MAAM;gBACN,SAASZ,IACL,CAAA,UAAA,EAAaA,CAAyB,CAAA,0CAAA,CAAA,GACtC;gBACJ,WAAA,aAAA,GAAA,IAAe,KAAA;gBACf,iBAAiBD;YAAA;YAIfF,MAAqB,KAAA,KACvBQ,EAAkB,CAAA,CAAI,GAIxBC,EAAY,CAAAO,IAEaA,EAAK,IAAA,CAAK,CAAAC,IAAOA,EAAI,EAAA,CAAG,UAAA,CAAW,OAAO,CAAC,IAEzDD,EAAK,GAAA,CAAI,CAAA,IACdC,EAAI,EAAA,CAAG,UAAA,CAAW,OAAO,IAAIF,IAAcE,KAGxC,CAAC;uBAAGD;oBAAMD,CAAW;iBAC7B;QAAA;IACH,GACC;QAACb;QAAqBG;QAAyBF;QAA2BH,CAAgB;KAAC;IAE9F,MAAMkB,IAAe,MAAM;QACrBjB,IACFA,EAAA,IAEAO,EAAkB,CAACD,CAAc,GAEnCK,EAAiB,CAAA,CAAI;IAAA,GAGjBO,IAAoB,OAAOC,MAA2B;QAC1D,IAAI,CAAC9C,EAAe,CAAA;QAGpB,MAAM+C,IAA2B;YAC/B,IAAI,CAAA,KAAA,EAAQ,KAAK,GAAA,CAAA,CAAK,EAAA;YACtB,MAAM;YACN,SAASD;YACT,WAAA,aAAA,GAAA,IAAe,KAAA;QAAK;QAGtBX,EAAY,CAAAO,IAAQ,CAAC;mBAAGA;gBAAMK,CAAW;aAAC,GAC1CX,EAAa,CAAA,CAAI;QAEjB,IAAI;YAEF,MAAMY,IAAmB,MAAMhD,EAAc8C,CAAc;YAC3DX,EAAY,CAAAO,IAAQ,CAAC;uBAAGA;oBAAMM,CAAgB;iBAAC;QAAA,EAAA,OACxC1N,GAAO;YACd,QAAQ,KAAA,CAAM,0BAA0BA,CAAK;YAC7C,MAAM2N,IAA4B;gBAChC,IAAI,CAAA,MAAA,EAAS,KAAK,GAAA,CAAA,CAAK,EAAA;gBACvB,MAAM;gBACN,SAAS;gBACT,WAAA,aAAA,GAAA,IAAe,KAAA;YAAK;YAEtBd,EAAY,CAAAO,IAAQ,CAAC;uBAAGA;oBAAMO,CAAY;iBAAC;QAAA,SAC7C;YACEb,EAAa,CAAA,CAAK;QAAA;IACpB,GAIIc,IAAoB,MAAM;QAC9B,OAAQ9S,EAAO,IAAA,EAAA;YACb,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAAA;IAClB,GAcIuJ,IAAmBnF,EACvB,wBACA,sDAAA,CAZyB,MAAM;QAC/B,OAAQpE,EAAO,QAAA,EAAA;YACb,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAAA;IAClB,CAAA,EAMA,GACAoH,IAGI2L,IAAc3O,EAClB,iHACA0O,EAAA,GACA;QACE,0CAA0C,CAACX;QAC3C,yBAAyBA;IAAA,IAIvB3I,KAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWW;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAAF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAWiL;gBACb,UAAAZ,KACCvJ,aAAAA,GAAAA,EAAAA,IAAAA,CAAAuI,EAAAA,QAAAA,EAAA;oBAEE,UAAA;wBAAAvI,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAA6B;4CAAA,CACpG;wCAAA,CACF;wCAAA,aAAA,GAAA,EAAA,IAAA,CACC,OAAA;4CACC,UAAA;gDAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oDAAG,WAAU;oDAAyB,UAAA6F;gDAAAA,CAAM;gDAC7C7F,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;oDAAE,WAAU;oDAAyB,UAAAuJ;gDAAA,CAAS;6CAAA;wCAAA,CACjD;qCAAA;gCAAA,CACF;gCACAzI,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCAEZ,UAAA;wCAAA4I,KAAuBA,EAAoB,MAAA,GAAS,KAAKI,KACxD9J,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4CACC,SAAS,MAAM;gDACb8J,EAAA,GACAG,EAAY,CAAAO,IAAQA,EAAK,MAAA,CAAO,CAAAC,IAAO,CAACA,EAAI,EAAA,CAAG,UAAA,CAAW,OAAO,CAAC,CAAC;4CAAA;4CAErE,WAAU;4CACV,cAAW;4CACX,OAAM;4CAEN,UAAAzK,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAsL;4CAAA,CAC7P;wCAAA;wCAIJA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4CACC,SAAS0K;4CACT,WAAU;4CACV,cAAW;4CAEX,UAAA1K,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;gDACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;gDAAA,CAAuB;4CAAA,CAC9F;wCAAA;qCACF;gCAAA,CACF;6BAAA;wBAAA,CACF;wBAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC+I,IAAA;4BACC,UAAAC;4BACA,QAAQ;gCACN,GAAG9Q,CAAAA;gCACH,gBAAA0R;4BAAA;4BAEF,OAAA1J;4BACA,WAAA+I;4BACA,eAAeW,IAAiBe,IAAoB,KAAA;4BACpD,uBAAAvG;4BACA,WAAU;wBAAA;qBACZ;gBAAA,CACF;YAAA,CAEJ;YAGC,CAACiG,KACAvJ,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;gBACC,SAAS4J;gBACT,WAAWpO,EACT,kGACA,iFACA;gBAEF,cAAW;gBAEX,UAAA;oBAAA0D,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;wBACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;wBAAA,CAAgK;oBAAA,CACvO;oBAGC,CAACmK,KACAnK,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;oBAAA,CAAyE;iBAAA;YAAA;YAM7FqK,KACCrK,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBAA4H,UAAA;YAAA,CAE3I;SAAA;IAAA;AAIR,GC5PakL,KAA0D,CAAC,EACtE,OAAArF,CAAAA,EACA,OAAA3F,CAAAA,EACA,aAAAiL,IAAc,CAAA,CAAA,EACd,aAAAC,IAAc,CAAA,CAAA,EACd,UAAA3B,CAAAA,EACA,UAAA4B,CAAAA,EACA,YAAAC,IAAa,CAAA,CAAA,EACb,WAAAhM,CAAAA,EACF,KAAM;IACJ,MAAM,CAACiM,GAAaC,CAAc,CAAA,4MAAIrO,YAAAA,EAAS,EAAE,GAC3C,CAACsO,GAAiBC,CAAkB,CAAA,wNAAIvO,EAAS,CAAA,CAAK,GACtD,CAACwO,GAAUC,CAAW,CAAA,6MAAIzO,WAAAA,EAAS,CAAA,CAAK;0NAG9CuC,EAAU,MAAM;QACd,MAAMmM,IAAc,MAAM;YACxBD,EAAY,OAAO,UAAA,GAAa,GAAG;QAAA;QAGrC,OAAAC,EAAA,GACA,OAAO,gBAAA,CAAiB,UAAUA,CAAW,GACtC,IAAM,OAAO,mBAAA,CAAoB,UAAUA,CAAW;IAAA,GAC5D,EAAE;IAEL,MAAMC,IAAqB,CAACxD,MAA2C;QACrE,MAAMhP,IAAQgP,EAAE,MAAA,CAAO,KAAA;QACvBkD,EAAelS,CAAK,GACpB+R,KAAA,QAAAA,EAAW/R;IAAK,GAGZyS,IAAoB,MAAM;QAC9BP,EAAe,EAAE,GACjBH,KAAA,QAAAA,EAAW;IAAE,GAGTW,IAAgB1P,EACpB,yBACA,iGACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAWkL;QACX,OAAOtK;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;wBAAG,WAAU;wBACX,UAAA6F;oBAAAA,CACH;oBAEA/E,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBAEZ,UAAA;4BAAA6K,KAAYlC,KACXzJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASyJ;gCACT,WAAU;gCACV,OAAM;gCAEN,UAAAzJ,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;oCAER,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAuB;gCAAA;4BAC9F;4BAKHmL,KACCnL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASyJ;gCACT,WAAU;gCACV,OAAO2B,IAAc,mBAAmB;gCAExC,UAAApL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCACC,WAAW1D,EACT,8EACA;wCAAE,cAAc8O;oCAAA;oCAElB,MAAK;oCACL,QAAO;oCACP,SAAQ;oCAER,UAAApL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAkB;gCAAA;4BACzF;yBACF;oBAAA,CAEJ;iBAAA;YAAA,CACF;YAGCsL,KAAc,CAACF,KACdtK,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAWxE,EACd,0DACA;4BACE,2CAA2CmP;wBAAA;wBAI7C,UAAA;4BAAAzL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAA8C;gCAAA,CACrH;4BAAA,CACF;4BAGAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,SAAA;gCACC,MAAK;gCACL,OAAOuL;gCACP,UAAUO;gCACV,SAAS,IAAMJ,EAAmB,CAAA,CAAI;gCACtC,QAAQ,IAAMA,EAAmB,CAAA,CAAK;gCACtC,aAAY;gCACZ,WAAWpP,EACT,sHACA,mFACA,yGACA;4BACF;4BAIDiP,KACCvL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAAS+L;gCACT,WAAU;gCACV,OAAM;gCAEN,UAAA/L,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAuB;gCAAA,CAC9F;4BAAA;yBACF;oBAAA,CAEJ;oBAGCuL,KACCvL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAAgD,UAAA;oBAAA,CAE/D;iBAAA;YAAA,CAEJ;YAID,CAACoL,KACAtK,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;4BAAA,CAAoC;4BACnDA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,UAAA;4BAAA,CAAoB;yBAAA;oBAAA,CAC5B;oBACAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAA6B;4BAAA,CACpG;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,UAAA;4BAAA,CAAU;yBAAA;oBAAA,CAClB;iBAAA;YAAA,CACF;SAAA;IAAA;AAIR,GC1KaiM,KAA4D,CAAC,EACxE,iBAAA9K,CAAAA,EACA,aAAA+K,CAAAA,EACA,OAAAhM,CAAAA,EACA,oBAAA6G,CAAAA,EACA,uBAAA3C,CAAAA,EACA,WAAA9E,CAAAA,EACF,KAAM;IACJ,MAAM,CAAC6M,GAAaC,CAAc,CAAA,4MAAIjP,YAAAA,EAAS,CAAA,CAAK,GAC9C,CAACkP,GAAWC,CAAY,CAAA,wNAAInP,EAAmC,KAAK,GAEpEiG,IAAyB2D,IAC3B5F,EAAgB,KAAA,CAAM,GAAG4F,CAAkB,IAC3C5F,GAeEoL,IAAAA,CAbwB,MAAM;QAClC,OAAQF,GAAA;YACN,KAAK;gBACH,OAAOjJ,EACJ,MAAA,CAAO,CAAAL,IAAOA,EAAI,kBAAA,IAAsB,GAAG,EAC3C,KAAA,CAAM,GAAG,CAAC;YACf,KAAK;gBACH,OAAOK,EAAuB,KAAA,CAAM,GAAG,CAAC;YAC1C;gBACE,OAAOA;QAAA;IACX,CAAA,EAGyB,GAErBoJ,IAAiBlQ,EACrB,0BACA,wBACAgD,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA,GAErBuM,IAAwB,MAAM;QAClC,IAAIF,EAAmB,MAAA,KAAW,GAChC,OACEzL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YAAI,WAAU;YACb,UAAA;gBAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBAA2C,MAAK;wBAAO,QAAO;wBAAe,SAAQ;wBAClG,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;wBAAA,CAA8C;oBAAA,CACrH;gBAAA,CACF;gBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oBAAG,WAAU;oBAA4D,UAAA;gBAAA,CAE1E;gBACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,KAAA;oBAAE,WAAU;oBAA2C,UAAA;gBAAA,CAExD;aAAA;QAAA,CACF;QAIJ,OAAQkM,GAAA;YACN,KAAK;gBACH,OACElM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAmB,GAAA,CAAI,CAACjB,GAAgB6C,IACvC5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;4BAEC,gBAAA/E;4BACA,OAAAmB;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAASkE;wBAAA,GALJrF,EAAe,KAAA,IAAS6C;gBAOhC,CACH;YAGJ,KAAK;gBACH,OACE5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAAuM,EAAmB,GAAA,CAAI,CAACxN,GAAgB6C,IACvCd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAwC,WAAU;4BACjD,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;gCAAA,CAAiD;gCAChEc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAA;wCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAAjB,EAAe,KAAA;wCAAA,CAClB;wCACA+B,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4CAAI,WAAU;4CACZ,UAAA;gDAAA,KAAK,KAAA,CAAM/B,EAAe,kBAAA,GAAqB,GAAG;gDAAE;6CAAA;wCAAA,CACvD;qCAAA;gCAAA,CACF;6BAAA;wBAAA,GATQA,EAAe,KAAA,IAAS6C,CAUlC,CACD;gBAAA,CACH;YAGJ,KAAK;gBACH,OAAA,aAAA,GAAA,EAAA,GAAA,CACG,OAAA;oBAAI,WAAU;oBACZ,UAAA2K,EAAmB,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACxN,GAAgB6C,IACnDd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAwC,WAAU;4BACjD,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;oCACC,gBAAA/E;oCACA,OAAAmB;oCACA,SAAS,CAAA;oCACT,YAAY,CAAA;oCACZ,SAASkE;gCAAA;gCAAA,aAAA,GAAA,EAAA,GAAA,CAEV,UAAA;oCAAO,WAAU;oCAChB,UAAApE,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAA0B,MAAK;wCAAe,SAAQ;wCACnE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,GAAE;wCAAA,CAA+F;oCAAA,CACzG;gCAAA,CACF;6BAAA;wBAAA,GAZQjB,EAAe,KAAA,IAAS6C,CAalC,CACD;gBAAA,CACH;YAGJ,KAAK;gBACH,OACEd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBAEZ,UAAA;wBAAAyL,CAAAA,CAAmB,CAAC,CAAA,IACnBzL,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BACC,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCAAoF,UAAA;gCAAA,CAElG;gCACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAACC,IAAA;oCACC,gBAAgBsM,CAAAA,CAAmB,CAAC,CAAA;oCACpC,OAAArM;oCACA,gBAAgB,CAAA;oCAChB,YAAY,CAAA;oCACZ,SAASkE;oCACT,WAAU;gCAAA;6BACZ;wBAAA,CACF;wBAIDmI,EAAmB,KAAA,CAAM,CAAC,EAAE,MAAA,GAAS,KAAA,aAAA,GAAA,EAAA,IAAA,CACnC,OAAA;4BACC,UAAA;gCAAAvM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;oCAAG,WAAU;oCAAoF,UAAA;gCAAA,CAElG;gCACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCACZ,UAAAuM,EAAmB,KAAA,CAAM,GAAG,CAAC,EAAE,GAAA,CAAI,CAACxN,GAAgB6C,IACnD5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;4CAEC,gBAAA/E;4CACA,OAAAmB;4CACA,SAAS,CAAA;4CACT,YAAY,CAAA;4CACZ,SAASkE;wCAAA,GALJrF,EAAe,KAAA,IAAS6C;gCAOhC,CACH;6BAAA;wBAAA,CACF;qBAAA;gBAAA,CAEJ;YAGJ;gBACE,OACE5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oBAAI,WAAU;oBACZ,UAAA,EAAmB,GAAA,CAAI,CAACjB,GAAgB6C,IACvC5B,aAAAA,GAAAA,EAAAA,GAAAA,CAAC8D,GAAA;4BAEC,gBAAA/E;4BACA,OAAAmB;4BACA,SAAS,CAAA;4BACT,YAAY,CAAA;4BACZ,SAASkE;wBAAA,GALJrF,EAAe,KAAA,IAAS6C;gBAOhC,CACH;QAAA;IAEN;IAGF,OACEd,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;QACC,WAAW0L;QACX,OAAO9K;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAG1B,UAAA;YAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBAAI,WAAU;gBACb,UAAA;oBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,UAAA;wBACC,SAAS,IAAMwL,EAAa,KAAK;wBACjC,WAAWhQ,EACT,0DACA+P,MAAc,QACV,qFACA;wBAEP,UAAA;4BAAA;4BACOlL,EAAgB,MAAA;4BAAO;yBAAA;oBAAA;oBAE/BnB,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBACC,SAAS,IAAMsM,EAAa,KAAK;wBACjC,WAAWhQ,EACT,0DACA+P,MAAc,QACV,qFACA;wBAEP,UAAA;oBAAA;oBAGDrM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;wBACC,SAAS,IAAMsM,EAAa,QAAQ;wBACpC,WAAWhQ,EACT,0DACA+P,MAAc,WACV,qFACA;wBAEP,UAAA;oBAAA;iBAED;YAAA,CACF;YAGArM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBAAI,WAAU;gBAAqC,OAAO;oBACzD,yBAAyB;oBAAA,0BAAA;oBACzB,oBAAoB;gBAAA;gBAEnB,UAAA;YAAA,CACH;YAAA,aAAA,GAAA,EAAA,GAAA,CAGC,OAAA;gBAAI,WAAU;gBACb,UAAAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;4BAAK,WAAU;4BACb,UAAA;gCAAAyL,EAAmB,MAAA;gCAAO;gCAAgBA,EAAmB,MAAA,KAAW,IAAI,MAAM;6BAAA;wBAAA,CACrF;wBACAvM,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4BACC,SAAS,IAAMoM,EAAe,CAACD,CAAW;4BAC1C,WAAU;4BACX,UAAA;wBAAA;qBAED;gBAAA,CACF;YAAA,CACF;SAAA;IAAA;AAGN,GCrPaO,KAA8C,CAAC,EAC1D,iBAAAvL,CAAAA,EACA,QAAAjJ,CAAAA,EACA,OAAAgI,CAAAA,EACA,OAAA2F,IAAQ,iBAAA,EACR,QAAAwE,IAAS,CAAA,CAAA,EACT,UAAAZ,CAAAA,EACA,uBAAArF,CAAAA,EACA,UAAAiH,CAAAA,EAAA,YAAA;AAEA,WAAA/L,CAAAA,EACA,eAAAqN,IAAgB,CAAA,CAAA,EAClB,KAAM;IACJ,MAAM,CAACvB,GAAawB,CAAc,CAAA,wNAAIzP,EAASjF,EAAO,gBAAA,IAAoB,CAAA,CAAK,GACzE,CAACqT,GAAaC,CAAc,CAAA,6MAAIrO,WAAAA,EAAS,EAAE,GAC3C,CAAC0P,CAAO,CAAA,wNAAI1P,EAAyB,CAAA,CAAE,GACvC,CAACwO,GAAUC,CAAW,CAAA,wNAAIzO,EAAS,CAAA,CAAK;6MAG9CuC,aAAAA,EAAU,MAAM;QACd,MAAMmM,IAAc,MAAM;YACxBD,EAAY,OAAO,UAAA,GAAa,GAAG;QAAA;QAGrC,OAAAC,EAAA,GACA,OAAO,gBAAA,CAAiB,UAAUA,CAAW,GACtC,IAAM,OAAO,mBAAA,CAAoB,UAAUA,CAAW;IAAA,GAC5D,EAAE,yNAGLnM,EAAU,MAAM;QACd,IAAIiM,KAAYtB,KAAU,CAACe,KAAe,CAACuB,GAAe;YACxD,MAAMG,IAAgB,OAAO,gBAAA,CAAiB,SAAS,IAAI,EAAE,QAAA;YAC7D,OAAA,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,UAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,SAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ,QAErB,MAAM;gBACX,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAWA,GAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,IAC/B,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ;YAAA;QAC9B;IACF,GACC;QAACnB;QAAUtB;QAAQe;QAAauB,CAAa;KAAC,yNAGjDjN,EAAU,MAAM;QACd,IAAIxH,EAAO,WAAA,IAAeA,EAAO,eAAA,EAAiB;YAChD,MAAM6U,IAAW,YAAY,MAAM;gBAEjC,QAAQ,GAAA,CAAI,oCAAoC;YAAA,GAC/C7U,EAAO,eAAe;YAEzB,OAAO,IAAM,cAAc6U,CAAQ;QAAA;IACrC,GACC;QAAC7U,EAAO,WAAA;QAAaA,EAAO,eAAe;KAAC;IAG/C,MAAM8U,IAA0BzP,oNAAAA,EAAQ,MAAM;QAC5C,IAAIgL,IAAW,CAAC;eAAGpH,CAAe;SAAA;QAGlC,IAAIoK,EAAY,IAAA,IAAQ;YACtB,MAAM0B,IAAQ1B,EAAY,WAAA,CAAA;YAC1BhD,IAAWA,EAAS,MAAA,CAAO,CAAAxF,MAAA;;gBACzB,OAAAA,EAAI,KAAA,CAAM,WAAA,CAAA,EAAc,QAAA,CAASkK,CAAK,KACtClK,EAAI,MAAA,CAAO,WAAA,CAAA,EAAc,QAAA,CAASkK,CAAK,KAAA,CAAA,CACvCzM,IAAAuC,EAAI,QAAA,KAAJ,OAAA,KAAA,IAAAvC,EAAc,IAAA,CAAK,CAAAC,IAAWA,EAAQ,WAAA,CAAA,EAAc,QAAA,CAASwM,CAAK,EAAA;YAAA;QACpE;QAIF,OAAIJ,EAAQ,UAAA,IAAcA,EAAQ,UAAA,CAAW,MAAA,GAAS,KAAA,CACpDtE,IAAWA,EAAS,MAAA,CAAO,CAAAxF,MAAA;;YACzB,OAAA,CAAAvC,IAAAuC,EAAI,UAAA,KAAJ,OAAA,KAAA,IAAAvC,EAAgB,IAAA,CAAK,CAAA;;gBAAO,OAAA,CAAAA,IAAAqM,EAAQ,UAAA,KAAR,OAAA,KAAA,IAAArM,EAAoB,QAAA,CAAS0M;YAAA;QAAA,EAAI,GAK7DL,EAAQ,WAAA,IAAA,CACVtE,IAAWA,EAAS,MAAA,CAAO,CAAAxF,IAAOA,EAAI,aAAa,CAAA,GAIjD8J,EAAQ,QAAA,IAAA,CACVtE,IAAWA,EAAS,MAAA,CAAO,CAAAxF,IAAOA,EAAI,UAAA,IAAcA,EAAI,UAAA,GAAa,CAAC,CAAA,GAIpE8J,EAAQ,aAAA,KAAkB,KAAA,KAAA,CAC5BtE,IAAWA,EAAS,MAAA,CAAO,CAAAxF,IAAOA,EAAI,kBAAA,IAAsB8J,EAAQ,aAAc,CAAA,GAIpFtE,EAAS,IAAA,CAAK,CAAChE,GAAGC,IAAMA,EAAE,kBAAA,GAAqBD,EAAE,kBAAkB,GAG/DrM,EAAO,kBAAA,IAAA,CACTqQ,IAAWA,EAAS,KAAA,CAAM,GAAGrQ,EAAO,kBAAkB,CAAA,GAGjDqQ;IAAA,GACN;QAACpH;QAAiBoK;QAAasB;QAAS3U,EAAO,kBAAkB;KAAC,GAE/DwS,IAAe,MAAM;QACrBxS,EAAO,WAAA,IAAA,CACT0U,EAAe,CAACxB,CAAW,GAC3B3B,KAAA,QAAAA,GAAAA;IACF,GAGI0D,IAAe,CAACF,MAAkB;QACtCzB,EAAeyB,CAAK,GACpB5B,KAAA,QAAAA,EAAW4B;IAAK,GAsBZG,IAAiB9Q,EACrB,kBACA,oIAAA,CAfsB,MAAM;QAC5B,IAAI8O,EAAa,CAAA,OAAO;QAGxB,OAAQlT,EAAO,IAAA,EAAA;YACb,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAAA;IAClB,CAAA,EAMA,GACA;QACE,YAAYA,EAAO,QAAA,KAAa;QAChC,YAAYA,EAAO,QAAA,KAAa;QAAA,0EAAA;QAAA,4DAAA;QAGhC,iCAAiC,CAACyU;QAClC,mBAAmBA;QACnB,UAAUzU,EAAO,QAAA,KAAa,UAAU,CAACyU;QACzC,WAAWzU,EAAO,QAAA,KAAa,WAAW,CAACyU;QAAA,mCAAA;QAE3C,+BAA+BzU,EAAO,QAAA,KAAa,UAAU,CAACmS,KAAU,CAACsC;QACzE,8BAA8BzU,EAAO,QAAA,KAAa,WAAW,CAACmS,KAAU,CAACsC;QAAA,+BAAA;QAEzE,WAAW,CAAA;QAAA,kCAAA;QACX,mBAAmB,CAACA;IAAA,GAEtBrN,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OAAI,CAACmK,KAAU,CAACnS,EAAO,WAAA,GACd,OAIP4I,aAAAA,GAAAA,EAAAA,IAAAA,CAAAuI,EAAAA,QAAAA,EAAA;QAEG,UAAA;YAAAgB,KAAU,CAACe,KACVpL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gBACC,WAAW1D,EACT,6EACAqQ,IAAgB,qBAAqB;gBAEvC,SAAS,IAAMlD,KAAA,OAAA,KAAA,IAAAA;gBACf,OAAO;oBAAA,sDAAA;oBAEL,UAAUkD,IAAgB,aAAa;oBACvC,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,aAAa;gBAAA;YACf;YAKJ7L,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;gBACC,WAAWsM;gBACX,OAAO1L;gBACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;gBAC1B,yBAAuBhI,EAAO,QAAA;gBAC9B,qBAAmBA,EAAO,IAAA;gBAC1B,oBAAkByT,KAAYtB,KAAU,CAACe,IAAc,SAAS;gBAChE,uBAAqBuB,IAAgB,SAAS;gBAG7C,UAAA;oBAAAzU,EAAO,UAAA,KAAe,CAAA,KACrB8H,aAAAA,GAAAA,EAAAA,GAAAA,CAACkL,IAAA;wBACC,OAAArF;wBACA,OAAA3F;wBACA,aAAahI,EAAO,WAAA;wBACpB,aAAAkT;wBACA,UAAUV;wBACV,UAAUxS,EAAO,UAAA,GAAaiV,IAAe,KAAA;wBAC7C,YAAYjV,EAAO,UAAA,IAAc,CAACkT;oBAAA;oBAKrC,CAACA,KACApL,aAAAA,GAAAA,EAAAA,GAAAA,CAACiM,IAAA;wBACC,iBAAiBe;wBACjB,aAAa9U,EAAO,WAAA;wBACpB,OAAAgI;wBACA,oBAAoBhI,EAAO,kBAAA;wBAC3B,uBAAAkM;wBACA,WAAU;oBAAA;oBAKbgH,KAAelT,EAAO,WAAA,IACrB4I,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAAS0K;gCACT,WAAU;gCACV,OAAM;gCAEN,UAAA1K,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCAA2C,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAClG,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;oCAAA,CAAe;gCAAA,CACtF;4BAAA;4BAEFA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCACZ,UAAA,EAAwB,MAAA;4BAAA,CAC3B;yBAAA;oBAAA,CACF;oBAID,CAACoL,KACApL,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;4BAAI,WAAU;4BAAuD,UAAA;wBAAA,CAEtE;oBAAA,CACF;iBAAA;YAAA;SAEJ;IAAA,CACF;AAEJ,GC/OaqN,KAAgF,CAAC,EAC5F,iBAAAlM,CAAAA,EACA,SAAAmM,CAAAA,EACA,OAAApN,CAAAA,EACA,OAAA2F,IAAQ,oBAAA,EACR,UAAA0H,IAAW,cAAA,EACX,MAAAtL,IAAO,IAAA,EACP,UAAAuL,IAAW,CAAA,CAAA,EACX,WAAAC,IAAY,GAAA,EACZ,uBAAArJ,CAAAA,EACA,WAAAqC,CAAAA,EACA,WAAAnH,CAAAA,EACF,KAAM;IACJ,MAAM,CAACoH,GAAWC,CAAY,CAAA,wNAAIxJ,EAAS,CAAA,CAAK,GAC1C,CAACyJ,GAAaC,CAAc,CAAA,IAAI1J,oNAAAA,EAAS,CAAA,CAAK;0NAGpDuC,EAAU,MAAM;QACd,IAAI8N,KAAYrM,EAAgB,MAAA,GAAS,GAAG;YAC1C,MAAM2F,IAAQ,WAAW,MAAM;gBAC7BH,EAAa,CAAA,CAAI,GACjBE,EAAe,CAAA,CAAI;YAAA,GAClB4G,CAAS;YAEZ,OAAO,IAAM,aAAa3G,CAAK;QAAA;IACjC,GACC;QAAC0G;QAAUrM,EAAgB,MAAA;QAAQsM,CAAS;KAAC;IAEhD,MAAMxG,IAAgB,MAAM;QAC1BN,EAAa,CAAA,CAAK,GAClBF,KAAA,QAAAA;IAAY,GAIRiH,IAAsB,MAAM;QAChC,OAAQzL,GAAA;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAAA;IAClB,GAII0L,IAAqB,MAAM;QAC/B,OAAQJ,GAAA;YACN,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAAA;IAClB;IAGF,IAAI,CAAC7G,KAAavF,EAAgB,MAAA,KAAW,GAC3C,OAAO;IAGT,MAAMM,IAAmBnF,EACvB,qCACA,mDACAqR,EAAA,GACAD,EAAA,GACA;QACE,oCAAoC,CAAC9G;QACrC,uCAAuCA;IAAA,GAEzCtH,IAGIoC,IAAiBxB,KAAA,QAAAA,EAAO,WAAA,GAAc;QAC1C,oBAAoBA,EAAM,WAAA;IAAA,IACD,KAAA;IAE3B,OACEF,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;QACC,WAAWyB;QACX,OAAOC;QACP,qBAAmBxB,KAAA,OAAA,KAAA,IAAAA,EAAO,IAAA;QAE1B,UAAAY,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;YAAI,WAAU;YAEb,UAAA;gBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBACb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;oCACb,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCACjE,UAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;wCAAA,CAA6B;oCAAA,CACpG;gCAAA,CACF;gCAAA,aAAA,GAAA,EAAA,IAAA,CACC,OAAA;oCACC,UAAA;wCAAAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,MAAA;4CAAG,WAAU;4CAAyB,UAAA6F;wCAAAA,CAAM;wCAC5CyH,KACCxM,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,KAAA;4CAAE,WAAU;4CAA0C,UAAA;gDAAA;gDACzCwM;gDAAQ;6CAAA;wCAAA,CACtB;qCAAA;gCAAA,CAEJ;6BAAA;wBAAA,CACF;wBACAtN,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;4BACC,SAASiH;4BACT,WAAU;4BACV,cAAW;4BAEX,UAAAjH,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;gCACjE,UAAA,aAAA,GAAA,EAAA,GAAA,CAAC,QAAA;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;gCAAA,CAAuB;4BAAA,CAC9F;wBAAA;qBACF;gBAAA,CACF;gBAGAc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;oBAAI,WAAU;oBAEb,UAAA;wBAAAA,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;4BAAI,WAAU;4BACb,UAAA;gCAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,OAAA;oCAAI,WAAU;gCAAA,CAAkD;gCACjEc,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,QAAA;oCAAK,WAAU;oCACb,UAAA;wCAAAK,EAAgB,MAAA;wCAAO;wCAAmBA,EAAgB,MAAA,GAAS,IAAI,OAAO;wCAAG;qCAAA;gCAAA,CACpF;6BAAA;wBAAA,CACF;wBAGAnB,aAAAA,GAAAA,EAAAA,GAAAA,CAACuG,IAAA;4BACC,iBAAApF;4BACA,QAAQ;gCACN,aAAa;gCACb,SAAS;gCACT,oBAAoB;gCACpB,eAAe,CAAA;gCACf,UAAU,CAAA;gCACV,SAAS;4BAAA;4BAEX,OAAAjB;4BACA,uBAAAkE;wBAAA;qBACF;gBAAA,CACF;gBAAA,aAAA,GAAA,EAAA,GAAA,CAGC,OAAA;oBAAI,WAAU;oBACb,UAAAtD,aAAAA,GAAAA,EAAAA,IAAAA,CAAC,OAAA;wBAAI,WAAU;wBACb,UAAA;4BAAAd,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,QAAA;gCAAK,WAAU;gCAA2C,UAAA;4BAAA,CAE3D;4BACAA,aAAAA,GAAAA,EAAAA,GAAAA,CAAC,UAAA;gCACC,SAASiH;gCACT,WAAU;gCACX,UAAA;4BAAA;yBAED;oBAAA,CACF;gBAAA,CACF;aAAA;QAAA,CACF;IAAA;AAGN,GC3Fa2G,KAAU,SAGVC,KAAiB;IAC5B,iBAAiB,CAAA;IACjB,OAAO,CAAA;IACP,OAAO;QACL,MAAM;QACN,aAAa;IAAA;AAEjB", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "debugId": null}}, {"offset": {"line": 4240, "column": 0}, "map": {"version": 3, "file": "zap.js", "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/node_modules/lucide-react/src/icons/zap.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z',\n      key: '1xq2db',\n    },\n  ],\n];\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxNGExIDEgMCAwIDEtLjc4LTEuNjNsOS45LTEwLjJhLjUuNSAwIDAgMSAuODYuNDZsLTEuOTIgNi4wMkExIDEgMCAwIDAgMTMgMTBoN2ExIDEgMCAwIDEgLjc4IDEuNjNsLTkuOSAxMC4yYS41LjUgMCAwIDEtLjg2LS40NmwxLjkyLTYuMDJBMSAxIDAgMCAwIDExIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('zap', __iconNode);\n\nexport default Zap;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}