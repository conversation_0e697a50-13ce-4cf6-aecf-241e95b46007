{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/src/components/CitationRenderer.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ExternalLink } from 'lucide-react';\n\ninterface Source {\n  id: number;\n  title: string;\n  url: string;\n  snippet: string;\n  favicon?: string;\n}\n\ninterface CitationRendererProps {\n  content: string;\n  sources: Source[];\n}\n\nexport default function CitationRenderer({ content, sources }: CitationRendererProps) {\n  const [hoveredCitation, setHoveredCitation] = useState<number | null>(null);\n\n  // Function to render content with interactive citations\n  const renderContentWithCitations = (text: string) => {\n    const citationPattern = /\\[(\\d+)\\]/g;\n    const parts = [];\n    let lastIndex = 0;\n    let match;\n\n    while ((match = citationPattern.exec(text)) !== null) {\n      // Add text before citation\n      if (match.index > lastIndex) {\n        parts.push(text.slice(lastIndex, match.index));\n      }\n\n      const citationNumber = parseInt(match[1]);\n      const source = sources.find(s => s.id === citationNumber);\n\n      if (source) {\n        parts.push(\n          <span\n            key={`citation-${match.index}`}\n            className=\"relative inline-block\"\n            onMouseEnter={() => setHoveredCitation(citationNumber)}\n            onMouseLeave={() => setHoveredCitation(null)}\n          >\n            <a\n              href={source.url}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors cursor-pointer\"\n            >\n              [{citationNumber}]\n            </a>\n            \n            {/* Citation tooltip */}\n            {hoveredCitation === citationNumber && (\n              <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-80 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg p-4 z-10\">\n                <div className=\"flex items-start space-x-3\">\n                  {source.favicon && (\n                    <img \n                      src={source.favicon} \n                      alt=\"\" \n                      className=\"w-4 h-4 mt-1 flex-shrink-0\"\n                      onError={(e) => {\n                        e.currentTarget.style.display = 'none';\n                      }}\n                    />\n                  )}\n                  <div className=\"flex-1 min-w-0\">\n                    <h4 className=\"font-medium text-gray-900 dark:text-white text-sm line-clamp-2 mb-1\">\n                      {source.title}\n                    </h4>\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 line-clamp-3 mb-2\">\n                      {source.snippet}\n                    </p>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-xs text-gray-500 dark:text-gray-500 truncate\">\n                        {new URL(source.url).hostname}\n                      </span>\n                      <ExternalLink className=\"w-3 h-3 text-gray-400\" />\n                    </div>\n                  </div>\n                </div>\n                {/* Tooltip arrow */}\n                <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200 dark:border-t-slate-600\"></div>\n              </div>\n            )}\n          </span>\n        );\n      } else {\n        parts.push(match[0]); // Keep original citation if source not found\n      }\n\n      lastIndex = match.index + match[0].length;\n    }\n\n    // Add remaining text\n    if (lastIndex < text.length) {\n      parts.push(text.slice(lastIndex));\n    }\n\n    return parts;\n  };\n\n  // Split content into paragraphs and render each with citations\n  const paragraphs = content.split('\\n\\n').filter(p => p.trim());\n\n  return (\n    <div className=\"space-y-4\">\n      {paragraphs.map((paragraph, index) => (\n        <p key={index} className=\"text-gray-700 dark:text-gray-300 leading-relaxed\">\n          {renderContentWithCitations(paragraph)}\n        </p>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAkBe,SAAS,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAyB;IAClF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,wDAAwD;IACxD,MAAM,6BAA6B,CAAC;QAClC,MAAM,kBAAkB;QACxB,MAAM,QAAQ,EAAE;QAChB,IAAI,YAAY;QAChB,IAAI;QAEJ,MAAO,CAAC,QAAQ,gBAAgB,IAAI,CAAC,KAAK,MAAM,KAAM;YACpD,2BAA2B;YAC3B,IAAI,MAAM,KAAK,GAAG,WAAW;gBAC3B,MAAM,IAAI,CAAC,KAAK,KAAK,CAAC,WAAW,MAAM,KAAK;YAC9C;YAEA,MAAM,iBAAiB,SAAS,KAAK,CAAC,EAAE;YACxC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAE1C,IAAI,QAAQ;gBACV,MAAM,IAAI,eACR,8OAAC;oBAEC,WAAU;oBACV,cAAc,IAAM,mBAAmB;oBACvC,cAAc,IAAM,mBAAmB;;sCAEvC,8OAAC;4BACC,MAAM,OAAO,GAAG;4BAChB,QAAO;4BACP,KAAI;4BACJ,WAAU;;gCACX;gCACG;gCAAe;;;;;;;wBAIlB,oBAAoB,gCACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,OAAO,OAAO,kBACb,8OAAC;4CACC,KAAK,OAAO,OAAO;4CACnB,KAAI;4CACJ,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;4CAClC;;;;;;sDAGJ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAE,WAAU;8DACV,OAAO,OAAO;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,IAAI,IAAI,OAAO,GAAG,EAAE,QAAQ;;;;;;sEAE/B,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAK9B,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;mBA5Cd,CAAC,SAAS,EAAE,MAAM,KAAK,EAAE;;;;;YAiDpC,OAAO;gBACL,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,6CAA6C;YACrE;YAEA,YAAY,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;QAC3C;QAEA,qBAAqB;QACrB,IAAI,YAAY,KAAK,MAAM,EAAE;YAC3B,MAAM,IAAI,CAAC,KAAK,KAAK,CAAC;QACxB;QAEA,OAAO;IACT;IAEA,+DAA+D;IAC/D,MAAM,aAAa,QAAQ,KAAK,CAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;IAE3D,qBACE,8OAAC;QAAI,WAAU;kBACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;gBAAc,WAAU;0BACtB,2BAA2B;eADtB;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/src/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Moon, Sun } from 'lucide-react';\n\nexport default function ThemeToggle() {\n  const [isDark, setIsDark] = useState(false);\n\n  useEffect(() => {\n    // Check for saved theme preference or default to system preference\n    const savedTheme = localStorage.getItem('theme');\n    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {\n      setIsDark(true);\n      document.documentElement.classList.add('dark');\n    } else {\n      setIsDark(false);\n      document.documentElement.classList.remove('dark');\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDark;\n    setIsDark(newTheme);\n    \n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n      localStorage.setItem('theme', 'dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      localStorage.setItem('theme', 'light');\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className=\"p-2 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors\"\n      aria-label=\"Toggle theme\"\n    >\n      {isDark ? (\n        <Sun className=\"w-5 h-5 text-yellow-500\" />\n      ) : (\n        <Moon className=\"w-5 h-5 text-gray-600\" />\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mEAAmE;QACnE,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;QAEnF,IAAI,eAAe,UAAW,CAAC,cAAc,mBAAoB;YAC/D,UAAU;YACV,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,UAAU;YACV,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,CAAC;QAClB,UAAU;QAEV,IAAI,UAAU;YACZ,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACvC,aAAa,OAAO,CAAC,SAAS;QAChC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC1C,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAW;kBAEV,uBACC,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;iCAEf,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/src/components/SearchHistory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Clock, X } from 'lucide-react';\n\ninterface SearchHistoryItem {\n  id: string;\n  query: string;\n  timestamp: number;\n}\n\ninterface SearchHistoryProps {\n  onSelectQuery: (query: string) => void;\n  currentQuery: string;\n}\n\nexport default function SearchHistory({ onSelectQuery, currentQuery }: SearchHistoryProps) {\n  const [history, setHistory] = useState<SearchHistoryItem[]>([]);\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Load search history from localStorage\n    const savedHistory = localStorage.getItem('searchHistory');\n    if (savedHistory) {\n      try {\n        setHistory(JSON.parse(savedHistory));\n      } catch (error) {\n        console.error('Error loading search history:', error);\n      }\n    }\n  }, []);\n\n  useEffect(() => {\n    // Save current query to history when it changes (and is not empty)\n    if (currentQuery.trim() && currentQuery.length > 2) {\n      const newItem: SearchHistoryItem = {\n        id: Date.now().toString(),\n        query: currentQuery.trim(),\n        timestamp: Date.now()\n      };\n\n      setHistory(prev => {\n        // Remove duplicate if exists\n        const filtered = prev.filter(item => item.query !== newItem.query);\n        // Add new item at the beginning and limit to 10 items\n        const updated = [newItem, ...filtered].slice(0, 10);\n        \n        // Save to localStorage\n        localStorage.setItem('searchHistory', JSON.stringify(updated));\n        \n        return updated;\n      });\n    }\n  }, [currentQuery]);\n\n  const clearHistory = () => {\n    setHistory([]);\n    localStorage.removeItem('searchHistory');\n  };\n\n  const removeItem = (id: string) => {\n    setHistory(prev => {\n      const updated = prev.filter(item => item.id !== id);\n      localStorage.setItem('searchHistory', JSON.stringify(updated));\n      return updated;\n    });\n  };\n\n  const formatTimestamp = (timestamp: number) => {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(diff / 3600000);\n    const days = Math.floor(diff / 86400000);\n\n    if (minutes < 1) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    return `${days}d ago`;\n  };\n\n  if (history.length === 0) return null;\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsVisible(!isVisible)}\n        className=\"flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n      >\n        <Clock className=\"w-4 h-4\" />\n        <span>Recent searches</span>\n      </button>\n\n      {isVisible && (\n        <div className=\"absolute top-full left-0 right-0 mt-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg shadow-lg z-10 max-h-80 overflow-y-auto\">\n          <div className=\"p-3 border-b border-gray-200 dark:border-slate-600 flex items-center justify-between\">\n            <h3 className=\"font-medium text-gray-900 dark:text-white\">Recent Searches</h3>\n            <button\n              onClick={clearHistory}\n              className=\"text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors\"\n            >\n              Clear all\n            </button>\n          </div>\n          \n          <div className=\"py-2\">\n            {history.map((item) => (\n              <div\n                key={item.id}\n                className=\"flex items-center justify-between px-3 py-2 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors group\"\n              >\n                <button\n                  onClick={() => {\n                    onSelectQuery(item.query);\n                    setIsVisible(false);\n                  }}\n                  className=\"flex-1 text-left\"\n                >\n                  <div className=\"text-sm text-gray-900 dark:text-white truncate\">\n                    {item.query}\n                  </div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {formatTimestamp(item.timestamp)}\n                  </div>\n                </button>\n                \n                <button\n                  onClick={() => removeItem(item.id)}\n                  className=\"opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-all\"\n                >\n                  <X className=\"w-3 h-3\" />\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAgBe,SAAS,cAAc,EAAE,aAAa,EAAE,YAAY,EAAsB;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,cAAc;YAChB,IAAI;gBACF,WAAW,KAAK,KAAK,CAAC;YACxB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mEAAmE;QACnE,IAAI,aAAa,IAAI,MAAM,aAAa,MAAM,GAAG,GAAG;YAClD,MAAM,UAA6B;gBACjC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,OAAO,aAAa,IAAI;gBACxB,WAAW,KAAK,GAAG;YACrB;YAEA,WAAW,CAAA;gBACT,6BAA6B;gBAC7B,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK;gBACjE,sDAAsD;gBACtD,MAAM,UAAU;oBAAC;uBAAY;iBAAS,CAAC,KAAK,CAAC,GAAG;gBAEhD,uBAAuB;gBACvB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBAErD,OAAO;YACT;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,eAAe;QACnB,WAAW,EAAE;QACb,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,WAAW,CAAA;YACT,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YACrD,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAO,MAAM;QACnB,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO;QAChC,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO;QAE/B,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,IAAI,OAAO,GAAG,QAAQ,KAAK,CAAC;QAC1C,IAAI,QAAQ,IAAI,OAAO,GAAG,MAAM,KAAK,CAAC;QACtC,OAAO,GAAG,KAAK,KAAK,CAAC;IACvB;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;;kCAEV,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;kCAAK;;;;;;;;;;;;YAGP,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,qBACZ,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCACC,SAAS;4CACP,cAAc,KAAK,KAAK;4CACxB,aAAa;wCACf;wCACA,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,KAAK,SAAS;;;;;;;;;;;;kDAInC,8OAAC;wCACC,SAAS,IAAM,WAAW,KAAK,EAAE;wCACjC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAtBV,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AA+B5B", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/src/components/AdmeshFloatingWidget.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  AdMeshAutoRecommendationWidget,\n  AdMeshFloatingChat,\n  type AdMeshRecommendation,\n  type AdMeshTheme \n} from 'admesh-ui-sdk';\nimport { X, MessageCircle, Sparkles } from 'lucide-react';\n\ninterface AdmeshResponse {\n  recommendations: AdmeshRecommendation[];\n  query: string;\n  success: boolean;\n  error?: string;\n}\n\ninterface AdmeshFloatingWidgetProps {\n  admeshResponse: AdmeshResponse | null;\n  theme?: AdMeshTheme;\n  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';\n  autoShow?: boolean;\n  showDelay?: number;\n  onRecommendationClick?: (adId: string, admeshLink: string) => void;\n  onDismiss?: () => void;\n}\n\n// Transform our recommendation format to AdMesh format\nconst transformRecommendations = (recommendations: any[]): AdMeshRecommendation[] => {\n  return recommendations.map((rec, index) => ({\n    title: rec.title,\n    reason: rec.description,\n    intent_match_score: rec.relevanceScore || 0.8,\n    admesh_link: rec.url || `https://useadmesh.com/track?ad_id=${rec.title.toLowerCase().replace(/\\s+/g, '-')}-${index}`,\n    ad_id: `${rec.title.toLowerCase().replace(/\\s+/g, '-')}-${index}`,\n    product_id: rec.title.toLowerCase().replace(/\\s+/g, '-'),\n    features: rec.category ? [rec.category] : [],\n    has_free_tier: true,\n    trial_days: 14,\n    keywords: [rec.category || 'General']\n  }));\n};\n\nexport default function AdmeshFloatingWidget({\n  admeshResponse,\n  theme = { mode: 'light' },\n  position = 'bottom-right',\n  autoShow = true,\n  showDelay = 1000,\n  onRecommendationClick,\n  onDismiss\n}: AdmeshFloatingWidgetProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  useEffect(() => {\n    if (admeshResponse && admeshResponse.recommendations.length > 0 && autoShow) {\n      const timer = setTimeout(() => {\n        setIsVisible(true);\n      }, showDelay);\n\n      return () => clearTimeout(timer);\n    }\n  }, [admeshResponse, autoShow, showDelay]);\n\n  if (!admeshResponse || !admeshResponse.recommendations.length || !isVisible) {\n    return null;\n  }\n\n  const transformedRecommendations = transformRecommendations(admeshResponse.recommendations);\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    if (onRecommendationClick) {\n      onRecommendationClick(adId, admeshLink);\n    } else {\n      window.open(admeshLink, '_blank');\n    }\n  };\n\n  const handleDismiss = () => {\n    setIsVisible(false);\n    if (onDismiss) {\n      onDismiss();\n    }\n  };\n\n  const positionClasses = {\n    'bottom-right': 'bottom-4 right-4',\n    'bottom-left': 'bottom-4 left-4',\n    'top-right': 'top-4 right-4',\n    'top-left': 'top-4 left-4'\n  };\n\n  if (isExpanded) {\n    return (\n      <div className={`fixed ${positionClasses[position]} z-50 max-w-sm w-full`}>\n        <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-slate-600 overflow-hidden\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-slate-600\">\n            <div className=\"flex items-center\">\n              <Sparkles className=\"w-5 h-5 text-blue-500 mr-2\" />\n              <h3 className=\"font-semibold text-gray-800 dark:text-white\">\n                Smart Recommendations\n              </h3>\n            </div>\n            <button\n              onClick={() => setIsExpanded(false)}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n            >\n              <X className=\"w-4 h-4\" />\n            </button>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-4 max-h-96 overflow-y-auto\">\n            {admeshResponse.error && (\n              <div className=\"mb-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-xs text-yellow-700 dark:text-yellow-300\">\n                {admeshResponse.error}\n              </div>\n            )}\n            \n            <AdMeshAutoRecommendationWidget\n              recommendations={transformedRecommendations}\n              trigger={admeshResponse.query}\n              autoShow={true}\n              position=\"inline\"\n              size=\"sm\"\n              title=\"\"\n              theme={theme}\n              onRecommendationClick={handleRecommendationClick}\n              onDismiss={handleDismiss}\n            />\n          </div>\n\n          {/* Footer */}\n          <div className=\"px-4 py-2 bg-gray-50 dark:bg-slate-700 text-xs text-gray-500 dark:text-gray-400 text-center\">\n            Based on: \"{admeshResponse.query}\"\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Collapsed state - floating button\n  return (\n    <div className={`fixed ${positionClasses[position]} z-50`}>\n      <div className=\"relative\">\n        {/* Notification badge */}\n        <div className=\"absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-semibold\">\n          {transformedRecommendations.length}\n        </div>\n        \n        {/* Main button */}\n        <button\n          onClick={() => setIsExpanded(true)}\n          className=\"bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg transition-all duration-200 hover:scale-105\"\n        >\n          <Sparkles className=\"w-6 h-6\" />\n        </button>\n\n        {/* Tooltip */}\n        <div className=\"absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity pointer-events-none\">\n          {transformedRecommendations.length} smart recommendations\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AAAA;AATA;;;;;AA4BA,uDAAuD;AACvD,MAAM,2BAA2B,CAAC;IAChC,OAAO,gBAAgB,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;YAC1C,OAAO,IAAI,KAAK;YAChB,QAAQ,IAAI,WAAW;YACvB,oBAAoB,IAAI,cAAc,IAAI;YAC1C,aAAa,IAAI,GAAG,IAAI,CAAC,kCAAkC,EAAE,IAAI,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,OAAO;YACpH,OAAO,GAAG,IAAI,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,OAAO;YACjE,YAAY,IAAI,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;YACpD,UAAU,IAAI,QAAQ,GAAG;gBAAC,IAAI,QAAQ;aAAC,GAAG,EAAE;YAC5C,eAAe;YACf,YAAY;YACZ,UAAU;gBAAC,IAAI,QAAQ,IAAI;aAAU;QACvC,CAAC;AACH;AAEe,SAAS,qBAAqB,EAC3C,cAAc,EACd,QAAQ;IAAE,MAAM;AAAQ,CAAC,EACzB,WAAW,cAAc,EACzB,WAAW,IAAI,EACf,YAAY,IAAI,EAChB,qBAAqB,EACrB,SAAS,EACiB;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,eAAe,eAAe,CAAC,MAAM,GAAG,KAAK,UAAU;YAC3E,MAAM,QAAQ,WAAW;gBACvB,aAAa;YACf,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAgB;QAAU;KAAU;IAExC,IAAI,CAAC,kBAAkB,CAAC,eAAe,eAAe,CAAC,MAAM,IAAI,CAAC,WAAW;QAC3E,OAAO;IACT;IAEA,MAAM,6BAA6B,yBAAyB,eAAe,eAAe;IAE1F,MAAM,4BAA4B,CAAC,MAAc;QAC/C,IAAI,uBAAuB;YACzB,sBAAsB,MAAM;QAC9B,OAAO;YACL,OAAO,IAAI,CAAC,YAAY;QAC1B;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI,WAAW;YACb;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,YAAY;IACd;IAEA,IAAI,YAAY;QACd,qBACE,8OAAC;YAAI,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,SAAS,CAAC,qBAAqB,CAAC;sBACvE,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA8C;;;;;;;;;;;;0CAI9D,8OAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;4BACZ,eAAe,KAAK,kBACnB,8OAAC;gCAAI,WAAU;0CACZ,eAAe,KAAK;;;;;;0CAIzB,8OAAC,qJAAA,CAAA,iCAA8B;gCAC7B,iBAAiB;gCACjB,SAAS,eAAe,KAAK;gCAC7B,UAAU;gCACV,UAAS;gCACT,MAAK;gCACL,OAAM;gCACN,OAAO;gCACP,uBAAuB;gCACvB,WAAW;;;;;;;;;;;;kCAKf,8OAAC;wBAAI,WAAU;;4BAA8F;4BAC/F,eAAe,KAAK;4BAAC;;;;;;;;;;;;;;;;;;IAK3C;IAEA,oCAAoC;IACpC,qBACE,8OAAC;QAAI,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC;kBACvD,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACZ,2BAA2B,MAAM;;;;;;8BAIpC,8OAAC;oBACC,SAAS,IAAM,cAAc;oBAC7B,WAAU;8BAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAItB,8OAAC;oBAAI,WAAU;;wBACZ,2BAA2B,MAAM;wBAAC;;;;;;;;;;;;;;;;;;AAK7C", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/src/components/AdmeshInlineAd.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { \n  AdMeshSimpleAd,\n  AdMeshProductCard,\n  AdMeshBadge,\n  type AdMeshRecommendation,\n  type AdMeshTheme \n} from 'admesh-ui-sdk';\nimport { Star, ExternalLink, Zap } from 'lucide-react';\n\ninterface AdmeshResponse {\n  recommendations: AdmeshRecommendation[];\n  query: string;\n  success: boolean;\n  error?: string;\n}\n\ninterface AdmeshInlineAdProps {\n  admeshResponse: AdmeshResponse;\n  adType?: 'simple' | 'product-card' | 'banner' | 'compact';\n  theme?: AdMeshTheme;\n  showBadge?: boolean;\n  maxRecommendations?: number;\n  onRecommendationClick?: (adId: string, admeshLink: string) => void;\n}\n\n// Transform our recommendation format to AdMesh format\nconst transformRecommendations = (recommendations: any[]): AdMeshRecommendation[] => {\n  return recommendations.map((rec, index) => ({\n    title: rec.title,\n    reason: rec.description,\n    intent_match_score: rec.relevanceScore || 0.8,\n    admesh_link: rec.url || `https://useadmesh.com/track?ad_id=${rec.title.toLowerCase().replace(/\\s+/g, '-')}-${index}`,\n    ad_id: `${rec.title.toLowerCase().replace(/\\s+/g, '-')}-${index}`,\n    product_id: rec.title.toLowerCase().replace(/\\s+/g, '-'),\n    features: rec.category ? [rec.category] : [],\n    has_free_tier: true,\n    trial_days: 14,\n    keywords: [rec.category || 'General']\n  }));\n};\n\nexport default function AdmeshInlineAd({\n  admeshResponse,\n  adType = 'simple',\n  theme = { mode: 'light' },\n  showBadge = true,\n  maxRecommendations = 1,\n  onRecommendationClick\n}: AdmeshInlineAdProps) {\n  if (!admeshResponse.success || !admeshResponse.recommendations.length) {\n    return null;\n  }\n\n  const transformedRecommendations = transformRecommendations(\n    admeshResponse.recommendations.slice(0, maxRecommendations)\n  );\n\n  const handleRecommendationClick = (adId: string, admeshLink: string) => {\n    if (onRecommendationClick) {\n      onRecommendationClick(adId, admeshLink);\n    } else {\n      window.open(admeshLink, '_blank');\n    }\n  };\n\n  const renderBadge = () => {\n    if (!showBadge) return null;\n    \n    return (\n      <div className=\"flex justify-center mb-3\">\n        <AdMeshBadge\n          type=\"sponsored\"\n          variant=\"outline\"\n          size=\"sm\"\n          theme={theme}\n        />\n      </div>\n    );\n  };\n\n  switch (adType) {\n    case 'simple':\n      return (\n        <div className=\"my-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n          {renderBadge()}\n          {transformedRecommendations.map((rec, index) => (\n            <AdMeshSimpleAd\n              key={index}\n              recommendation={rec}\n              theme={theme}\n              compact={true}\n              onClick={handleRecommendationClick}\n            />\n          ))}\n        </div>\n      );\n\n    case 'product-card':\n      return (\n        <div className=\"my-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n          {renderBadge()}\n          {transformedRecommendations.map((rec, index) => (\n            <AdMeshProductCard\n              key={index}\n              recommendation={rec}\n              theme={theme}\n              showMatchScore={true}\n              onClick={handleRecommendationClick}\n            />\n          ))}\n        </div>\n      );\n\n    case 'banner':\n      const topRec = transformedRecommendations[0];\n      return (\n        <div className=\"my-6 p-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg shadow-lg\">\n          {showBadge && (\n            <div className=\"flex items-center justify-between mb-3\">\n              <span className=\"text-xs font-medium bg-white/20 px-2 py-1 rounded\">\n                Sponsored\n              </span>\n              <Zap className=\"w-4 h-4\" />\n            </div>\n          )}\n          \n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex-1\">\n              <h3 className=\"font-semibold text-lg mb-1\">{topRec.title}</h3>\n              <p className=\"text-sm opacity-90 mb-2\">{topRec.reason}</p>\n              <div className=\"flex items-center text-xs\">\n                <Star className=\"w-3 h-3 mr-1\" />\n                <span>{Math.round(topRec.intent_match_score * 100)}% match</span>\n              </div>\n            </div>\n            \n            <button\n              onClick={() => handleRecommendationClick(topRec.ad_id, topRec.admesh_link)}\n              className=\"ml-4 bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center\"\n            >\n              Learn More\n              <ExternalLink className=\"w-4 h-4 ml-1\" />\n            </button>\n          </div>\n        </div>\n      );\n\n    case 'compact':\n      const compactRec = transformedRecommendations[0];\n      return (\n        <div className=\"my-4 p-3 bg-gray-50 dark:bg-slate-700 border-l-4 border-blue-500 rounded-r-lg\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex-1\">\n              {showBadge && (\n                <span className=\"text-xs text-gray-500 dark:text-gray-400 font-medium\">\n                  Sponsored\n                </span>\n              )}\n              <h4 className=\"font-medium text-gray-800 dark:text-white text-sm\">\n                {compactRec.title}\n              </h4>\n              <p className=\"text-xs text-gray-600 dark:text-gray-300 mt-1\">\n                {compactRec.reason}\n              </p>\n            </div>\n            \n            <button\n              onClick={() => handleRecommendationClick(compactRec.ad_id, compactRec.admesh_link)}\n              className=\"ml-3 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors\"\n            >\n              <ExternalLink className=\"w-4 h-4\" />\n            </button>\n          </div>\n        </div>\n      );\n\n    default:\n      return null;\n  }\n}\n"], "names": [], "mappings": ";;;;AAGA;AAOA;AAAA;AAAA;AAVA;;;;AA4BA,uDAAuD;AACvD,MAAM,2BAA2B,CAAC;IAChC,OAAO,gBAAgB,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;YAC1C,OAAO,IAAI,KAAK;YAChB,QAAQ,IAAI,WAAW;YACvB,oBAAoB,IAAI,cAAc,IAAI;YAC1C,aAAa,IAAI,GAAG,IAAI,CAAC,kCAAkC,EAAE,IAAI,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,OAAO;YACpH,OAAO,GAAG,IAAI,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,OAAO;YACjE,YAAY,IAAI,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ;YACpD,UAAU,IAAI,QAAQ,GAAG;gBAAC,IAAI,QAAQ;aAAC,GAAG,EAAE;YAC5C,eAAe;YACf,YAAY;YACZ,UAAU;gBAAC,IAAI,QAAQ,IAAI;aAAU;QACvC,CAAC;AACH;AAEe,SAAS,eAAe,EACrC,cAAc,EACd,SAAS,QAAQ,EACjB,QAAQ;IAAE,MAAM;AAAQ,CAAC,EACzB,YAAY,IAAI,EAChB,qBAAqB,CAAC,EACtB,qBAAqB,EACD;IACpB,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,eAAe,eAAe,CAAC,MAAM,EAAE;QACrE,OAAO;IACT;IAEA,MAAM,6BAA6B,yBACjC,eAAe,eAAe,CAAC,KAAK,CAAC,GAAG;IAG1C,MAAM,4BAA4B,CAAC,MAAc;QAC/C,IAAI,uBAAuB;YACzB,sBAAsB,MAAM;QAC9B,OAAO;YACL,OAAO,IAAI,CAAC,YAAY;QAC1B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,OAAO;QAEvB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,qJAAA,CAAA,cAAW;gBACV,MAAK;gBACL,SAAQ;gBACR,MAAK;gBACL,OAAO;;;;;;;;;;;IAIf;IAEA,OAAQ;QACN,KAAK;YACH,qBACE,8OAAC;gBAAI,WAAU;;oBACZ;oBACA,2BAA2B,GAAG,CAAC,CAAC,KAAK,sBACpC,8OAAC,qJAAA,CAAA,iBAAc;4BAEb,gBAAgB;4BAChB,OAAO;4BACP,SAAS;4BACT,SAAS;2BAJJ;;;;;;;;;;;QAUf,KAAK;YACH,qBACE,8OAAC;gBAAI,WAAU;;oBACZ;oBACA,2BAA2B,GAAG,CAAC,CAAC,KAAK,sBACpC,8OAAC,qJAAA,CAAA,oBAAiB;4BAEhB,gBAAgB;4BAChB,OAAO;4BACP,gBAAgB;4BAChB,SAAS;2BAJJ;;;;;;;;;;;QAUf,KAAK;YACH,MAAM,SAAS,0BAA0B,CAAC,EAAE;YAC5C,qBACE,8OAAC;gBAAI,WAAU;;oBACZ,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAoD;;;;;;0CAGpE,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;kCAInB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8B,OAAO,KAAK;;;;;;kDACxD,8OAAC;wCAAE,WAAU;kDAA2B,OAAO,MAAM;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;;oDAAM,KAAK,KAAK,CAAC,OAAO,kBAAkB,GAAG;oDAAK;;;;;;;;;;;;;;;;;;;0CAIvD,8OAAC;gCACC,SAAS,IAAM,0BAA0B,OAAO,KAAK,EAAE,OAAO,WAAW;gCACzE,WAAU;;oCACX;kDAEC,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;QAMlC,KAAK;YACH,MAAM,aAAa,0BAA0B,CAAC,EAAE;YAChD,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,2BACC,8OAAC;oCAAK,WAAU;8CAAuD;;;;;;8CAIzE,8OAAC;oCAAG,WAAU;8CACX,WAAW,KAAK;;;;;;8CAEnB,8OAAC;oCAAE,WAAU;8CACV,WAAW,MAAM;;;;;;;;;;;;sCAItB,8OAAC;4BACC,SAAS,IAAM,0BAA0B,WAAW,KAAK,EAAE,WAAW,WAAW;4BACjF,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;QAMlC;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/AISearch/ai-search-engine/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Search, Loader2, ExternalLink, Lightbulb, Star } from 'lucide-react';\nimport CitationRenderer from '@/components/CitationRenderer';\nimport ThemeToggle from '@/components/ThemeToggle';\nimport SearchHistory from '@/components/SearchHistory';\nimport AdmeshRecommendations from '@/components/AdmeshRecommendations';\nimport AdmeshFloatingWidget from '@/components/AdmeshFloatingWidget';\nimport AdmeshInlineAd from '@/components/AdmeshInlineAd';\n\ninterface Source {\n  id: number;\n  title: string;\n  url: string;\n  snippet: string;\n  favicon?: string;\n}\n\ninterface AdmeshRecommendation {\n  title: string;\n  description: string;\n  url?: string;\n  category?: string;\n  relevanceScore?: number;\n}\n\ninterface AdmeshResponse {\n  recommendations: AdmeshRecommendation[];\n  query: string;\n  success: boolean;\n  error?: string;\n}\n\ninterface SearchResponse {\n  query: string;\n  aiResponse: string;\n  sources: Source[];\n}\n\nexport default function Home() {\n  const [query, setQuery] = useState('');\n  const [isSearching, setIsSearching] = useState(false);\n  const [response, setResponse] = useState<SearchResponse | null>(null);\n  const [streamedContent, setStreamedContent] = useState('');\n  const [sources, setSources] = useState<Source[]>([]);\n  const [recommendations, setRecommendations] = useState<AdmeshResponse | null>(null);\n  const [status, setStatus] = useState('');\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    inputRef.current?.focus();\n  }, []);\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!query.trim() || isSearching) return;\n\n    setIsSearching(true);\n    setResponse(null);\n    setStreamedContent('');\n    setSources([]);\n    setRecommendations(null);\n    setStatus('');\n\n    try {\n      const response = await fetch('/api/search/stream', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ query }),\n      });\n\n      if (!response.body) throw new Error('No response body');\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value);\n        const lines = chunk.split('\\n');\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            try {\n              const data = JSON.parse(line.slice(6));\n\n              switch (data.type) {\n                case 'status':\n                  setStatus(data.message);\n                  break;\n                case 'sources':\n                  setSources(data.data);\n                  break;\n                case 'recommendations':\n                  setRecommendations(data.data);\n                  break;\n                case 'content':\n                  setStreamedContent(prev => prev + data.data);\n                  break;\n                case 'complete':\n                  setStatus('');\n                  break;\n                case 'error':\n                  console.error('Stream error:', data.message);\n                  break;\n              }\n            } catch (e) {\n              // Ignore parsing errors for incomplete chunks\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setStatus('An error occurred while searching');\n    } finally {\n      setIsSearching(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"flex justify-between items-start mb-8\">\n          <div className=\"flex-1 text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-4 gradient-text\">\n              AI Search Engine\n            </h1>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto\">\n              Get comprehensive answers powered by AI and real-time web search\n            </p>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <ThemeToggle />\n          </div>\n        </div>\n\n        {/* Search Form */}\n        <div className=\"max-w-4xl mx-auto mb-8\">\n          <form onSubmit={handleSearch}>\n            <div className=\"relative\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={query}\n                onChange={(e) => setQuery(e.target.value)}\n                placeholder=\"Ask anything...\"\n                className=\"w-full px-6 py-4 pr-16 text-lg border border-gray-300 rounded-2xl shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-slate-800 dark:border-slate-600 dark:text-white\"\n                disabled={isSearching}\n              />\n              <button\n                type=\"submit\"\n                disabled={isSearching || !query.trim()}\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2 p-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {isSearching ? (\n                  <Loader2 className=\"w-5 h-5 animate-spin\" />\n                ) : (\n                  <Search className=\"w-5 h-5\" />\n                )}\n              </button>\n            </div>\n          </form>\n\n          {/* Search History */}\n          <div className=\"mt-4 flex justify-center\">\n            <SearchHistory\n              onSelectQuery={setQuery}\n              currentQuery={query}\n            />\n          </div>\n        </div>\n\n        {/* Status */}\n        {status && (\n          <div className=\"max-w-4xl mx-auto mb-6\">\n            <div className=\"flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400\">\n              <Loader2 className=\"w-4 h-4 animate-spin\" />\n              <span>{status}</span>\n            </div>\n          </div>\n        )}\n\n        {/* Results */}\n        {(streamedContent || sources.length > 0 || recommendations) && (\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n              {/* AI Response */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-6\">\n                  <h2 className=\"text-xl font-semibold mb-4 text-gray-800 dark:text-white\">\n                    AI Response\n                  </h2>\n                  <div className=\"prose dark:prose-invert max-w-none\">\n                    {streamedContent ? (\n                      <CitationRenderer content={streamedContent} sources={sources} />\n                    ) : (\n                      <div className=\"text-gray-500 dark:text-gray-400\">\n                        Waiting for response...\n                      </div>\n                    )}\n                    {isSearching && (\n                      <span className=\"inline-block w-2 h-5 bg-blue-600 animate-pulse ml-1\"></span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Inline Ad between AI Response and Sources */}\n                {recommendations && (\n                  <div className=\"mt-6\">\n                    <AdmeshInlineAd\n                      admeshResponse={recommendations}\n                      adType=\"banner\"\n                      showBadge={true}\n                      maxRecommendations={1}\n                      onRecommendationClick={(adId, admeshLink) => {\n                        console.log('Inline ad clicked:', { adId, admeshLink });\n                        window.open(admeshLink, '_blank');\n                      }}\n                    />\n                  </div>\n                )}\n              </div>\n\n              {/* Sources */}\n              {sources.length > 0 && (\n                <div className=\"lg:col-span-1\">\n                  <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-6\">\n                    <h2 className=\"text-xl font-semibold mb-4 text-gray-800 dark:text-white\">\n                      Sources\n                    </h2>\n                    <div className=\"space-y-4\">\n                      {sources.map((source, index) => (\n                        <React.Fragment key={source.id}>\n                          <div className=\"border border-gray-200 dark:border-slate-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors\">\n                            <div className=\"flex items-start justify-between mb-3\">\n                              <div className=\"flex items-center space-x-2\">\n                                <span className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">\n                                  [{source.id}]\n                                </span>\n                                {source.favicon && (\n                                  <img\n                                    src={source.favicon}\n                                    alt=\"\"\n                                    className=\"w-4 h-4\"\n                                    onError={(e) => {\n                                      e.currentTarget.style.display = 'none';\n                                    }}\n                                  />\n                                )}\n                              </div>\n                              <a\n                                href={source.url}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\"\n                              >\n                                <ExternalLink className=\"w-4 h-4\" />\n                              </a>\n                            </div>\n                            <h3 className=\"font-medium text-gray-800 dark:text-white mb-2 line-clamp-2\">\n                              {source.title}\n                            </h3>\n                            <p className=\"text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-2\">\n                              {source.snippet}\n                            </p>\n                            <p className=\"text-xs text-gray-500 dark:text-gray-500 truncate\">\n                              {new URL(source.url).hostname}\n                            </p>\n                          </div>\n\n                          {/* Compact inline ad after second source */}\n                          {index === 1 && recommendations && (\n                            <AdmeshInlineAd\n                              admeshResponse={recommendations}\n                              adType=\"compact\"\n                              showBadge={false}\n                              maxRecommendations={1}\n                              onRecommendationClick={(adId, admeshLink) => {\n                                console.log('Compact ad clicked:', { adId, admeshLink });\n                                window.open(admeshLink, '_blank');\n                              }}\n                            />\n                          )}\n                        </React.Fragment>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Smart Recommendations */}\n              {/* {recommendations && (\n                <div className=\"lg:col-span-1\">\n                  <AdmeshRecommendations\n                    admeshResponse={recommendations}\n                    displayMode=\"cards\"\n                    theme={{ mode: 'light' }}\n                    onRecommendationClick={(adId, admeshLink) => {\n                      console.log('Recommendation clicked:', { adId, admeshLink });\n                      window.open(admeshLink, '_blank');\n                    }}\n                  />\n                </div>\n              )} */}\n            </div>\n          </div>\n        )}\n\n        {/* Example Queries */}\n        {!streamedContent && !isSearching && (\n          <div className=\"max-w-4xl mx-auto mt-12\">\n            <h3 className=\"text-lg font-semibold text-center mb-6 text-gray-700 dark:text-gray-300\">\n              Try asking about:\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[\n                \"What's the latest news in AI?\",\n                \"How does quantum computing work?\",\n                \"Best practices for React development\",\n                \"Climate change recent developments\",\n                \"Space exploration missions 2024\",\n                \"Cryptocurrency market trends\"\n              ].map((example, index) => (\n                <button\n                  key={index}\n                  onClick={() => setQuery(example)}\n                  className=\"p-4 text-left bg-white dark:bg-slate-800 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200 dark:border-slate-600\"\n                >\n                  <span className=\"text-gray-700 dark:text-gray-300\">{example}</span>\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Floating Recommendation Widget */}\n        <AdmeshFloatingWidget\n          admeshResponse={recommendations}\n          theme={{ mode: 'light' }}\n          position=\"bottom-right\"\n          autoShow={true}\n          showDelay={2000}\n          onRecommendationClick={(adId, admeshLink) => {\n            console.log('Floating widget clicked:', { adId, admeshLink });\n            window.open(admeshLink, '_blank');\n          }}\n          onDismiss={() => {\n            console.log('Floating widget dismissed');\n          }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AATA;;;;;;;;;AAwCe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,OAAO,EAAE;IACpB,GAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,aAAa;QAElC,eAAe;QACf,YAAY;QACZ,mBAAmB;QACnB,WAAW,EAAE;QACb,mBAAmB;QACnB,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,IAAI,CAAC,SAAS,IAAI,EAAE,MAAM,IAAI,MAAM;YAEpC,MAAM,SAAS,SAAS,IAAI,CAAC,SAAS;YACtC,MAAM,UAAU,IAAI;YAEpB,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;gBAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;gBAE1B,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;wBAC7B,IAAI;4BACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;4BAEnC,OAAQ,KAAK,IAAI;gCACf,KAAK;oCACH,UAAU,KAAK,OAAO;oCACtB;gCACF,KAAK;oCACH,WAAW,KAAK,IAAI;oCACpB;gCACF,KAAK;oCACH,mBAAmB,KAAK,IAAI;oCAC5B;gCACF,KAAK;oCACH,mBAAmB,CAAA,OAAQ,OAAO,KAAK,IAAI;oCAC3C;gCACF,KAAK;oCACH,UAAU;oCACV;gCACF,KAAK;oCACH,QAAQ,KAAK,CAAC,iBAAiB,KAAK,OAAO;oCAC3C;4BACJ;wBACF,EAAE,OAAO,GAAG;wBACV,8CAA8C;wBAChD;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,UAAU;QACZ,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAI5E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,iIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,UAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,UAAU,eAAe,CAAC,MAAM,IAAI;wCACpC,WAAU;kDAET,4BACC,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAO1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;gCACZ,eAAe;gCACf,cAAc;;;;;;;;;;;;;;;;;gBAMnB,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;0CAAM;;;;;;;;;;;;;;;;;gBAMZ,CAAC,mBAAmB,QAAQ,MAAM,GAAG,KAAK,eAAe,mBACxD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,8OAAC;gDAAI,WAAU;;oDACZ,gCACC,8OAAC,sIAAA,CAAA,UAAgB;wDAAC,SAAS;wDAAiB,SAAS;;;;;6EAErD,8OAAC;wDAAI,WAAU;kEAAmC;;;;;;oDAInD,6BACC,8OAAC;wDAAK,WAAU;;;;;;;;;;;;;;;;;;oCAMrB,iCACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;4CACb,gBAAgB;4CAChB,QAAO;4CACP,WAAW;4CACX,oBAAoB;4CACpB,uBAAuB,CAAC,MAAM;gDAC5B,QAAQ,GAAG,CAAC,sBAAsB;oDAAE;oDAAM;gDAAW;gDACrD,OAAO,IAAI,CAAC,YAAY;4CAC1B;;;;;;;;;;;;;;;;;4BAOP,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;;wFAAuD;wFACnE,OAAO,EAAE;wFAAC;;;;;;;gFAEb,OAAO,OAAO,kBACb,8OAAC;oFACC,KAAK,OAAO,OAAO;oFACnB,KAAI;oFACJ,WAAU;oFACV,SAAS,CAAC;wFACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oFAClC;;;;;;;;;;;;sFAIN,8OAAC;4EACC,MAAM,OAAO,GAAG;4EAChB,QAAO;4EACP,KAAI;4EACJ,WAAU;sFAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;;;;;;;;;;;;8EAG5B,8OAAC;oEAAG,WAAU;8EACX,OAAO,KAAK;;;;;;8EAEf,8OAAC;oEAAE,WAAU;8EACV,OAAO,OAAO;;;;;;8EAEjB,8OAAC;oEAAE,WAAU;8EACV,IAAI,IAAI,OAAO,GAAG,EAAE,QAAQ;;;;;;;;;;;;wDAKhC,UAAU,KAAK,iCACd,8OAAC,oIAAA,CAAA,UAAc;4DACb,gBAAgB;4DAChB,QAAO;4DACP,WAAW;4DACX,oBAAoB;4DACpB,uBAAuB,CAAC,MAAM;gEAC5B,QAAQ,GAAG,CAAC,uBAAuB;oEAAE;oEAAM;gEAAW;gEACtD,OAAO,IAAI,CAAC,YAAY;4DAC1B;;;;;;;mDAhDe,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBA6E7C,CAAC,mBAAmB,CAAC,6BACpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0E;;;;;;sCAGxF,8OAAC;4BAAI,WAAU;sCACZ;gCACC;gCACA;gCACA;gCACA;gCACA;gCACA;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;oCAEC,SAAS,IAAM,SAAS;oCACxB,WAAU;8CAEV,cAAA,8OAAC;wCAAK,WAAU;kDAAoC;;;;;;mCAJ/C;;;;;;;;;;;;;;;;8BAYf,8OAAC,0IAAA,CAAA,UAAoB;oBACnB,gBAAgB;oBAChB,OAAO;wBAAE,MAAM;oBAAQ;oBACvB,UAAS;oBACT,UAAU;oBACV,WAAW;oBACX,uBAAuB,CAAC,MAAM;wBAC5B,QAAQ,GAAG,CAAC,4BAA4B;4BAAE;4BAAM;wBAAW;wBAC3D,OAAO,IAAI,CAAC,YAAY;oBAC1B;oBACA,WAAW;wBACT,QAAQ,GAAG,CAAC;oBACd;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}]}