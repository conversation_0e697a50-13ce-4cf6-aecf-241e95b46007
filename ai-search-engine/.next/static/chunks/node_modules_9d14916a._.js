(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "hasA11yProp": (()=>hasA11yProp),
    "mergeClasses": (()=>mergeClasses),
    "toCamelCase": (()=>toCamelCase),
    "toKebabCase": (()=>toKebabCase),
    "toPascalCase": (()=>toPascalCase)
});
const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string)=>string.replace(/^([A-Z])|[\s-_]+(\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());
const toPascalCase = (string)=>{
    const camelCase = toCamelCase(string);
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = (...classes)=>classes.filter((className, index, array)=>{
        return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
    }).join(" ").trim();
const hasA11yProp = (props)=>{
    for(const prop in props){
        if (prop.startsWith("aria-") || prop === "role" || prop === "title") {
            return true;
        }
    }
};
;
 //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>defaultAttributes)
});
var defaultAttributes = {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: 2,
    strokeLinecap: "round",
    strokeLinejoin: "round"
};
;
 //# sourceMappingURL=defaultAttributes.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>Icon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
;
;
;
const Icon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ color = "currentColor", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = "", children, iconNode, ...rest }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        ref,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        width: size,
        height: size,
        stroke: color,
        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])("lucide", className),
        ...!children && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasA11yProp"])(rest) && {
            "aria-hidden": "true"
        },
        ...rest
    }, [
        ...iconNode.map(([tag, attrs])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(tag, attrs)),
        ...Array.isArray(children) ? children : [
            children
        ]
    ]));
;
 //# sourceMappingURL=Icon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>createLucideIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)");
;
;
;
const createLucideIcon = (iconName, iconNode)=>{
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            ref,
            iconNode,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])(`lucide-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toKebabCase"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName))}`, `lucide-${iconName}`, className),
            ...props
        }));
    Component.displayName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName);
    return Component;
};
;
 //# sourceMappingURL=createLucideIcon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Search)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m21 21-4.34-4.34",
            key: "14j7rj"
        }
    ],
    [
        "circle",
        {
            cx: "11",
            cy: "11",
            r: "8",
            key: "4ej97u"
        }
    ]
];
const Search = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("search", __iconNode);
;
 //# sourceMappingURL=search.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Search": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>LoaderCircle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M21 12a9 9 0 1 1-6.219-8.56",
            key: "13zald"
        }
    ]
];
const LoaderCircle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("loader-circle", __iconNode);
;
 //# sourceMappingURL=loader-circle.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Loader2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ExternalLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M15 3h6v6",
            key: "1q9fwt"
        }
    ],
    [
        "path",
        {
            d: "M10 14 21 3",
            key: "gplh6r"
        }
    ],
    [
        "path",
        {
            d: "M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",
            key: "a6xqqp"
        }
    ]
];
const ExternalLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("external-link", __iconNode);
;
 //# sourceMappingURL=external-link.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLink>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ExternalLink": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Moon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",
            key: "a7tn18"
        }
    ]
];
const Moon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("moon", __iconNode);
;
 //# sourceMappingURL=moon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-client] (ecmascript) <export default as Moon>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Moon": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$moon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$moon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/moon.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Sun)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "circle",
        {
            cx: "12",
            cy: "12",
            r: "4",
            key: "4exip2"
        }
    ],
    [
        "path",
        {
            d: "M12 2v2",
            key: "tus03m"
        }
    ],
    [
        "path",
        {
            d: "M12 20v2",
            key: "1lh1kg"
        }
    ],
    [
        "path",
        {
            d: "m4.93 4.93 1.41 1.41",
            key: "149t6j"
        }
    ],
    [
        "path",
        {
            d: "m17.66 17.66 1.41 1.41",
            key: "ptbguv"
        }
    ],
    [
        "path",
        {
            d: "M2 12h2",
            key: "1t8f8n"
        }
    ],
    [
        "path",
        {
            d: "M20 12h2",
            key: "1q8mjw"
        }
    ],
    [
        "path",
        {
            d: "m6.34 17.66-1.41 1.41",
            key: "1m8zz5"
        }
    ],
    [
        "path",
        {
            d: "m19.07 4.93-1.41 1.41",
            key: "1shlcs"
        }
    ]
];
const Sun = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("sun", __iconNode);
;
 //# sourceMappingURL=sun.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-client] (ecmascript) <export default as Sun>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Sun": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sun$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$sun$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/sun.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Clock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "circle",
        {
            cx: "12",
            cy: "12",
            r: "10",
            key: "1mglay"
        }
    ],
    [
        "polyline",
        {
            points: "12 6 12 12 16 14",
            key: "68esgv"
        }
    ]
];
const Clock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("clock", __iconNode);
;
 //# sourceMappingURL=clock.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Clock": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>X)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M18 6 6 18",
            key: "1bl5f8"
        }
    ],
    [
        "path",
        {
            d: "m6 6 12 12",
            key: "d8bk6v"
        }
    ]
];
const X = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("x", __iconNode);
;
 //# sourceMappingURL=x.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "X": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/admesh-ui-sdk/dist/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdMeshAutoRecommendationWidget": (()=>er),
    "AdMeshBadge": (()=>Ie),
    "AdMeshChatInput": (()=>Be),
    "AdMeshChatInterface": (()=>Oe),
    "AdMeshChatMessage": (()=>We),
    "AdMeshCitationReference": (()=>be),
    "AdMeshCitationUnit": (()=>ze),
    "AdMeshCompareTable": (()=>Ae),
    "AdMeshConversationSummary": (()=>Pe),
    "AdMeshConversationalUnit": (()=>ye),
    "AdMeshExpandableUnit": (()=>Qe),
    "AdMeshFloatingChat": (()=>Xe),
    "AdMeshInlineRecommendation": (()=>q),
    "AdMeshLinkTracker": (()=>U),
    "AdMeshProductCard": (()=>ue),
    "AdMeshSidebar": (()=>Ze),
    "AdMeshSidebarContent": (()=>Fe),
    "AdMeshSidebarHeader": (()=>$e),
    "DEFAULT_CONFIG": (()=>nr),
    "VERSION": (()=>lr),
    "buildAdMeshLink": (()=>qe),
    "createAdMeshTheme": (()=>G),
    "createDarkTheme": (()=>tr),
    "extractTrackingData": (()=>Ye),
    "getBadgeText": (()=>Te),
    "getCtaText": (()=>Ge),
    "getInlineDisclosure": (()=>ce),
    "getInlineTooltip": (()=>xe),
    "getLabelTooltip": (()=>se),
    "getPoweredByText": (()=>Ke),
    "getRecommendationLabel": (()=>H),
    "getSectionDisclosure": (()=>He),
    "hasHighQualityMatches": (()=>Je),
    "mergeThemes": (()=>ar),
    "setAdMeshTrackerConfig": (()=>Ue),
    "themeFromCSSProperties": (()=>or),
    "themePresets": (()=>sr),
    "useAdMeshStyles": (()=>rr),
    "useAdMeshTracker": (()=>Le)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function we(r) {
    return r && r.__esModule && Object.prototype.hasOwnProperty.call(r, "default") ? r.default : r;
}
var te = {
    exports: {}
}, X = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var me;
function je() {
    if (me) return X;
    me = 1;
    var r = Symbol.for("react.transitional.element"), t = Symbol.for("react.fragment");
    function s(n, o, a) {
        var i = null;
        if (a !== void 0 && (i = "" + a), o.key !== void 0 && (i = "" + o.key), "key" in o) {
            a = {};
            for(var x in o)x !== "key" && (a[x] = o[x]);
        } else a = o;
        return o = a.ref, {
            $$typeof: r,
            type: n,
            key: i,
            ref: o !== void 0 ? o : null,
            props: a
        };
    }
    return X.Fragment = t, X.jsx = s, X.jsxs = s, X;
}
var Z = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var pe;
function Ne() {
    return pe || (pe = 1, ("TURBOPACK compile-time value", "development") !== "production" && function() {
        function r(l) {
            if (l == null) return null;
            if (typeof l == "function") return l.$$typeof === $ ? null : l.displayName || l.name || null;
            if (typeof l == "string") return l;
            switch(l){
                case v:
                    return "Fragment";
                case f:
                    return "Profiler";
                case b:
                    return "StrictMode";
                case M:
                    return "Suspense";
                case S:
                    return "SuspenseList";
                case I:
                    return "Activity";
            }
            if (typeof l == "object") switch(typeof l.tag == "number" && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), l.$$typeof){
                case u:
                    return "Portal";
                case g:
                    return (l.displayName || "Context") + ".Provider";
                case _:
                    return (l._context.displayName || "Context") + ".Consumer";
                case L:
                    var j = l.render;
                    return l = l.displayName, l || (l = j.displayName || j.name || "", l = l !== "" ? "ForwardRef(" + l + ")" : "ForwardRef"), l;
                case k:
                    return j = l.displayName || null, j !== null ? j : r(l.type) || "Memo";
                case T:
                    j = l._payload, l = l._init;
                    try {
                        return r(l(j));
                    } catch  {}
            }
            return null;
        }
        function t(l) {
            return "" + l;
        }
        function s(l) {
            try {
                t(l);
                var j = !1;
            } catch  {
                j = !0;
            }
            if (j) {
                j = console;
                var R = j.error, B = typeof Symbol == "function" && Symbol.toStringTag && l[Symbol.toStringTag] || l.constructor.name || "Object";
                return R.call(j, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", B), t(l);
            }
        }
        function n(l) {
            if (l === v) return "<>";
            if (typeof l == "object" && l !== null && l.$$typeof === T) return "<...>";
            try {
                var j = r(l);
                return j ? "<" + j + ">" : "<...>";
            } catch  {
                return "<...>";
            }
        }
        function o() {
            var l = W.A;
            return l === null ? null : l.getOwner();
        }
        function a() {
            return Error("react-stack-top-frame");
        }
        function i(l) {
            if (w.call(l, "key")) {
                var j = Object.getOwnPropertyDescriptor(l, "key").get;
                if (j && j.isReactWarning) return !1;
            }
            return l.key !== void 0;
        }
        function x(l, j) {
            function R() {
                C || (C = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", j));
            }
            R.isReactWarning = !0, Object.defineProperty(l, "key", {
                get: R,
                configurable: !0
            });
        }
        function p() {
            var l = r(this.type);
            return E[l] || (E[l] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")), l = this.props.ref, l !== void 0 ? l : null;
        }
        function h(l, j, R, B, Y, V, ae, oe) {
            return R = V.ref, l = {
                $$typeof: y,
                type: l,
                key: j,
                props: V,
                _owner: Y
            }, (R !== void 0 ? R : null) !== null ? Object.defineProperty(l, "ref", {
                enumerable: !1,
                get: p
            }) : Object.defineProperty(l, "ref", {
                enumerable: !1,
                value: null
            }), l._store = {}, Object.defineProperty(l._store, "validated", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: 0
            }), Object.defineProperty(l, "_debugInfo", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: null
            }), Object.defineProperty(l, "_debugStack", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: ae
            }), Object.defineProperty(l, "_debugTask", {
                configurable: !1,
                enumerable: !1,
                writable: !0,
                value: oe
            }), Object.freeze && (Object.freeze(l.props), Object.freeze(l)), l;
        }
        function c(l, j, R, B, Y, V, ae, oe) {
            var O = j.children;
            if (O !== void 0) if (B) if (F(O)) {
                for(B = 0; B < O.length; B++)m(O[B]);
                Object.freeze && Object.freeze(O);
            } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else m(O);
            if (w.call(j, "key")) {
                O = r(l);
                var K = Object.keys(j).filter(function(ve) {
                    return ve !== "key";
                });
                B = 0 < K.length ? "{key: someKey, " + K.join(": ..., ") + ": ...}" : "{key: someKey}", he[O + B] || (K = 0 < K.length ? "{" + K.join(": ..., ") + ": ...}" : "{}", console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`, B, O, K, O), he[O + B] = !0);
            }
            if (O = null, R !== void 0 && (s(R), O = "" + R), i(j) && (s(j.key), O = "" + j.key), "key" in j) {
                R = {};
                for(var le in j)le !== "key" && (R[le] = j[le]);
            } else R = j;
            return O && x(R, typeof l == "function" ? l.displayName || l.name || "Unknown" : l), h(l, O, V, Y, o(), R, ae, oe);
        }
        function m(l) {
            typeof l == "object" && l !== null && l.$$typeof === y && l._store && (l._store.validated = 1);
        }
        var d = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], y = Symbol.for("react.transitional.element"), u = Symbol.for("react.portal"), v = Symbol.for("react.fragment"), b = Symbol.for("react.strict_mode"), f = Symbol.for("react.profiler"), _ = Symbol.for("react.consumer"), g = Symbol.for("react.context"), L = Symbol.for("react.forward_ref"), M = Symbol.for("react.suspense"), S = Symbol.for("react.suspense_list"), k = Symbol.for("react.memo"), T = Symbol.for("react.lazy"), I = Symbol.for("react.activity"), $ = Symbol.for("react.client.reference"), W = d.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, w = Object.prototype.hasOwnProperty, F = Array.isArray, P = console.createTask ? console.createTask : function() {
            return null;
        };
        d = {
            "react-stack-bottom-frame": function(l) {
                return l();
            }
        };
        var C, E = {}, z = d["react-stack-bottom-frame"].bind(d, a)(), J = P(n(a)), he = {};
        Z.Fragment = v, Z.jsx = function(l, j, R, B, Y) {
            var V = 1e4 > W.recentlyCreatedOwnerStacks++;
            return c(l, j, R, !1, B, Y, V ? Error("react-stack-top-frame") : z, V ? P(n(l)) : J);
        }, Z.jsxs = function(l, j, R, B, Y) {
            var V = 1e4 > W.recentlyCreatedOwnerStacks++;
            return c(l, j, R, !0, B, Y, V ? Error("react-stack-top-frame") : z, V ? P(n(l)) : J);
        };
    }()), Z;
}
var ge;
function _e() {
    return ge || (ge = 1, ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : te.exports = Ne()), te.exports;
}
var e = _e(), ne = {
    exports: {}
};
/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/ var fe;
function Ce() {
    return fe || (fe = 1, function(r) {
        (function() {
            var t = {}.hasOwnProperty;
            function s() {
                for(var a = "", i = 0; i < arguments.length; i++){
                    var x = arguments[i];
                    x && (a = o(a, n(x)));
                }
                return a;
            }
            function n(a) {
                if (typeof a == "string" || typeof a == "number") return a;
                if (typeof a != "object") return "";
                if (Array.isArray(a)) return s.apply(null, a);
                if (a.toString !== Object.prototype.toString && !a.toString.toString().includes("[native code]")) return a.toString();
                var i = "";
                for(var x in a)t.call(a, x) && a[x] && (i = o(i, x));
                return i;
            }
            function o(a, i) {
                return i ? a ? a + " " + i : a + i : a;
            }
            r.exports ? (s.default = s, r.exports = s) : window.classNames = s;
        })();
    }(ne)), ne.exports;
}
var Me = Ce();
const N = /* @__PURE__ */ we(Me), Se = "https://api.useadmesh.com/track";
let de = {
    apiBaseUrl: Se,
    enabled: !0,
    debug: !1,
    retryAttempts: 3,
    retryDelay: 1e3
};
const Ue = (r)=>{
    de = {
        ...de,
        ...r
    };
}, Le = (r)=>{
    const [t, s] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), [n, o] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            ...de,
            ...r
        }), [
        r
    ]), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])((m, d)=>{
        a.debug && console.log(`[AdMesh Tracker] ${m}`, d);
    }, [
        a.debug
    ]), x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(async (m, d)=>{
        if (!a.enabled) {
            i("Tracking disabled, skipping event", {
                eventType: m,
                data: d
            });
            return;
        }
        if (!d.adId || !d.admeshLink) {
            const b = "Missing required tracking data: adId and admeshLink are required";
            i(b, d), o(b);
            return;
        }
        s(!0), o(null);
        const y = {
            event_type: m,
            ad_id: d.adId,
            admesh_link: d.admeshLink,
            product_id: d.productId,
            user_id: d.userId,
            session_id: d.sessionId,
            revenue: d.revenue,
            conversion_type: d.conversionType,
            metadata: d.metadata,
            timestamp: /* @__PURE__ */ new Date().toISOString(),
            user_agent: navigator.userAgent,
            referrer: document.referrer,
            page_url: window.location.href
        };
        i(`Sending ${m} event`, y);
        let u = null;
        for(let b = 1; b <= (a.retryAttempts || 3); b++)try {
            const f = await fetch(`${a.apiBaseUrl}/events`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(y)
            });
            if (!f.ok) throw new Error(`HTTP ${f.status}: ${f.statusText}`);
            const _ = await f.json();
            i(`${m} event tracked successfully`, _), s(!1);
            return;
        } catch (f) {
            u = f, i(`Attempt ${b} failed for ${m} event`, f), b < (a.retryAttempts || 3) && await new Promise((_)=>setTimeout(_, (a.retryDelay || 1e3) * b));
        }
        const v = `Failed to track ${m} event after ${a.retryAttempts} attempts: ${u == null ? void 0 : u.message}`;
        i(v, u), o(v), s(!1);
    }, [
        a,
        i
    ]), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(async (m)=>x("click", m), [
        x
    ]), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(async (m)=>x("view", m), [
        x
    ]), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(async (m)=>(!m.revenue && !m.conversionType && i("Warning: Conversion tracking without revenue or conversion type", m), x("conversion", m)), [
        x,
        i
    ]);
    return {
        trackClick: p,
        trackView: h,
        trackConversion: c,
        isTracking: t,
        error: n
    };
}, qe = (r, t, s)=>{
    try {
        const n = new URL(r);
        return n.searchParams.set("ad_id", t), n.searchParams.set("utm_source", "admesh"), n.searchParams.set("utm_medium", "recommendation"), s && Object.entries(s).forEach(([o, a])=>{
            n.searchParams.set(o, a);
        }), n.toString();
    } catch (n) {
        return console.warn("[AdMesh] Invalid URL provided to buildAdMeshLink:", r, n), r;
    }
}, Ye = (r, t)=>({
        adId: r.ad_id,
        admeshLink: r.admesh_link,
        productId: r.product_id,
        ...t
    }), U = ({ adId: r, admeshLink: t, productId: s, children: n, onClick: o, trackingData: a, className: i })=>{
    const { trackClick: x, trackView: p } = Le(), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!h.current || c.current) return;
        const d = new IntersectionObserver((y)=>{
            y.forEach((u)=>{
                u.isIntersecting && !c.current && (c.current = !0, p({
                    adId: r,
                    admeshLink: t,
                    productId: s,
                    ...a
                }).catch(console.error));
            });
        }, {
            threshold: 0.5,
            // Track when 50% of the element is visible
            rootMargin: "0px"
        });
        return d.observe(h.current), ()=>{
            d.disconnect();
        };
    }, [
        r,
        t,
        s,
        a,
        p
    ]);
    const m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(async (d)=>{
        try {
            await x({
                adId: r,
                admeshLink: t,
                productId: s,
                ...a
            });
        } catch (v) {
            console.error("Failed to track click:", v);
        }
        o && o(), d.target.closest("a") || window.open(t, "_blank", "noopener,noreferrer");
    }, [
        r,
        t,
        s,
        a,
        x,
        o
    ]);
    return /* @__PURE__ */ e.jsx("div", {
        ref: h,
        className: i,
        onClick: m,
        style: {
            cursor: "pointer"
        },
        children: n
    });
};
U.displayName = "AdMeshLinkTracker";
const H = (r, t = {})=>{
    const s = r.intent_match_score || 0, n = t.customLabels || {};
    return s >= 0.8 ? n.smartPick || "Smart Pick" : s >= 0.6 ? n.partnerMatch || "Partner Match" : s >= 0.3 ? n.promotedOption || "Promoted Option" : n.relatedOption || "Related Option";
}, se = (r, t)=>{
    const s = r.intent_match_score || 0;
    return s >= 0.8 ? "This recommendation is from a partner who compensates us when you engage. We've matched it to your needs based on your query." : s >= 0.6 ? "Top-rated partner solution matched to your specific requirements. Partner compensates us for qualified referrals." : s >= 0.3 ? "This partner solution may be relevant to your needs. The partner compensates us when you take qualifying actions." : "This solution is somewhat related to your query. While not a perfect match, it might still be helpful. This partner compensates us for qualified referrals.";
}, He = (r = !0, t = !1)=>r ? t ? "These curated recommendations are from partners who compensate us for referrals." : "Personalized Partner Recommendations: All results are from vetted partners who compensate us for qualified matches. We've ranked them based on relevance to your specific needs." : "Expanded Results: While these don't perfectly match your query, they're related solutions from our partner network. All partners compensate us for referrals.", ce = (r, t = !1)=>{
    const s = r.intent_match_score || 0;
    return t ? "Promoted Match" : s >= 0.8 ? "Smart Pick" : s >= 0.6 ? "Partner Match" : "Promoted Option";
}, xe = ()=>"We've partnered with trusted providers to bring you relevant solutions. These partners compensate us for qualified referrals, which helps us keep our service free.", Te = (r)=>({
        "Top Match": "Top Match",
        "Smart Pick": "Smart Pick",
        "Perfect Fit": "Perfect Fit",
        "Great Match": "Great Match",
        Recommended: "Recommended",
        "Good Fit": "Good Fit",
        Featured: "Featured",
        "Popular Choice": "Popular Choice",
        "Premium Pick": "Premium Pick",
        "Free Tier": "Free Tier",
        "AI Powered": "AI Powered",
        Popular: "Popular",
        New: "New",
        "Trial Available": "Trial Available",
        "Related Option": "Related Option",
        "Alternative Solution": "Alternative Solution",
        "Expanded Match": "Expanded Match"
    })[r] || r, Ge = (r, t = "button")=>{
    const s = r.recommendation_title || r.title;
    return t === "link" ? s : r.trial_days && r.trial_days > 0 ? `Try ${s}` : "Learn More";
}, Je = (r)=>r.some((t)=>(t.intent_match_score || 0) >= 0.8), Ke = (r = !1)=>r ? "Powered by AdMesh" : "Recommendations powered by AdMesh", ue = ({ recommendation: r, theme: t, showMatchScore: s = !1, showBadges: n = !0, variation: o = "default", onClick: a, className: i })=>{
    var f, _, g, L, M, S, k, T, I, $, W;
    const [x, p] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), h = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        var E;
        const w = [];
        H(r) === "Smart Pick" && w.push("Top Match"), r.trial_days && r.trial_days > 0 && w.push("Trial Available");
        const P = [
            "ai",
            "artificial intelligence",
            "machine learning",
            "ml",
            "automation"
        ];
        return (((E = r.keywords) == null ? void 0 : E.some((z)=>P.some((J)=>z.toLowerCase().includes(J)))) || r.title.toLowerCase().includes("ai")) && w.push("AI Powered"), r.badges && r.badges.length > 0 && r.badges.forEach((z)=>{
            [
                "Top Match",
                "Free Tier",
                "AI Powered",
                "Popular",
                "New",
                "Trial Available"
            ].includes(z) && !w.includes(z) && w.push(z);
        }), r.is_open_source && w.push("Popular"), w;
    }, [
        r
    ]), c = ce(r, !1), m = xe(), d = Math.round(r.intent_match_score * 100), u = (()=>{
        const w = r.content_variations;
        return o === "simple" ? {
            title: r.recommendation_title || r.title,
            description: r.recommendation_description || r.description || r.reason,
            ctaText: r.recommendation_title || r.title,
            isSimple: !0
        } : o === "question" && w != null && w.question ? {
            title: w.question.cta || r.recommendation_title || r.title,
            description: w.question.text,
            ctaText: w.question.cta || r.recommendation_title || r.title
        } : o === "statement" && w != null && w.statement ? {
            title: r.recommendation_title || r.title,
            description: w.statement.text,
            ctaText: w.statement.cta || r.recommendation_title || r.title
        } : {
            title: r.recommendation_title || r.title,
            description: r.recommendation_description || r.description || r.reason,
            ctaText: r.recommendation_title || r.title
        };
    })(), v = N("admesh-component", "admesh-card", "relative p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1", i), b = t ? {
        "--admesh-primary": t.primaryColor || t.accentColor || "#3b82f6",
        "--admesh-secondary": t.secondaryColor || "#10b981",
        "--admesh-accent": t.accentColor || "#3b82f6",
        "--admesh-background": t.backgroundColor,
        "--admesh-surface": t.surfaceColor,
        "--admesh-border": t.borderColor,
        "--admesh-text": t.textColor,
        "--admesh-text-secondary": t.textSecondaryColor,
        "--admesh-radius": t.borderRadius || "12px",
        "--admesh-shadow-sm": (f = t.shadows) == null ? void 0 : f.small,
        "--admesh-shadow-md": (_ = t.shadows) == null ? void 0 : _.medium,
        "--admesh-shadow-lg": (g = t.shadows) == null ? void 0 : g.large,
        "--admesh-spacing-sm": (L = t.spacing) == null ? void 0 : L.small,
        "--admesh-spacing-md": (M = t.spacing) == null ? void 0 : M.medium,
        "--admesh-spacing-lg": (S = t.spacing) == null ? void 0 : S.large,
        "--admesh-font-size-sm": (k = t.fontSize) == null ? void 0 : k.small,
        "--admesh-font-size-base": (T = t.fontSize) == null ? void 0 : T.base,
        "--admesh-font-size-lg": (I = t.fontSize) == null ? void 0 : I.large,
        "--admesh-font-size-title": ($ = t.fontSize) == null ? void 0 : $.title,
        fontFamily: t.fontFamily
    } : void 0;
    return o === "simple" ? /* @__PURE__ */ e.jsxs("div", {
        className: N("admesh-component admesh-simple-ad", "inline-block text-sm leading-relaxed", i),
        style: {
            fontFamily: (t == null ? void 0 : t.fontFamily) || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            ...(W = t == null ? void 0 : t.components) == null ? void 0 : W.productCard
        },
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            /* @__PURE__ */ e.jsx("span", {
                style: {
                    fontSize: "11px",
                    fontWeight: "600",
                    color: (t == null ? void 0 : t.accentColor) || "#2563eb",
                    backgroundColor: (t == null ? void 0 : t.mode) === "dark" ? "#374151" : "#f3f4f6",
                    padding: "2px 6px",
                    borderRadius: "4px",
                    marginRight: "8px"
                },
                title: se(r, H(r)),
                children: H(r)
            }),
            /* @__PURE__ */ e.jsxs("span", {
                style: {
                    color: (t == null ? void 0 : t.mode) === "dark" ? "#f3f4f6" : "#374151",
                    marginRight: "4px"
                },
                children: [
                    u.description,
                    " "
                ]
            }),
            /* @__PURE__ */ e.jsx(U, {
                adId: r.ad_id,
                admeshLink: r.admesh_link,
                productId: r.product_id,
                onClick: ()=>a == null ? void 0 : a(r.ad_id, r.admesh_link),
                trackingData: {
                    title: r.title,
                    matchScore: r.intent_match_score,
                    component: "simple_ad_cta"
                },
                children: /* @__PURE__ */ e.jsx("span", {
                    style: {
                        color: (t == null ? void 0 : t.accentColor) || "#2563eb",
                        textDecoration: "underline",
                        cursor: "pointer",
                        fontSize: "inherit",
                        fontFamily: "inherit"
                    },
                    children: u.ctaText
                })
            }),
            /* @__PURE__ */ e.jsxs("span", {
                style: {
                    fontSize: "10px",
                    color: (t == null ? void 0 : t.mode) === "dark" ? "#9ca3af" : "#6b7280",
                    marginLeft: "8px"
                },
                title: m,
                children: [
                    "(",
                    c,
                    ")"
                ]
            })
        ]
    }) : o === "question" || o === "statement" ? /* @__PURE__ */ e.jsx("div", {
        className: N("admesh-component admesh-expandable-variation transition-all duration-300", x ? "p-4 sm:p-5 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-slate-800 dark:to-slate-900 border border-gray-200/50 dark:border-slate-700/50 shadow-lg" : "p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow-md"),
        children: x ? // Expanded full card layout (same as default variation)
        /* @__PURE__ */ e.jsxs("div", {
            className: "h-full flex flex-col",
            style: b,
            "data-admesh-theme": t == null ? void 0 : t.mode,
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-col sm:flex-row sm:items-center gap-2 flex-1 min-w-0",
                            children: [
                                n && h.includes("Top Match") && /* @__PURE__ */ e.jsx("span", {
                                    className: "text-xs font-semibold text-white px-3 py-1 rounded-full w-fit shadow-md",
                                    style: {
                                        backgroundColor: (t == null ? void 0 : t.primaryColor) || (t == null ? void 0 : t.accentColor) || "#f59e0b",
                                        borderRadius: (t == null ? void 0 : t.borderRadius) || "9999px"
                                    },
                                    title: se(r),
                                    children: Te("Top Match")
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-2 min-w-0",
                                    children: [
                                        r.product_logo && /* @__PURE__ */ e.jsx("img", {
                                            src: r.product_logo.url,
                                            alt: `${r.title} logo`,
                                            className: "w-6 h-6 rounded flex-shrink-0",
                                            onError: (w)=>{
                                                w.target.style.display = "none";
                                            }
                                        }),
                                        /* @__PURE__ */ e.jsx("h4", {
                                            className: "font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate",
                                            children: u.title
                                        })
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex gap-3 flex-shrink-0",
                            children: [
                                /* @__PURE__ */ e.jsxs("button", {
                                    onClick: ()=>p(!1),
                                    className: "flex items-center gap-2 px-3 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600",
                                    title: "Show less details",
                                    children: [
                                        /* @__PURE__ */ e.jsx("span", {
                                            children: "Less Details"
                                        }),
                                        /* @__PURE__ */ e.jsx("svg", {
                                            className: "h-4 w-4",
                                            fill: "none",
                                            stroke: "currentColor",
                                            viewBox: "0 0 24 24",
                                            children: /* @__PURE__ */ e.jsx("path", {
                                                strokeLinecap: "round",
                                                strokeLinejoin: "round",
                                                strokeWidth: 2,
                                                d: "M20 12H4"
                                            })
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsx(U, {
                                    adId: r.ad_id,
                                    admeshLink: r.admesh_link,
                                    productId: r.product_id,
                                    onClick: ()=>a == null ? void 0 : a(r.ad_id, r.admesh_link),
                                    trackingData: {
                                        title: r.title,
                                        matchScore: r.intent_match_score,
                                        component: "product_card_cta"
                                    },
                                    children: /* @__PURE__ */ e.jsxs("button", {
                                        className: "text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg",
                                        children: [
                                            o === "question" ? "Try" : "Visit",
                                            " ",
                                            u.ctaText,
                                            /* @__PURE__ */ e.jsx("svg", {
                                                className: "ml-1 h-3 w-3",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                                })
                                            })
                                        ]
                                    })
                                })
                            ]
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "mb-6",
                    children: /* @__PURE__ */ e.jsx("p", {
                        className: "text-sm text-gray-600 dark:text-gray-300 leading-relaxed",
                        children: u.description
                    })
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "mb-6",
                    children: /* @__PURE__ */ e.jsx("p", {
                        className: "text-xs text-gray-500 dark:text-gray-400 leading-relaxed",
                        title: m,
                        children: c
                    })
                }),
                s && typeof r.intent_match_score == "number" && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2",
                            children: [
                                /* @__PURE__ */ e.jsx("span", {
                                    className: "font-medium",
                                    children: "Match Score"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap",
                                    children: [
                                        d,
                                        "% match"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden",
                            children: /* @__PURE__ */ e.jsx("div", {
                                className: "bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out",
                                style: {
                                    width: `${d}%`
                                }
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-wrap gap-2 text-xs mb-3",
                    children: [
                        r.pricing && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                    })
                                }),
                                r.pricing
                            ]
                        }),
                        r.trial_days && r.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"
                                    })
                                }),
                                r.trial_days,
                                "-day trial"
                            ]
                        })
                    ]
                }),
                r.features && r.features.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-3",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",
                            children: "✨ Key Features"
                        }),
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-wrap gap-1.5",
                            children: [
                                r.features.slice(0, 4).map((w, F)=>/* @__PURE__ */ e.jsxs("span", {
                                        className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700",
                                        children: [
                                            /* @__PURE__ */ e.jsx("svg", {
                                                className: "h-3 w-3 mr-0.5 inline text-indigo-500",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                })
                                            }),
                                            w
                                        ]
                                    }, F)),
                                r.features.length > 4 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1",
                                    children: [
                                        "+",
                                        r.features.length - 4,
                                        " more"
                                    ]
                                })
                            ]
                        })
                    ]
                }),
                r.integrations && r.integrations.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-3",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",
                            children: "🔗 Integrations"
                        }),
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-wrap gap-1.5",
                            children: [
                                r.integrations.slice(0, 3).map((w, F)=>/* @__PURE__ */ e.jsxs("span", {
                                        className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700",
                                        children: [
                                            /* @__PURE__ */ e.jsx("svg", {
                                                className: "h-3 w-3 mr-0.5 inline text-orange-500",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                                })
                                            }),
                                            w
                                        ]
                                    }, F)),
                                r.integrations.length > 3 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1",
                                    children: [
                                        "+",
                                        r.integrations.length - 3,
                                        " more"
                                    ]
                                })
                            ]
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex justify-end mt-auto pt-2",
                    children: /* @__PURE__ */ e.jsx("span", {
                        className: "text-xs text-gray-400 dark:text-gray-500",
                        children: "Powered by AdMesh"
                    })
                })
            ]
        }) : // Simple inline layout with top label
        /* @__PURE__ */ e.jsxs(e.Fragment, {
            children: [
                /* @__PURE__ */ e.jsx("div", {
                    className: "mb-2",
                    children: /* @__PURE__ */ e.jsx("span", {
                        style: {
                            fontSize: "11px",
                            fontWeight: "600",
                            color: (t == null ? void 0 : t.accentColor) || "#2563eb",
                            backgroundColor: (t == null ? void 0 : t.mode) === "dark" ? "#374151" : "#f3f4f6",
                            padding: "2px 6px",
                            borderRadius: "4px"
                        },
                        title: se(r, H(r)),
                        children: H(r)
                    })
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex items-center justify-between gap-3",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "flex-1 min-w-0",
                            children: /* @__PURE__ */ e.jsxs("p", {
                                className: "text-sm text-gray-700 dark:text-gray-300 leading-relaxed",
                                children: [
                                    u.description,
                                    " ",
                                    /* @__PURE__ */ e.jsx(U, {
                                        adId: r.ad_id,
                                        admeshLink: r.admesh_link,
                                        productId: r.product_id,
                                        onClick: ()=>a == null ? void 0 : a(r.ad_id, r.admesh_link),
                                        trackingData: {
                                            title: r.title,
                                            matchScore: r.intent_match_score,
                                            component: "simple_variation_cta"
                                        },
                                        children: /* @__PURE__ */ e.jsx("span", {
                                            className: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline cursor-pointer font-medium transition-colors",
                                            children: u.ctaText
                                        })
                                    })
                                ]
                            })
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "flex items-center gap-3 flex-shrink-0",
                            children: /* @__PURE__ */ e.jsxs("button", {
                                onClick: ()=>p(!0),
                                className: "flex items-center gap-2 px-3 py-2 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600",
                                title: "View more details",
                                children: [
                                    /* @__PURE__ */ e.jsx("span", {
                                        children: "More Details"
                                    }),
                                    /* @__PURE__ */ e.jsx("svg", {
                                        className: "h-4 w-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                        })
                                    })
                                ]
                            })
                        })
                    ]
                })
            ]
        })
    }) : /* @__PURE__ */ e.jsx("div", {
        className: v,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "h-full flex flex-col",
            style: b,
            "data-admesh-theme": t == null ? void 0 : t.mode,
            children: [
                /* @__PURE__ */ e.jsx("div", {
                    className: "mb-3",
                    children: /* @__PURE__ */ e.jsx("span", {
                        style: {
                            fontSize: "11px",
                            fontWeight: "600",
                            color: (t == null ? void 0 : t.accentColor) || "#2563eb",
                            backgroundColor: (t == null ? void 0 : t.mode) === "dark" ? "#374151" : "#f3f4f6",
                            padding: "2px 6px",
                            borderRadius: "4px"
                        },
                        title: se(r, H(r)),
                        children: H(r)
                    })
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 mb-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-2 flex-1 min-w-0",
                            children: [
                                r.product_logo && /* @__PURE__ */ e.jsx("img", {
                                    src: r.product_logo.url,
                                    alt: `${r.title} logo`,
                                    className: "w-6 h-6 rounded flex-shrink-0",
                                    onError: (w)=>{
                                        w.target.style.display = "none";
                                    }
                                }),
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "font-semibold text-gray-800 dark:text-gray-200 text-sm sm:text-base truncate",
                                    children: u.title
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "flex gap-2 flex-shrink-0",
                            children: /* @__PURE__ */ e.jsx(U, {
                                adId: r.ad_id,
                                admeshLink: r.admesh_link,
                                productId: r.product_id,
                                onClick: ()=>a == null ? void 0 : a(r.ad_id, r.admesh_link),
                                trackingData: {
                                    title: r.title,
                                    matchScore: r.intent_match_score,
                                    component: "product_card_cta"
                                },
                                children: /* @__PURE__ */ e.jsxs("button", {
                                    className: "text-xs sm:text-sm px-3 py-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 flex items-center transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg",
                                    children: [
                                        "Visit ",
                                        u.ctaText,
                                        /* @__PURE__ */ e.jsx("svg", {
                                            className: "ml-1 h-3 w-3",
                                            fill: "none",
                                            stroke: "currentColor",
                                            viewBox: "0 0 24 24",
                                            children: /* @__PURE__ */ e.jsx("path", {
                                                strokeLinecap: "round",
                                                strokeLinejoin: "round",
                                                strokeWidth: 2,
                                                d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                            })
                                        })
                                    ]
                                })
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "mb-6",
                    children: /* @__PURE__ */ e.jsx("p", {
                        className: "text-sm text-gray-600 dark:text-gray-300 leading-relaxed",
                        children: u.description
                    })
                }),
                s && typeof r.intent_match_score == "number" && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-6",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-300 mb-2",
                            children: [
                                /* @__PURE__ */ e.jsx("span", {
                                    className: "font-medium",
                                    children: "Match Score"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-blue-500 whitespace-nowrap",
                                    children: [
                                        d,
                                        "% match"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "w-full bg-gray-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden",
                            children: /* @__PURE__ */ e.jsx("div", {
                                className: "bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out",
                                style: {
                                    width: `${d}%`
                                }
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-wrap gap-2 text-xs mb-3",
                    children: [
                        r.pricing && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                    })
                                }),
                                r.pricing
                            ]
                        }),
                        r.trial_days && r.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", {
                            className: "flex items-center px-2 py-1 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "h-3 w-3 mr-1",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"
                                    })
                                }),
                                r.trial_days,
                                "-day trial"
                            ]
                        })
                    ]
                }),
                r.features && r.features.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-3",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",
                            children: "✨ Key Features"
                        }),
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-wrap gap-1.5",
                            children: [
                                r.features.slice(0, 4).map((w, F)=>/* @__PURE__ */ e.jsxs("span", {
                                        className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700",
                                        children: [
                                            /* @__PURE__ */ e.jsx("svg", {
                                                className: "h-3 w-3 mr-0.5 inline text-indigo-500",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                })
                                            }),
                                            w
                                        ]
                                    }, F)),
                                r.features.length > 4 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1",
                                    children: [
                                        "+",
                                        r.features.length - 4,
                                        " more"
                                    ]
                                })
                            ]
                        })
                    ]
                }),
                r.integrations && r.integrations.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                    className: "mb-3",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-600 dark:text-gray-300 mb-2 font-medium",
                            children: "🔗 Integrations"
                        }),
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-wrap gap-1.5",
                            children: [
                                r.integrations.slice(0, 3).map((w, F)=>/* @__PURE__ */ e.jsxs("span", {
                                        className: "text-xs px-2 py-1 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700",
                                        children: [
                                            /* @__PURE__ */ e.jsx("svg", {
                                                className: "h-3 w-3 mr-0.5 inline text-orange-500",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                                                })
                                            }),
                                            w
                                        ]
                                    }, F)),
                                r.integrations.length > 3 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs text-gray-500 dark:text-gray-400 px-2 py-1",
                                    children: [
                                        "+",
                                        r.integrations.length - 3,
                                        " more"
                                    ]
                                })
                            ]
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "mt-auto pt-3 border-t border-gray-100 dark:border-slate-700",
                    children: /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",
                        children: [
                            /* @__PURE__ */ e.jsx("span", {
                                title: m,
                                children: c
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                className: "text-gray-400 dark:text-gray-500",
                                children: "Powered by AdMesh"
                            })
                        ]
                    })
                })
            ]
        })
    });
};
ue.displayName = "AdMeshProductCard";
const Ae = ({ recommendations: r, theme: t, maxProducts: s = 3, showMatchScores: n = !0, showFeatures: o = !0, onProductClick: a, className: i })=>{
    const x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>r.slice(0, s), [
        r,
        s
    ]), p = N("admesh-component", "admesh-compare-layout", i), h = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return x.length === 0 ? /* @__PURE__ */ e.jsx("div", {
        className: p,
        children: /* @__PURE__ */ e.jsx("div", {
            className: "p-8 text-center text-gray-500 dark:text-gray-400",
            children: /* @__PURE__ */ e.jsx("p", {
                children: "No products to compare"
            })
        })
    }) : /* @__PURE__ */ e.jsx("div", {
        className: p,
        style: h,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "space-y-6",
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    className: "text-center",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-center gap-2 mb-2",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-5 h-5 text-gray-600 dark:text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    })
                                }),
                                /* @__PURE__ */ e.jsx("h3", {
                                    className: "text-lg font-semibold text-gray-800 dark:text-gray-200",
                                    children: "Smart Comparison"
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsxs("p", {
                            className: "text-sm text-gray-600 dark:text-gray-400",
                            children: [
                                x.length,
                                " intelligent matches found"
                            ]
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                    children: x.map((c, m)=>/* @__PURE__ */ e.jsxs("div", {
                            className: "relative p-4 rounded-lg bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 shadow-sm hover:shadow transition-shadow",
                            children: [
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex justify-between items-start mb-3",
                                    children: [
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                m === 0 && /* @__PURE__ */ e.jsx("span", {
                                                    className: "text-xs font-semibold text-white bg-black px-2 py-0.5 rounded-full",
                                                    children: "Top Match"
                                                }),
                                                /* @__PURE__ */ e.jsxs("span", {
                                                    className: "text-xs text-gray-400 dark:text-gray-500",
                                                    children: [
                                                        "#",
                                                        m + 1
                                                    ]
                                                })
                                            ]
                                        }),
                                        n && /* @__PURE__ */ e.jsxs("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap",
                                            children: [
                                                Math.round(c.intent_match_score * 100),
                                                "% match"
                                            ]
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "font-semibold text-gray-800 dark:text-gray-200 mb-2",
                                    children: c.title
                                }),
                                n && /* @__PURE__ */ e.jsxs("div", {
                                    className: "mb-3",
                                    children: [
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1",
                                            children: [
                                                /* @__PURE__ */ e.jsx("span", {
                                                    children: "Match Score"
                                                }),
                                                /* @__PURE__ */ e.jsxs("span", {
                                                    className: "whitespace-nowrap",
                                                    children: [
                                                        Math.round(c.intent_match_score * 100),
                                                        "% match"
                                                    ]
                                                })
                                            ]
                                        }),
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "w-full bg-gray-200 dark:bg-slate-600 rounded h-1.5 overflow-hidden",
                                            children: /* @__PURE__ */ e.jsx("div", {
                                                className: "bg-black h-1.5",
                                                style: {
                                                    width: `${Math.round(c.intent_match_score * 100)}%`
                                                }
                                            })
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex flex-wrap gap-2 text-xs mb-3",
                                    children: [
                                        c.pricing && /* @__PURE__ */ e.jsxs("span", {
                                            className: "flex items-center text-gray-600 dark:text-gray-400",
                                            children: [
                                                /* @__PURE__ */ e.jsx("svg", {
                                                    className: "h-3 w-3 mr-1",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /* @__PURE__ */ e.jsx("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                                    })
                                                }),
                                                c.pricing
                                            ]
                                        }),
                                        c.trial_days && c.trial_days > 0 && /* @__PURE__ */ e.jsxs("span", {
                                            className: "flex items-center text-gray-600 dark:text-gray-400",
                                            children: [
                                                /* @__PURE__ */ e.jsx("svg", {
                                                    className: "h-3 w-3 mr-1",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /* @__PURE__ */ e.jsx("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6 0h6"
                                                    })
                                                }),
                                                c.trial_days,
                                                "-day trial"
                                            ]
                                        })
                                    ]
                                }),
                                o && c.features && c.features.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                                    className: "mb-3",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400 mb-1",
                                            children: "Key Features:"
                                        }),
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "flex flex-wrap gap-1.5",
                                            children: [
                                                c.features.slice(0, 4).map((d, y)=>/* @__PURE__ */ e.jsxs("span", {
                                                        className: "text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-gray-300",
                                                        children: [
                                                            /* @__PURE__ */ e.jsx("svg", {
                                                                className: "h-3 w-3 mr-0.5 inline text-gray-500",
                                                                fill: "none",
                                                                stroke: "currentColor",
                                                                viewBox: "0 0 24 24",
                                                                children: /* @__PURE__ */ e.jsx("path", {
                                                                    strokeLinecap: "round",
                                                                    strokeLinejoin: "round",
                                                                    strokeWidth: 2,
                                                                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                                                })
                                                            }),
                                                            d
                                                        ]
                                                    }, y)),
                                                (c.features.length || 0) > 4 && /* @__PURE__ */ e.jsxs("span", {
                                                    className: "text-xs text-gray-500 dark:text-gray-400 italic",
                                                    children: [
                                                        "+",
                                                        c.features.length - 4,
                                                        " more"
                                                    ]
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsx(U, {
                                    adId: c.ad_id,
                                    admeshLink: c.admesh_link,
                                    productId: c.product_id,
                                    onClick: ()=>a == null ? void 0 : a(c.ad_id, c.admesh_link),
                                    trackingData: {
                                        title: c.title,
                                        matchScore: c.intent_match_score,
                                        component: "compare_table_cta"
                                    },
                                    children: /* @__PURE__ */ e.jsxs("button", {
                                        className: "w-full text-xs px-3 py-2 rounded-lg bg-black text-white hover:bg-gray-800 flex items-center justify-center gap-1 mt-auto transition-colors",
                                        children: [
                                            "Visit Offer",
                                            /* @__PURE__ */ e.jsx("svg", {
                                                className: "h-3 w-3",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                                })
                                            })
                                        ]
                                    })
                                })
                            ]
                        }, c.product_id || m))
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex items-center justify-center mt-6 pt-4 border-t border-gray-200/50 dark:border-gray-700/50",
                    children: /* @__PURE__ */ e.jsxs("span", {
                        className: "flex items-center gap-1.5 text-xs text-gray-400 dark:text-gray-500",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-3 h-3 text-indigo-500",
                                fill: "currentColor",
                                viewBox: "0 0 20 20",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    fillRule: "evenodd",
                                    d: "M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",
                                    clipRule: "evenodd"
                                })
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                className: "font-medium",
                                children: "Powered by"
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                className: "font-semibold text-indigo-600 dark:text-indigo-400",
                                children: "AdMesh"
                            })
                        ]
                    })
                })
            ]
        })
    });
};
Ae.displayName = "AdMeshCompareTable";
const Re = {
    "Top Match": "primary",
    "Free Tier": "success",
    "AI Powered": "secondary",
    Popular: "warning",
    New: "primary",
    "Trial Available": "success"
}, Ee = {
    "Top Match": "★",
    "Free Tier": "◆",
    "AI Powered": "◉",
    Popular: "▲",
    New: "●",
    "Trial Available": "◈"
}, Ie = ({ type: r, variant: t, size: s = "md", className: n })=>{
    const o = t || Re[r] || "secondary", a = Ee[r], i = N("admesh-component", "admesh-badge", `admesh-badge--${o}`, `admesh-badge--${s}`, n);
    return /* @__PURE__ */ e.jsxs("span", {
        className: i,
        children: [
            a && /* @__PURE__ */ e.jsx("span", {
                className: "admesh-badge__icon",
                children: a
            }),
            /* @__PURE__ */ e.jsx("span", {
                className: "admesh-badge__text",
                children: r
            })
        ]
    });
};
Ie.displayName = "AdMeshBadge";
const Qe = ({ recommendation: r, theme: t = {
    mode: "light"
}, className: s = "", onClick: n, showPoweredBy: o = !0, initialExpanded: a = !1, sections: i, ctaText: x, collapsible: p = !0 })=>{
    var M, S, k, T, I, $, W, w, F, P;
    const [h, c] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(a), m = ()=>{
        n == null || n(r.ad_id, r.admesh_link);
    }, d = ()=>{
        p && c(!h);
    }, y = r.feature_sections || [], u = [
        {
            title: "Documentation",
            description: `Learn more about ${r.recommendation_title || r.title}. Start exploring the features and capabilities.`,
            icon: "◆"
        },
        {
            title: "Talk To An Expert",
            description: `Ready to learn more about ${r.recommendation_title || r.title}? Reach out to a platform specialist for personalized guidance.`,
            icon: "◉"
        },
        {
            title: `${r.recommendation_title || r.title} Features`,
            description: r.recommendation_description || r.description || `${r.recommendation_title || r.title} offers comprehensive solutions for your needs. Discover the full potential.`,
            icon: "▲"
        },
        {
            title: "How it Works",
            description: `Learn how to get started with ${r.recommendation_title || r.title}. Begin your journey today.`,
            icon: "●"
        }
    ], v = ce(r, !1), b = xe(), f = i || (y.length > 0 ? y : u), _ = x || `Try ${r.recommendation_title || r.title}`, g = {
        background: t.backgroundColor || (t.mode === "dark" ? "#1f2937" : "#ffffff"),
        surface: t.surfaceColor || (t.mode === "dark" ? "#374151" : "#f9fafb"),
        border: t.borderColor || (t.mode === "dark" ? "#4b5563" : "#e5e7eb"),
        text: t.textColor || (t.mode === "dark" ? "#f9fafb" : "#111827"),
        textSecondary: t.textSecondaryColor || (t.mode === "dark" ? "#9ca3af" : "#6b7280"),
        accent: t.accentColor || t.primaryColor || "#3b82f6",
        secondary: t.secondaryColor || "#10b981",
        // Remove excessive gradients, use clean solid colors or subtle gradients
        headerBg: ((M = t.gradients) == null ? void 0 : M.primary) || (t.mode === "dark" ? "#374151" : "#f8fafc"),
        sectionBg: ((S = t.gradients) == null ? void 0 : S.secondary) || (t.mode === "dark" ? "#4b5563" : "#ffffff")
    }, L = t.disableDefaultStyles ? {} : {
        fontFamily: t.fontFamily || '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        borderRadius: t.borderRadius || "12px",
        border: `1px solid ${g.border}`,
        background: g.background,
        overflow: "hidden",
        maxWidth: "420px",
        boxShadow: ((k = t.shadows) == null ? void 0 : k.medium) || (t.mode === "dark" ? "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)" : "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"),
        position: "relative",
        transition: "all 0.2s ease"
    };
    return /* @__PURE__ */ e.jsxs("div", {
        className: `admesh-expandable-unit ${s}`,
        style: {
            ...L,
            ...(T = t.components) == null ? void 0 : T.expandableUnit
        },
        "data-admesh-theme": t.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                style: {
                    background: g.headerBg,
                    padding: "20px",
                    borderBottom: h || !p ? `1px solid ${g.border}` : "none",
                    position: "relative",
                    transition: "all 0.2s ease"
                },
                children: /* @__PURE__ */ e.jsxs("div", {
                    style: {
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        gap: "16px"
                    },
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            style: {
                                display: "flex",
                                alignItems: "center",
                                gap: "16px",
                                flex: 1,
                                minWidth: 0
                            },
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    style: {
                                        width: "40px",
                                        height: "40px",
                                        borderRadius: t.borderRadius || "8px",
                                        background: g.accent,
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center",
                                        color: "white",
                                        fontSize: ((I = t.fontSize) == null ? void 0 : I.base) || "16px",
                                        fontWeight: "600",
                                        boxShadow: (($ = t.shadows) == null ? void 0 : $.small) || "0 2px 4px rgba(0, 0, 0, 0.1)",
                                        border: `1px solid ${g.border}`
                                    },
                                    children: (r.recommendation_title || r.title).charAt(0).toUpperCase()
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    style: {
                                        flex: 1,
                                        minWidth: 0
                                    },
                                    children: [
                                        /* @__PURE__ */ e.jsx("h3", {
                                            style: {
                                                margin: 0,
                                                fontSize: "18px",
                                                fontWeight: "600",
                                                color: g.text,
                                                lineHeight: "1.4",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                whiteSpace: "nowrap"
                                            },
                                            children: r.recommendation_title || r.title
                                        }),
                                        /* @__PURE__ */ e.jsxs("p", {
                                            style: {
                                                margin: "8px 0 0 0",
                                                fontSize: "13px",
                                                color: g.textSecondary,
                                                fontWeight: "400",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                whiteSpace: "nowrap"
                                            },
                                            title: b,
                                            children: [
                                                v,
                                                " • ",
                                                new URL(r.url || r.admesh_link).hostname
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsxs("div", {
                            style: {
                                display: "flex",
                                alignItems: "center",
                                gap: "12px"
                            },
                            children: [
                                !h && p && /* @__PURE__ */ e.jsx(U, {
                                    adId: r.ad_id,
                                    admeshLink: r.admesh_link,
                                    productId: r.product_id,
                                    onClick: m,
                                    trackingData: {
                                        title: r.recommendation_title || r.title,
                                        component: "expandable_unit",
                                        expanded: !1,
                                        location: "header"
                                    },
                                    children: /* @__PURE__ */ e.jsx("button", {
                                        style: {
                                            padding: (W = t.spacing) != null && W.small ? `${t.spacing.small} ${t.spacing.medium || "12px"}` : "6px 12px",
                                            backgroundColor: g.accent,
                                            color: "white",
                                            border: "none",
                                            borderRadius: t.borderRadius || "6px",
                                            fontSize: ((w = t.fontSize) == null ? void 0 : w.small) || "12px",
                                            fontWeight: "500",
                                            cursor: "pointer",
                                            transition: "all 0.2s ease",
                                            boxShadow: ((F = t.shadows) == null ? void 0 : F.small) || "0 1px 3px rgba(0, 0, 0, 0.1)",
                                            whiteSpace: "nowrap",
                                            ...(P = t.components) == null ? void 0 : P.button
                                        },
                                        onMouseOver: (C)=>{
                                            var E;
                                            t.disableDefaultStyles || (C.currentTarget.style.transform = "translateY(-1px)", C.currentTarget.style.boxShadow = ((E = t.shadows) == null ? void 0 : E.medium) || "0 2px 6px rgba(0, 0, 0, 0.15)");
                                        },
                                        onMouseOut: (C)=>{
                                            var E;
                                            t.disableDefaultStyles || (C.currentTarget.style.transform = "translateY(0)", C.currentTarget.style.boxShadow = ((E = t.shadows) == null ? void 0 : E.small) || "0 1px 3px rgba(0, 0, 0, 0.1)");
                                        },
                                        children: _
                                    })
                                }),
                                p && /* @__PURE__ */ e.jsxs("button", {
                                    onClick: d,
                                    style: {
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "8px",
                                        padding: "8px 12px",
                                        background: t.mode === "dark" ? "#374151" : "#f3f4f6",
                                        border: `1px solid ${t.mode === "dark" ? "#4b5563" : "#d1d5db"}`,
                                        borderRadius: "8px",
                                        cursor: "pointer",
                                        color: t.accentColor || "#2563eb",
                                        fontSize: "12px",
                                        fontWeight: "600",
                                        transition: "all 0.2s ease"
                                    },
                                    onMouseEnter: (C)=>{
                                        C.currentTarget.style.background = t.mode === "dark" ? "#4b5563" : "#e5e7eb", C.currentTarget.style.borderColor = t.accentColor || "#2563eb";
                                    },
                                    onMouseLeave: (C)=>{
                                        C.currentTarget.style.background = t.mode === "dark" ? "#374151" : "#f3f4f6", C.currentTarget.style.borderColor = t.mode === "dark" ? "#4b5563" : "#d1d5db";
                                    },
                                    "aria-label": h ? "Show less details" : "Show more details",
                                    children: [
                                        /* @__PURE__ */ e.jsx("span", {
                                            children: h ? "Less Details" : "More Details"
                                        }),
                                        /* @__PURE__ */ e.jsx("svg", {
                                            width: "16",
                                            height: "16",
                                            viewBox: "0 0 24 24",
                                            fill: "none",
                                            stroke: "currentColor",
                                            strokeWidth: "2",
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            children: h ? // Minus icon for collapse
                                            /* @__PURE__ */ e.jsx("path", {
                                                d: "M5 12h14"
                                            }) : // Info icon for expand
                                            /* @__PURE__ */ e.jsxs(e.Fragment, {
                                                children: [
                                                    /* @__PURE__ */ e.jsx("circle", {
                                                        cx: "12",
                                                        cy: "12",
                                                        r: "10"
                                                    }),
                                                    /* @__PURE__ */ e.jsx("path", {
                                                        d: "M12 16v-4"
                                                    }),
                                                    /* @__PURE__ */ e.jsx("path", {
                                                        d: "M12 8h.01"
                                                    })
                                                ]
                                            })
                                        })
                                    ]
                                })
                            ]
                        })
                    ]
                })
            }),
            (h || !p) && /* @__PURE__ */ e.jsxs("div", {
                style: {
                    padding: "0"
                },
                children: [
                    f.map((C, E)=>/* @__PURE__ */ e.jsxs("div", {
                            style: {
                                padding: "24px",
                                backgroundColor: E % 2 === 0 ? g.background : g.sectionBg,
                                borderBottom: E < f.length - 1 ? `1px solid ${g.border}` : "none"
                            },
                            children: [
                                /* @__PURE__ */ e.jsxs("h4", {
                                    style: {
                                        margin: "0 0 12px 0",
                                        fontSize: "15px",
                                        fontWeight: "600",
                                        color: g.text,
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "12px"
                                    },
                                    children: [
                                        C.icon && /* @__PURE__ */ e.jsx("span", {
                                            children: C.icon
                                        }),
                                        C.title
                                    ]
                                }),
                                /* @__PURE__ */ e.jsx("p", {
                                    style: {
                                        margin: 0,
                                        fontSize: "14px",
                                        color: g.textSecondary,
                                        lineHeight: "1.6"
                                    },
                                    children: C.description
                                })
                            ]
                        }, E)),
                    (h || !p) && /* @__PURE__ */ e.jsx("div", {
                        style: {
                            padding: "24px",
                            borderTop: `1px solid ${g.border}`,
                            backgroundColor: g.background
                        },
                        children: /* @__PURE__ */ e.jsx(U, {
                            adId: r.ad_id,
                            admeshLink: r.admesh_link,
                            productId: r.product_id,
                            onClick: m,
                            trackingData: {
                                title: r.title,
                                component: "expandable_unit",
                                expanded: h,
                                location: "footer"
                            },
                            children: /* @__PURE__ */ e.jsx("button", {
                                style: {
                                    width: "100%",
                                    padding: "14px 28px",
                                    background: g.accent,
                                    color: "white",
                                    border: "none",
                                    borderRadius: "12px",
                                    fontSize: "15px",
                                    fontWeight: "600",
                                    cursor: "pointer",
                                    transition: "all 0.3s ease",
                                    boxShadow: "0 4px 12px rgba(99, 102, 241, 0.3)",
                                    position: "relative",
                                    overflow: "hidden"
                                },
                                onMouseOver: (C)=>{
                                    C.currentTarget.style.transform = "translateY(-2px) scale(1.02)", C.currentTarget.style.boxShadow = "0 8px 20px rgba(99, 102, 241, 0.4)";
                                },
                                onMouseOut: (C)=>{
                                    C.currentTarget.style.transform = "translateY(0) scale(1)", C.currentTarget.style.boxShadow = "0 4px 12px rgba(99, 102, 241, 0.3)";
                                },
                                children: _
                            })
                        })
                    }),
                    o && /* @__PURE__ */ e.jsx("div", {
                        style: {
                            padding: "8px 16px",
                            borderTop: `1px solid ${g.border}`,
                            backgroundColor: g.headerBg
                        },
                        children: /* @__PURE__ */ e.jsxs("div", {
                            style: {
                                fontSize: "11px",
                                color: g.textSecondary,
                                textAlign: "center"
                            },
                            children: [
                                "powered by ",
                                /* @__PURE__ */ e.jsx("strong", {
                                    style: {
                                        color: g.text
                                    },
                                    children: "AdMesh"
                                })
                            ]
                        })
                    })
                ]
            })
        ]
    });
}, q = ({ recommendation: r, theme: t, compact: s = !1, showReason: n = !0, onClick: o, className: a })=>{
    const i = Math.round(r.intent_match_score * 100), x = ce(r, s), p = xe(), h = N("admesh-inline-recommendation", "group cursor-pointer transition-all duration-200", {
        "p-2 sm:p-3 rounded-md bg-gray-50 dark:bg-slate-800/50 hover:bg-gray-100 dark:hover:bg-slate-800 border border-gray-200 dark:border-slate-700": !s,
        "p-1.5 sm:p-2 rounded hover:bg-gray-50 dark:hover:bg-slate-800/30": s
    }, a), c = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsx(U, {
        adId: r.ad_id,
        admeshLink: r.admesh_link,
        productId: r.product_id,
        onClick: ()=>o == null ? void 0 : o(r.ad_id, r.admesh_link),
        trackingData: {
            title: r.title,
            matchScore: r.intent_match_score
        },
        className: h,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "flex items-start gap-3",
            style: c,
            "data-admesh-theme": t == null ? void 0 : t.mode,
            children: [
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex-shrink-0 mt-0.5",
                    children: r.offer_images && r.offer_images.length > 0 ? /* @__PURE__ */ e.jsx("div", {
                        className: "w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600",
                        children: /* @__PURE__ */ e.jsx("img", {
                            src: r.offer_images[0].url,
                            alt: r.recommendation_title || r.title,
                            className: "w-full h-full object-cover"
                        })
                    }) : r.product_logo ? /* @__PURE__ */ e.jsx("div", {
                        className: "w-6 h-6 rounded-full overflow-hidden border border-gray-200 dark:border-gray-600",
                        children: /* @__PURE__ */ e.jsx("img", {
                            src: r.product_logo.url,
                            alt: r.recommendation_title || r.title,
                            className: "w-full h-full object-cover"
                        })
                    }) : r.intent_match_score >= 0.8 ? /* @__PURE__ */ e.jsx("div", {
                        className: "w-2 h-2 bg-green-500 rounded-full"
                    }) : /* @__PURE__ */ e.jsx("div", {
                        className: "w-2 h-2 bg-blue-500 rounded-full"
                    })
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex-1 min-w-0",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-start sm:items-center gap-2 mb-1 flex-col sm:flex-row",
                            children: [
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: N("font-medium transition-colors duration-200 flex-shrink-0", "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300", "cursor-pointer hover:underline", s ? "text-sm sm:text-base" : "text-base sm:text-lg"),
                                    children: r.recommendation_title || r.title
                                }),
                                r.intent_match_score >= 0.7 && /* @__PURE__ */ e.jsxs("span", {
                                    className: N("inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium flex-shrink-0 whitespace-nowrap", r.intent_match_score >= 0.8 ? "bg-green-100 text-green-800 dark:bg-green-800/80 dark:text-green-100" : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"),
                                    children: [
                                        i,
                                        "% match"
                                    ]
                                })
                            ]
                        }),
                        n && (r.recommendation_description || r.reason) && /* @__PURE__ */ e.jsx("p", {
                            className: N("text-gray-600 dark:text-gray-400 line-clamp-2", s ? "text-xs" : "text-sm"),
                            children: r.recommendation_description || r.reason
                        }),
                        /* @__PURE__ */ e.jsx("p", {
                            className: N("text-gray-500 dark:text-gray-400 mt-1", "text-xs"),
                            title: p,
                            children: x
                        }),
                        !s && r.keywords && r.keywords.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                            className: "flex flex-wrap gap-1 mt-2",
                            children: [
                                r.keywords.slice(0, 3).map((m, d)=>/* @__PURE__ */ e.jsx("span", {
                                        className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-slate-700 dark:text-gray-300",
                                        children: m
                                    }, d)),
                                r.keywords.length > 3 && /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-xs text-gray-500 dark:text-gray-400",
                                    children: [
                                        "+",
                                        r.keywords.length - 3,
                                        " more"
                                    ]
                                })
                            ]
                        }),
                        !s && r.trial_days && r.trial_days > 0 && /* @__PURE__ */ e.jsx("div", {
                            className: "flex items-center gap-2 mt-2",
                            children: /* @__PURE__ */ e.jsxs("span", {
                                className: "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                                children: [
                                    r.trial_days,
                                    "-day trial"
                                ]
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity",
                    children: /* @__PURE__ */ e.jsx("svg", {
                        className: "w-4 h-4 text-gray-400 dark:text-gray-500",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /* @__PURE__ */ e.jsx("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M9 5l7 7-7 7"
                        })
                    })
                })
            ]
        })
    });
}, Pe = ({ recommendations: r, conversationSummary: t, theme: s, showTopRecommendations: n = 3, onRecommendationClick: o, onStartNewConversation: a, className: i })=>{
    const x = r.sort((c, m)=>m.intent_match_score - c.intent_match_score).slice(0, n), p = N("admesh-conversation-summary", "bg-white dark:bg-black", "rounded-lg border border-gray-200 dark:border-gray-800 p-4 sm:p-6", "font-sans", // Standardize font family
    i), h = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: p,
        style: h,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex items-center gap-3 mb-4",
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: "flex-shrink-0",
                        children: /* @__PURE__ */ e.jsx("div", {
                            className: "w-8 h-8 sm:w-10 sm:h-10 bg-black dark:bg-white rounded-full flex items-center justify-center",
                            children: /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4 sm:w-5 sm:h-5 text-white dark:text-black",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                })
                            })
                        })
                    }),
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "min-w-0 flex-1",
                        children: [
                            /* @__PURE__ */ e.jsx("h3", {
                                className: "text-base sm:text-lg font-semibold text-black dark:text-white",
                                children: "Conversation Summary"
                            }),
                            /* @__PURE__ */ e.jsx("p", {
                                className: "text-xs sm:text-sm text-gray-600 dark:text-gray-300",
                                children: "Here's what we discussed and found for you"
                            })
                        ]
                    })
                ]
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "mb-6",
                children: /* @__PURE__ */ e.jsx("div", {
                    className: "bg-gray-50 dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700",
                    children: /* @__PURE__ */ e.jsx("p", {
                        className: "text-gray-800 dark:text-gray-200 leading-relaxed",
                        children: t
                    })
                })
            }),
            x.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                className: "mb-6",
                children: [
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-2 mb-3",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-5 h-5 text-black dark:text-white",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                })
                            }),
                            /* @__PURE__ */ e.jsx("h4", {
                                className: "font-medium text-black dark:text-white",
                                children: "Top Recommendations"
                            })
                        ]
                    }),
                    /* @__PURE__ */ e.jsx("div", {
                        className: "space-y-2",
                        children: x.map((c, m)=>/* @__PURE__ */ e.jsxs("div", {
                                className: "relative",
                                children: [
                                    /* @__PURE__ */ e.jsx("div", {
                                        className: "absolute -left-2 top-2 z-10",
                                        children: /* @__PURE__ */ e.jsx("div", {
                                            className: N("w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold", m === 0 ? "bg-black dark:bg-white text-white dark:text-black" : m === 1 ? "bg-gray-600 dark:bg-gray-400 text-white dark:text-black" : "bg-gray-800 dark:bg-gray-200 text-white dark:text-black"),
                                            children: m + 1
                                        })
                                    }),
                                    /* @__PURE__ */ e.jsx("div", {
                                        className: "ml-4",
                                        children: /* @__PURE__ */ e.jsx(q, {
                                            recommendation: c,
                                            theme: s,
                                            compact: !0,
                                            showReason: !0,
                                            onClick: o
                                        })
                                    })
                                ]
                            }, c.ad_id || m))
                    })
                ]
            }),
            r.length > n && /* @__PURE__ */ e.jsx("div", {
                className: "mb-6",
                children: /* @__PURE__ */ e.jsx("div", {
                    className: "bg-gray-100 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700",
                    children: /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4 text-black dark:text-white",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                })
                            }),
                            /* @__PURE__ */ e.jsxs("span", {
                                className: "text-sm font-medium text-gray-800 dark:text-gray-200",
                                children: [
                                    r.length - n,
                                    " additional recommendation",
                                    r.length - n > 1 ? "s" : "",
                                    " available"
                                ]
                            })
                        ]
                    })
                })
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex flex-col sm:flex-row gap-3",
                children: [
                    a && /* @__PURE__ */ e.jsxs("button", {
                        onClick: a,
                        className: "flex-1 bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                })
                            }),
                            "Start New Conversation"
                        ]
                    }),
                    /* @__PURE__ */ e.jsxs("button", {
                        onClick: ()=>{
                            x.length > 0 && (o == null || o(x[0].ad_id, x[0].admesh_link));
                        },
                        className: "flex-1 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 text-black dark:text-white font-medium py-2.5 px-4 rounded-lg border border-gray-300 dark:border-gray-600 transition-all duration-200 flex items-center justify-center gap-2",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                                })
                            }),
                            "View Top Pick"
                        ]
                    })
                ]
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "flex justify-center mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",
                children: /* @__PURE__ */ e.jsx("span", {
                    className: "text-xs text-gray-500 dark:text-gray-400",
                    children: "Powered by AdMesh"
                })
            })
        ]
    });
}, be = ({ recommendation: r, citationNumber: t, citationStyle: s = "numbered", theme: n, showTooltip: o = !0, onClick: a, onHover: i, className: x })=>{
    const [p, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), c = ()=>{
        h(!0), i == null || i(r);
    }, m = ()=>{
        h(!1);
    }, d = ()=>{
        a == null || a(r.ad_id, r.admesh_link);
    }, y = ()=>{
        switch(s){
            case "bracketed":
                return `[${t}]`;
            case "superscript":
                return t.toString();
            case "numbered":
            default:
                return t.toString();
        }
    }, u = N("admesh-citation-reference", "inline-flex items-center justify-center", "cursor-pointer transition-all duration-200", "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300", "font-medium", {
        // Numbered style (default)
        "w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full text-xs border border-blue-300 dark:border-blue-700 hover:bg-blue-200 dark:hover:bg-blue-900/50": s === "numbered",
        // Bracketed style
        "px-1 text-sm hover:underline": s === "bracketed",
        // Superscript style
        "text-xs align-super hover:underline": s === "superscript"
    }, x), v = n != null && n.accentColor ? {
        "--admesh-primary": n.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("span", {
        className: "relative inline-block",
        children: [
            /* @__PURE__ */ e.jsx(U, {
                adId: r.ad_id,
                admeshLink: r.admesh_link,
                productId: r.product_id,
                onClick: d,
                trackingData: {
                    title: r.title,
                    matchScore: r.intent_match_score,
                    citationNumber: t,
                    citationStyle: s
                },
                className: u,
                children: /* @__PURE__ */ e.jsx("span", {
                    style: v,
                    "data-admesh-theme": n == null ? void 0 : n.mode,
                    onMouseEnter: c,
                    onMouseLeave: m,
                    children: y()
                })
            }),
            o && p && /* @__PURE__ */ e.jsx("div", {
                className: "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50",
                children: /* @__PURE__ */ e.jsxs("div", {
                    className: "bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded-lg px-3 py-2 shadow-lg max-w-xs",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "font-semibold mb-1",
                            children: r.title
                        }),
                        r.reason && /* @__PURE__ */ e.jsx("div", {
                            className: "text-gray-300 dark:text-gray-600 text-xs",
                            children: r.reason.length > 100 ? `${r.reason.substring(0, 100)}...` : r.reason
                        }),
                        r.intent_match_score >= 0.7 && /* @__PURE__ */ e.jsxs("div", {
                            className: "text-green-400 dark:text-green-600 text-xs mt-1",
                            children: [
                                Math.round(r.intent_match_score * 100),
                                "% match"
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "text-gray-400 dark:text-gray-500 text-xs mt-1 italic",
                            children: "Click to visit product page"
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-100"
                        })
                    ]
                })
            })
        ]
    });
}, ze = ({ recommendations: r, conversationText: t, theme: s, showCitationList: n = !0, citationStyle: o = "numbered", onRecommendationClick: a, onCitationHover: i, className: x })=>{
    const [p, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null), c = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!t || r.length === 0) return {
            text: t,
            citationMap: /* @__PURE__ */ new Map()
        };
        let u = t;
        const v = /* @__PURE__ */ new Map();
        return [
            ...r
        ].sort((f, _)=>_.intent_match_score - f.intent_match_score).forEach((f, _)=>{
            const g = _ + 1, L = f.title;
            v.set(g, f);
            const M = new RegExp(`\\b${L.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi");
            if (M.test(u)) u = u.replace(M, (S)=>`${S}{{CITATION_${g}}}`);
            else {
                const S = f.keywords || [];
                let k = !1;
                for (const T of S){
                    const I = new RegExp(`\\b${T.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`, "gi");
                    if (I.test(u) && !k) {
                        u = u.replace(I, ($)=>(k = !0, `${$}{{CITATION_${g}}}`));
                        break;
                    }
                }
                k || (u += `{{CITATION_${g}}}`);
            }
        }), {
            text: u,
            citationMap: v
        };
    }, [
        t,
        r
    ]), m = ()=>{
        const { text: u, citationMap: v } = c;
        return u.split(/(\{\{CITATION_\d+\}\})/).map((f, _)=>{
            const g = f.match(/\{\{CITATION_(\d+)\}\}/);
            if (g) {
                const L = parseInt(g[1]), M = v.get(L);
                if (M) return /* @__PURE__ */ e.jsx(be, {
                    recommendation: M,
                    citationNumber: L,
                    citationStyle: o,
                    theme: s,
                    showTooltip: !0,
                    onClick: a,
                    onHover: (S)=>{
                        h(S), i == null || i(S);
                    }
                }, `citation-${L}-${_}`);
            }
            return /* @__PURE__ */ e.jsx("span", {
                children: f
            }, _);
        });
    }, d = N("admesh-citation-unit", "space-y-4", x), y = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: d,
        style: y,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                className: "admesh-citation-text text-gray-800 dark:text-gray-200 leading-relaxed",
                children: m()
            }),
            n && r.length > 0 && /* @__PURE__ */ e.jsx("div", {
                className: "admesh-citation-list",
                children: /* @__PURE__ */ e.jsxs("div", {
                    className: "border-t border-gray-200 dark:border-slate-700 pt-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("h4", {
                            className: "text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2",
                            children: [
                                /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-4 h-4",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                    })
                                }),
                                "References"
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "space-y-2",
                            children: r.sort((u, v)=>v.intent_match_score - u.intent_match_score).map((u, v)=>/* @__PURE__ */ e.jsxs("div", {
                                    className: N("flex items-start gap-3 p-2 rounded-lg transition-colors duration-200", {
                                        "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800": (p == null ? void 0 : p.ad_id) === u.ad_id,
                                        "hover:bg-gray-50 dark:hover:bg-slate-800/50": (p == null ? void 0 : p.ad_id) !== u.ad_id
                                    }),
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "flex-shrink-0 mt-1",
                                            children: /* @__PURE__ */ e.jsx(be, {
                                                recommendation: u,
                                                citationNumber: v + 1,
                                                citationStyle: o,
                                                theme: s,
                                                showTooltip: !1,
                                                onClick: a
                                            })
                                        }),
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "flex-1 min-w-0",
                                            children: /* @__PURE__ */ e.jsx(q, {
                                                recommendation: u,
                                                theme: s,
                                                compact: !0,
                                                showReason: !1,
                                                onClick: a
                                            })
                                        })
                                    ]
                                }, u.ad_id || v))
                        })
                    ]
                })
            })
        ]
    });
}, ye = ({ recommendations: r, config: t, theme: s, conversationSummary: n, sessionId: o, onRecommendationClick: a, onDismiss: i, className: x })=>{
    const [p, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(t.autoShow !== !1), [c, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (t.delayMs && t.delayMs > 0) {
            const g = setTimeout(()=>{
                h(!0), m(!0);
            }, t.delayMs);
            return ()=>clearTimeout(g);
        } else m(!0);
    }, [
        t.delayMs
    ]), !p || r.length === 0) return null;
    const d = t.maxRecommendations || 3, y = r.slice(0, d), u = (g, L)=>{
        a == null || a(g, L);
    }, v = ()=>{
        h(!1), i == null || i();
    }, b = ()=>{
        switch(t.displayMode){
            case "summary":
                return n ? /* @__PURE__ */ e.jsx(Pe, {
                    recommendations: y,
                    conversationSummary: n,
                    theme: s,
                    showTopRecommendations: d,
                    onRecommendationClick: u,
                    onStartNewConversation: i
                }) : null;
            case "inline":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-2",
                    children: y.map((g, L)=>/* @__PURE__ */ e.jsx(q, {
                            recommendation: g,
                            theme: s,
                            compact: !0,
                            showReason: !0,
                            onClick: u
                        }, g.ad_id || L))
                });
            case "minimal":
                return y.length > 0 ? /* @__PURE__ */ e.jsxs("div", {
                    className: "admesh-minimal-unit",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-2 mb-2",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-2 h-2 bg-blue-500 rounded-full"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                    children: [
                                        y.length,
                                        " intelligent match",
                                        y.length > 1 ? "es" : "",
                                        " found"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx(q, {
                            recommendation: y[0],
                            theme: s,
                            compact: !0,
                            showReason: !1,
                            onClick: u
                        }),
                        y.length > 1 && /* @__PURE__ */ e.jsxs("div", {
                            className: "text-xs text-gray-500 dark:text-gray-400 mt-1",
                            children: [
                                "+",
                                y.length - 1,
                                " more recommendation",
                                y.length > 2 ? "s" : ""
                            ]
                        })
                    ]
                }) : null;
            case "citation":
                return n ? /* @__PURE__ */ e.jsx(ze, {
                    recommendations: y,
                    conversationText: n,
                    theme: s,
                    showCitationList: !0,
                    citationStyle: "numbered",
                    onRecommendationClick: u
                }) : null;
            case "floating":
                return /* @__PURE__ */ e.jsxs("div", {
                    className: "admesh-floating-unit bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 p-4",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex justify-between items-start mb-3",
                            children: [
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "w-2 h-2 bg-blue-500 rounded-full"
                                        }),
                                        /* @__PURE__ */ e.jsx("span", {
                                            className: "text-sm font-semibold text-gray-800 dark:text-gray-200",
                                            children: "Recommended for you"
                                        })
                                    ]
                                }),
                                i && /* @__PURE__ */ e.jsx("button", {
                                    onClick: v,
                                    className: "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",
                                    "aria-label": "Dismiss recommendations",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M6 18L18 6M6 6l12 12"
                                        })
                                    })
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            className: "space-y-2",
                            children: y.map((g, L)=>/* @__PURE__ */ e.jsx(q, {
                                    recommendation: g,
                                    theme: s,
                                    compact: !0,
                                    showReason: !1,
                                    onClick: u
                                }, g.ad_id || L))
                        })
                    ]
                });
            default:
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: y.map((g, L)=>/* @__PURE__ */ e.jsx(ue, {
                            recommendation: g,
                            theme: s,
                            showMatchScore: !1,
                            showBadges: !0,
                            onClick: u
                        }, g.ad_id || L))
                });
        }
    }, f = N("admesh-conversational-unit", "transition-all duration-300 ease-in-out", {
        "opacity-0 translate-y-2": !c,
        "opacity-100 translate-y-0": c,
        "fixed bottom-4 right-4 max-w-sm z-50": t.displayMode === "floating",
        "my-3": t.displayMode === "inline",
        "mt-4 pt-4 border-t border-gray-200 dark:border-slate-700": t.displayMode === "summary"
    }, x), _ = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: f,
        style: _,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        "data-admesh-context": t.context,
        "data-session-id": o,
        children: [
            b(),
            t.showPoweredBy !== !1 && /* @__PURE__ */ e.jsx("div", {
                className: "flex justify-end mt-2",
                children: /* @__PURE__ */ e.jsx("span", {
                    className: "text-xs text-gray-400 dark:text-gray-500",
                    children: "Powered by AdMesh"
                })
            })
        ]
    });
}, We = ({ message: r, theme: t, onRecommendationClick: s, className: n })=>{
    const o = r.role === "user", a = r.role === "assistant", i = N("admesh-chat-message", "flex items-start gap-3", {
        "flex-row-reverse": o
    }, n), x = N("max-w-xs lg:max-w-sm px-4 py-3 rounded-lg text-sm", {
        "bg-gradient-to-r from-blue-600 to-indigo-600 text-white": o,
        "bg-gray-100 dark:bg-slate-800 text-gray-900 dark:text-gray-100": a,
        "bg-yellow-100 dark:bg-yellow-900 text-yellow-900 dark:text-yellow-100": r.role === "system"
    }), p = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0, h = (c)=>c.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit"
        });
    return /* @__PURE__ */ e.jsxs("div", {
        className: i,
        style: p,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            !o && /* @__PURE__ */ e.jsx("div", {
                className: "w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",
                children: /* @__PURE__ */ e.jsx("svg", {
                    className: "w-4 h-4 text-white",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ e.jsx("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M13 10V3L4 14h7v7l9-11h-7z"
                    })
                })
            }),
            o && /* @__PURE__ */ e.jsx("div", {
                className: "w-8 h-8 bg-gray-300 dark:bg-slate-600 rounded-full flex items-center justify-center flex-shrink-0",
                children: /* @__PURE__ */ e.jsx("svg", {
                    className: "w-4 h-4 text-gray-600 dark:text-gray-300",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    children: /* @__PURE__ */ e.jsx("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    })
                })
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: `flex flex-col ${o ? "items-end" : "items-start"} flex-1`,
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: x,
                        children: /* @__PURE__ */ e.jsx("div", {
                            className: "whitespace-pre-wrap break-words",
                            children: r.content
                        })
                    }),
                    /* @__PURE__ */ e.jsx("div", {
                        className: N("text-xs text-gray-500 dark:text-gray-400 mt-1", {
                            "text-right": o
                        }),
                        children: h(r.timestamp)
                    }),
                    r.recommendations && r.recommendations.length > 0 && /* @__PURE__ */ e.jsxs("div", {
                        className: "mt-3 w-full max-w-lg",
                        children: [
                            /* @__PURE__ */ e.jsxs("div", {
                                className: "flex items-center gap-2 mb-3",
                                children: [
                                    /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4 text-blue-600 dark:text-blue-400",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 10V3L4 14h7v7l9-11h-7z"
                                        })
                                    }),
                                    /* @__PURE__ */ e.jsxs("span", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                        children: [
                                            r.recommendations.length,
                                            " recommendation",
                                            r.recommendations.length > 1 ? "s" : "",
                                            " found"
                                        ]
                                    })
                                ]
                            }),
                            /* @__PURE__ */ e.jsx(ye, {
                                recommendations: r.recommendations,
                                config: {
                                    displayMode: "inline",
                                    context: "chat",
                                    maxRecommendations: 3,
                                    showPoweredBy: !1,
                                    autoShow: !0,
                                    delayMs: 300
                                },
                                theme: t,
                                onRecommendationClick: s,
                                className: "bg-gray-50 dark:bg-slate-800/50 rounded-lg p-3 border border-gray-200 dark:border-slate-700"
                            })
                        ]
                    })
                ]
            })
        ]
    });
}, Be = ({ placeholder: r = "Type your message...", disabled: t = !1, suggestions: s = [], theme: n, onSendMessage: o, className: a })=>{
    const [i, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(""), [p, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), [c, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]), d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), y = (M)=>{
        const S = M.target.value;
        if (x(S), S.trim() && s.length > 0) {
            const k = s.filter((T)=>T.toLowerCase().includes(S.toLowerCase()));
            m(k), h(k.length > 0);
        } else h(!1);
        d.current && (d.current.style.height = "auto", d.current.style.height = `${Math.min(d.current.scrollHeight, 120)}px`);
    }, u = (M)=>{
        M.key === "Enter" && !M.shiftKey && (M.preventDefault(), v());
    }, v = ()=>{
        const M = i.trim();
        M && !t && o && (o(M), x(""), h(!1), d.current && (d.current.style.height = "auto"));
    }, b = (M)=>{
        x(M), h(!1), d.current && d.current.focus();
    }, f = N("admesh-chat-input", "relative", a), _ = N("w-full resize-none rounded-lg border border-gray-300 dark:border-slate-600", "bg-white dark:bg-slate-800 text-gray-900 dark:text-gray-100", "placeholder-gray-500 dark:placeholder-gray-400", "focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent", "transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600", "pr-12 pl-4 py-3 text-sm leading-5", {
        "opacity-50 cursor-not-allowed": t
    }), g = N("absolute right-2 bottom-2 p-2 rounded-lg transition-all duration-200", "flex items-center justify-center", {
        "bg-blue-600 hover:bg-blue-700 text-white": i.trim() && !t,
        "bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-gray-500 cursor-not-allowed": !i.trim() || t
    }), L = n != null && n.accentColor ? {
        "--admesh-primary": n.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: f,
        style: L,
        "data-admesh-theme": n == null ? void 0 : n.mode,
        children: [
            p && c.length > 0 && /* @__PURE__ */ e.jsx("div", {
                className: "absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-lg shadow-lg max-h-40 overflow-y-auto z-10",
                children: c.slice(0, 5).map((M, S)=>/* @__PURE__ */ e.jsx("button", {
                        onClick: ()=>b(M),
                        className: "w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors first:rounded-t-lg last:rounded-b-lg",
                        children: M
                    }, S))
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: "relative",
                children: [
                    /* @__PURE__ */ e.jsx("textarea", {
                        ref: d,
                        value: i,
                        onChange: y,
                        onKeyDown: u,
                        placeholder: r,
                        disabled: t,
                        rows: 1,
                        className: _,
                        style: {
                            minHeight: "44px",
                            maxHeight: "120px"
                        }
                    }),
                    /* @__PURE__ */ e.jsx("button", {
                        onClick: v,
                        disabled: !i.trim() || t,
                        className: g,
                        "aria-label": "Send message",
                        children: /* @__PURE__ */ e.jsx("svg", {
                            className: "w-4 h-4",
                            fill: "none",
                            stroke: "currentColor",
                            viewBox: "0 0 24 24",
                            children: /* @__PURE__ */ e.jsx("path", {
                                strokeLinecap: "round",
                                strokeLinejoin: "round",
                                strokeWidth: 2,
                                d: "M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                            })
                        })
                    })
                ]
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400",
                children: [
                    /* @__PURE__ */ e.jsx("span", {
                        children: "Press Enter to send, Shift+Enter for new line"
                    }),
                    /* @__PURE__ */ e.jsxs("span", {
                        className: N("transition-opacity duration-200", {
                            "opacity-0": i.length < 100
                        }),
                        children: [
                            i.length,
                            "/500"
                        ]
                    })
                ]
            })
        ]
    });
}, Oe = ({ messages: r, config: t, theme: s, isLoading: n = !1, onSendMessage: o, onRecommendationClick: a, className: i })=>{
    const x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        x.current && x.current.scrollIntoView({
            behavior: "smooth"
        });
    }, [
        r
    ]);
    const h = N("admesh-chat-interface", "flex flex-col h-full bg-white dark:bg-slate-900", i), c = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0, m = t.maxMessages ? r.slice(-t.maxMessages) : r;
    return /* @__PURE__ */ e.jsxs("div", {
        className: h,
        style: c,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                ref: p,
                className: "flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",
                children: m.length === 0 ? /* @__PURE__ */ e.jsxs("div", {
                    className: "flex flex-col items-center justify-center h-full text-center",
                    children: [
                        /* @__PURE__ */ e.jsx("div", {
                            className: "w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900 dark:to-indigo-900 rounded-full flex items-center justify-center mb-4",
                            children: /* @__PURE__ */ e.jsx("svg", {
                                className: "w-8 h-8 text-blue-600 dark:text-blue-400",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                })
                            })
                        }),
                        /* @__PURE__ */ e.jsx("h3", {
                            className: "text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",
                            children: "Welcome to AdMesh AI"
                        }),
                        /* @__PURE__ */ e.jsx("p", {
                            className: "text-sm text-gray-600 dark:text-gray-400 max-w-xs",
                            children: "Ask me anything about products, tools, or services. I'll provide personalized recommendations just for you!"
                        })
                    ]
                }) : /* @__PURE__ */ e.jsxs(e.Fragment, {
                    children: [
                        m.map((d)=>/* @__PURE__ */ e.jsx(We, {
                                message: d,
                                theme: s,
                                onRecommendationClick: a
                            }, d.id)),
                        n && t.enableTypingIndicator !== !1 && /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-start gap-3",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4 text-white",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 10V3L4 14h7v7l9-11h-7z"
                                        })
                                    })
                                }),
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "bg-gray-100 dark:bg-slate-800 rounded-lg px-4 py-3 max-w-xs",
                                    children: /* @__PURE__ */ e.jsxs("div", {
                                        className: "flex space-x-1",
                                        children: [
                                            /* @__PURE__ */ e.jsx("div", {
                                                className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce"
                                            }),
                                            /* @__PURE__ */ e.jsx("div", {
                                                className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
                                                style: {
                                                    animationDelay: "0.1s"
                                                }
                                            }),
                                            /* @__PURE__ */ e.jsx("div", {
                                                className: "w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full animate-bounce",
                                                style: {
                                                    animationDelay: "0.2s"
                                                }
                                            })
                                        ]
                                    })
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("div", {
                            ref: x
                        })
                    ]
                })
            }),
            t.enableSuggestions && t.suggestions && t.suggestions.length > 0 && r.length === 0 && /* @__PURE__ */ e.jsxs("div", {
                className: "px-4 pb-2",
                children: [
                    /* @__PURE__ */ e.jsx("div", {
                        className: "text-xs text-gray-500 dark:text-gray-400 mb-2",
                        children: "Quick suggestions:"
                    }),
                    /* @__PURE__ */ e.jsx("div", {
                        className: "flex flex-wrap gap-2",
                        children: t.suggestions.slice(0, 3).map((d, y)=>/* @__PURE__ */ e.jsx("button", {
                                onClick: ()=>o == null ? void 0 : o(d),
                                className: "px-3 py-1.5 text-xs bg-gray-100 dark:bg-slate-800 hover:bg-gray-200 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 rounded-full transition-colors",
                                children: d
                            }, y))
                    })
                ]
            }),
            t.showInputField !== !1 && o && /* @__PURE__ */ e.jsx("div", {
                className: "border-t border-gray-200 dark:border-slate-700 p-4",
                children: /* @__PURE__ */ e.jsx(Be, {
                    placeholder: t.placeholder || "Ask me about products, tools, or services...",
                    disabled: n,
                    suggestions: t.suggestions,
                    theme: s,
                    onSendMessage: o
                })
            })
        ]
    });
}, Xe = ({ config: r, theme: t, title: s = "AI Assistant", subtitle: n = "Get personalized recommendations", isOpen: o, onToggle: a, onSendMessage: i, onRecommendationClick: x, autoRecommendations: p, autoRecommendationTrigger: h, showInputField: c = !0, autoShowRecommendations: m = !1, onAutoRecommendationDismiss: d, className: y })=>{
    const [u, v] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(r.autoOpen || !1), [b, f] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]), [_, g] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), [L, M] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), S = o !== void 0 ? o : u;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (r.showWelcomeMessage && r.welcomeMessage && b.length === 0) {
            const P = {
                id: "welcome",
                role: "assistant",
                content: r.welcomeMessage,
                timestamp: /* @__PURE__ */ new Date()
            };
            f([
                P
            ]);
        }
    }, [
        r.showWelcomeMessage,
        r.welcomeMessage,
        b.length
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (p && p.length > 0 && m) {
            const P = {
                id: `auto-${Date.now()}`,
                role: "assistant",
                content: h ? `Based on "${h}", here are some relevant recommendations:` : "I found some relevant recommendations for you:",
                timestamp: /* @__PURE__ */ new Date(),
                recommendations: p
            };
            o === void 0 && v(!0), f((C)=>C.some((z)=>z.id.startsWith("auto-")) ? C.map((z)=>z.id.startsWith("auto-") ? P : z) : [
                    ...C,
                    P
                ]);
        }
    }, [
        p,
        m,
        h,
        o
    ]);
    const k = ()=>{
        a ? a() : v(!u), M(!0);
    }, T = async (P)=>{
        if (!i) return;
        const C = {
            id: `user-${Date.now()}`,
            role: "user",
            content: P,
            timestamp: /* @__PURE__ */ new Date()
        };
        f((E)=>[
                ...E,
                C
            ]), g(!0);
        try {
            const E = await i(P);
            f((z)=>[
                    ...z,
                    E
                ]);
        } catch (E) {
            console.error("Error sending message:", E);
            const z = {
                id: `error-${Date.now()}`,
                role: "assistant",
                content: "Sorry, I encountered an error. Please try again.",
                timestamp: /* @__PURE__ */ new Date()
            };
            f((J)=>[
                    ...J,
                    z
                ]);
        } finally{
            g(!1);
        }
    }, I = ()=>{
        switch(r.size){
            case "sm":
                return "w-80 h-96";
            case "md":
                return "w-96 h-[32rem]";
            case "lg":
                return "w-[28rem] h-[36rem]";
            case "xl":
                return "w-[32rem] h-[40rem]";
            default:
                return "w-96 h-[32rem]";
        }
    }, W = N("admesh-floating-chat", "fixed z-50 transition-all duration-300 ease-in-out", (()=>{
        switch(r.position){
            case "bottom-right":
                return "bottom-4 right-4";
            case "bottom-left":
                return "bottom-4 left-4";
            case "top-right":
                return "top-4 right-4";
            case "top-left":
                return "top-4 left-4";
            default:
                return "bottom-4 right-4";
        }
    })(), y), w = N("bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden", I(), {
        "opacity-0 scale-95 pointer-events-none": !S,
        "opacity-100 scale-100": S
    }), F = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: W,
        style: F,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            /* @__PURE__ */ e.jsx("div", {
                className: w,
                children: S && /* @__PURE__ */ e.jsxs(e.Fragment, {
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",
                            children: [
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-3",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",
                                            children: /* @__PURE__ */ e.jsx("svg", {
                                                className: "w-4 h-4",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                                })
                                            })
                                        }),
                                        /* @__PURE__ */ e.jsxs("div", {
                                            children: [
                                                /* @__PURE__ */ e.jsx("h3", {
                                                    className: "font-semibold text-sm",
                                                    children: s
                                                }),
                                                /* @__PURE__ */ e.jsx("p", {
                                                    className: "text-xs text-blue-100",
                                                    children: n
                                                })
                                            ]
                                        })
                                    ]
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        p && p.length > 0 && d && /* @__PURE__ */ e.jsx("button", {
                                            onClick: ()=>{
                                                d(), f((P)=>P.filter((C)=>!C.id.startsWith("auto-")));
                                            },
                                            className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                                            "aria-label": "Dismiss recommendations",
                                            title: "Dismiss recommendations",
                                            children: /* @__PURE__ */ e.jsx("svg", {
                                                className: "w-4 h-4",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                                                })
                                            })
                                        }),
                                        /* @__PURE__ */ e.jsx("button", {
                                            onClick: k,
                                            className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                                            "aria-label": "Close chat",
                                            children: /* @__PURE__ */ e.jsx("svg", {
                                                className: "w-4 h-4",
                                                fill: "none",
                                                stroke: "currentColor",
                                                viewBox: "0 0 24 24",
                                                children: /* @__PURE__ */ e.jsx("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M6 18L18 6M6 6l12 12"
                                                })
                                            })
                                        })
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx(Oe, {
                            messages: b,
                            config: {
                                ...r,
                                showInputField: c
                            },
                            theme: t,
                            isLoading: _,
                            onSendMessage: c ? T : void 0,
                            onRecommendationClick: x,
                            className: "h-full"
                        })
                    ]
                })
            }),
            !S && /* @__PURE__ */ e.jsxs("button", {
                onClick: k,
                className: N("w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700", "text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200", "flex items-center justify-center relative"),
                "aria-label": "Open chat",
                children: [
                    /* @__PURE__ */ e.jsx("svg", {
                        className: "w-6 h-6",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /* @__PURE__ */ e.jsx("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                        })
                    }),
                    !L && /* @__PURE__ */ e.jsx("div", {
                        className: "absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"
                    })
                ]
            }),
            S && /* @__PURE__ */ e.jsx("div", {
                className: "absolute bottom-2 right-2 text-xs text-gray-400 dark:text-gray-500 bg-white dark:bg-slate-900 px-2 py-1 rounded shadow-sm",
                children: "Powered by AdMesh"
            })
        ]
    });
}, $e = ({ title: r, theme: t, collapsible: s = !1, isCollapsed: n = !1, onToggle: o, onSearch: a, showSearch: i = !1, className: x })=>{
    const [p, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(""), [c, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), [d, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const _ = ()=>{
            y(window.innerWidth < 640);
        };
        return _(), window.addEventListener("resize", _), ()=>window.removeEventListener("resize", _);
    }, []);
    const u = (_)=>{
        const g = _.target.value;
        h(g), a == null || a(g);
    }, v = ()=>{
        h(""), a == null || a("");
    }, b = N("admesh-sidebar-header", "flex flex-col p-4 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800", x), f = t != null && t.accentColor ? {
        "--admesh-primary": t.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsxs("div", {
        className: b,
        style: f,
        "data-admesh-theme": t == null ? void 0 : t.mode,
        children: [
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex items-center justify-between mb-3",
                children: [
                    /* @__PURE__ */ e.jsx("h3", {
                        className: "text-lg font-semibold text-gray-900 dark:text-gray-100 truncate",
                        children: r
                    }),
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-2",
                        children: [
                            d && o && /* @__PURE__ */ e.jsx("button", {
                                onClick: o,
                                className: "p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 sm:hidden",
                                title: "Close sidebar",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-4 h-4 text-gray-600 dark:text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M6 18L18 6M6 6l12 12"
                                    })
                                })
                            }),
                            s && /* @__PURE__ */ e.jsx("button", {
                                onClick: o,
                                className: "p-1.5 rounded-lg hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors flex-shrink-0 hidden sm:block",
                                title: n ? "Expand sidebar" : "Collapse sidebar",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: N("w-4 h-4 text-gray-600 dark:text-gray-400 transition-transform duration-200", {
                                        "rotate-180": n
                                    }),
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M15 19l-7-7 7-7"
                                    })
                                })
                            })
                        ]
                    })
                ]
            }),
            i && !n && /* @__PURE__ */ e.jsxs("div", {
                className: "relative",
                children: [
                    /* @__PURE__ */ e.jsxs("div", {
                        className: N("relative flex items-center transition-all duration-200", {
                            "ring-2 ring-blue-500 dark:ring-blue-400": c
                        }),
                        children: [
                            /* @__PURE__ */ e.jsx("div", {
                                className: "absolute left-3 pointer-events-none",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-4 h-4 text-gray-400 dark:text-gray-500",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    })
                                })
                            }),
                            /* @__PURE__ */ e.jsx("input", {
                                type: "text",
                                value: p,
                                onChange: u,
                                onFocus: ()=>m(!0),
                                onBlur: ()=>m(!1),
                                placeholder: "Search recommendations...",
                                className: N("w-full pl-10 pr-10 py-2 text-sm bg-white dark:bg-slate-900 border border-gray-300 dark:border-slate-600 rounded-lg", "placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100", "focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent", "transition-all duration-200")
                            }),
                            p && /* @__PURE__ */ e.jsx("button", {
                                onClick: v,
                                className: "absolute right-3 p-0.5 rounded-full hover:bg-gray-200 dark:hover:bg-slate-700 transition-colors",
                                title: "Clear search",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-3 h-3 text-gray-400 dark:text-gray-500",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M6 18L18 6M6 6l12 12"
                                    })
                                })
                            })
                        ]
                    }),
                    p && /* @__PURE__ */ e.jsx("div", {
                        className: "mt-2 text-xs text-gray-500 dark:text-gray-400",
                        children: "Search results will be filtered in real-time"
                    })
                ]
            }),
            !n && /* @__PURE__ */ e.jsxs("div", {
                className: "mt-3 flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400",
                children: [
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-1",
                        children: [
                            /* @__PURE__ */ e.jsx("div", {
                                className: "w-2 h-2 bg-green-500 rounded-full"
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                children: "Live recommendations"
                            })
                        ]
                    }),
                    /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center gap-1",
                        children: [
                            /* @__PURE__ */ e.jsx("svg", {
                                className: "w-3 h-3",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M13 10V3L4 14h7v7l9-11h-7z"
                                })
                            }),
                            /* @__PURE__ */ e.jsx("span", {
                                children: "AI-powered"
                            })
                        ]
                    })
                ]
            })
        ]
    });
}, Fe = ({ recommendations: r, displayMode: t, theme: s, maxRecommendations: n, onRecommendationClick: o, className: a })=>{
    const [i, x] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), [p, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("all"), c = n ? r.slice(0, n) : r, d = (()=>{
        switch(p){
            case "top":
                return c.filter((b)=>b.intent_match_score >= 0.8).slice(0, 5);
            case "recent":
                return c.slice(0, 3);
            default:
                return c;
        }
    })(), y = N("admesh-sidebar-content", "flex flex-col h-full", a), u = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0, v = ()=>{
        if (d.length === 0) return /* @__PURE__ */ e.jsxs("div", {
            className: "flex-1 flex flex-col items-center justify-center p-6 text-center",
            children: [
                /* @__PURE__ */ e.jsx("div", {
                    className: "w-16 h-16 bg-gray-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4",
                    children: /* @__PURE__ */ e.jsx("svg", {
                        className: "w-8 h-8 text-gray-400 dark:text-gray-500",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: /* @__PURE__ */ e.jsx("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        })
                    })
                }),
                /* @__PURE__ */ e.jsx("h4", {
                    className: "text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",
                    children: "No recommendations found"
                }),
                /* @__PURE__ */ e.jsx("p", {
                    className: "text-xs text-gray-500 dark:text-gray-400",
                    children: "Try adjusting your search or filters"
                })
            ]
        });
        switch(t){
            case "recommendations":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: d.map((b, f)=>/* @__PURE__ */ e.jsx(q, {
                            recommendation: b,
                            theme: s,
                            compact: !0,
                            showReason: !0,
                            onClick: o
                        }, b.ad_id || f))
                });
            case "history":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-2",
                    children: d.map((b, f)=>/* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800 transition-colors",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-2 h-2 bg-gray-400 rounded-full flex-shrink-0"
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    className: "flex-1 min-w-0",
                                    children: [
                                        /* @__PURE__ */ e.jsx("div", {
                                            className: "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                            children: b.title
                                        }),
                                        /* @__PURE__ */ e.jsxs("div", {
                                            className: "text-xs text-gray-500 dark:text-gray-400",
                                            children: [
                                                Math.round(b.intent_match_score * 100),
                                                "% match"
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }, b.ad_id || f))
                });
            case "favorites":
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: d.slice(0, 3).map((b, f)=>/* @__PURE__ */ e.jsxs("div", {
                            className: "relative",
                            children: [
                                /* @__PURE__ */ e.jsx(q, {
                                    recommendation: b,
                                    theme: s,
                                    compact: !0,
                                    showReason: !1,
                                    onClick: o
                                }),
                                /* @__PURE__ */ e.jsx("button", {
                                    className: "absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-3 h-3 text-yellow-500",
                                        fill: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                                        })
                                    })
                                })
                            ]
                        }, b.ad_id || f))
                });
            case "mixed":
                return /* @__PURE__ */ e.jsxs("div", {
                    className: "space-y-4",
                    children: [
                        d[0] && /* @__PURE__ */ e.jsxs("div", {
                            children: [
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",
                                    children: "Top Pick"
                                }),
                                /* @__PURE__ */ e.jsx(ue, {
                                    recommendation: d[0],
                                    theme: s,
                                    showMatchScore: !0,
                                    showBadges: !0,
                                    onClick: o,
                                    className: "text-xs"
                                })
                            ]
                        }),
                        d.slice(1).length > 0 && /* @__PURE__ */ e.jsxs("div", {
                            children: [
                                /* @__PURE__ */ e.jsx("h4", {
                                    className: "text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2",
                                    children: "More Options"
                                }),
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "space-y-2",
                                    children: d.slice(1, 4).map((b, f)=>/* @__PURE__ */ e.jsx(q, {
                                            recommendation: b,
                                            theme: s,
                                            compact: !0,
                                            showReason: !1,
                                            onClick: o
                                        }, b.ad_id || f))
                                })
                            ]
                        })
                    ]
                });
            default:
                return /* @__PURE__ */ e.jsx("div", {
                    className: "space-y-3",
                    children: d.map((b, f)=>/* @__PURE__ */ e.jsx(q, {
                            recommendation: b,
                            theme: s,
                            compact: !0,
                            showReason: !0,
                            onClick: o
                        }, b.ad_id || f))
                });
        }
    };
    return /* @__PURE__ */ e.jsxs("div", {
        className: y,
        style: u,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: [
            /* @__PURE__ */ e.jsxs("div", {
                className: "flex border-b border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-900",
                children: [
                    /* @__PURE__ */ e.jsxs("button", {
                        onClick: ()=>h("all"),
                        className: N("flex-1 px-3 py-2 text-xs font-medium transition-colors", p === "all" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),
                        children: [
                            "All (",
                            r.length,
                            ")"
                        ]
                    }),
                    /* @__PURE__ */ e.jsx("button", {
                        onClick: ()=>h("top"),
                        className: N("flex-1 px-3 py-2 text-xs font-medium transition-colors", p === "top" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),
                        children: "Top"
                    }),
                    /* @__PURE__ */ e.jsx("button", {
                        onClick: ()=>h("recent"),
                        className: N("flex-1 px-3 py-2 text-xs font-medium transition-colors", p === "recent" ? "text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400" : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"),
                        children: "Recent"
                    })
                ]
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "flex-1 overflow-y-auto p-4 min-h-0",
                style: {
                    WebkitOverflowScrolling: "touch",
                    // Smooth scrolling on iOS
                    overscrollBehavior: "contain"
                },
                children: v()
            }),
            /* @__PURE__ */ e.jsx("div", {
                className: "p-3 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800",
                children: /* @__PURE__ */ e.jsxs("div", {
                    className: "flex items-center justify-between text-xs",
                    children: [
                        /* @__PURE__ */ e.jsxs("span", {
                            className: "text-gray-500 dark:text-gray-400",
                            children: [
                                d.length,
                                " recommendation",
                                d.length !== 1 ? "s" : ""
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("button", {
                            onClick: ()=>x(!i),
                            className: "text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors",
                            children: "Filters"
                        })
                    ]
                })
            })
        ]
    });
}, Ze = ({ recommendations: r, config: t, theme: s, title: n = "Recommendations", isOpen: o = !0, onToggle: a, onRecommendationClick: i, onSearch: x, // onFilter,
className: p, containerMode: h = !1 })=>{
    const [c, m] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(t.defaultCollapsed || !1), [d, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(""), [u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({}), [v, b] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const k = ()=>{
            b(window.innerWidth < 640);
        };
        return k(), window.addEventListener("resize", k), ()=>window.removeEventListener("resize", k);
    }, []), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (v && o && !c && !h) {
            const k = window.getComputedStyle(document.body).overflow;
            return document.body.style.overflow = "hidden", document.body.style.position = "fixed", document.body.style.width = "100%", ()=>{
                document.body.style.overflow = k, document.body.style.position = "", document.body.style.width = "";
            };
        }
    }, [
        v,
        o,
        c,
        h
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (t.autoRefresh && t.refreshInterval) {
            const k = setInterval(()=>{
                console.log("Auto-refreshing recommendations...");
            }, t.refreshInterval);
            return ()=>clearInterval(k);
        }
    }, [
        t.autoRefresh,
        t.refreshInterval
    ]);
    const f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        let k = [
            ...r
        ];
        if (d.trim()) {
            const T = d.toLowerCase();
            k = k.filter((I)=>{
                var $;
                return I.title.toLowerCase().includes(T) || I.reason.toLowerCase().includes(T) || (($ = I.keywords) == null ? void 0 : $.some((W)=>W.toLowerCase().includes(T)));
            });
        }
        return u.categories && u.categories.length > 0 && (k = k.filter((T)=>{
            var I;
            return (I = T.categories) == null ? void 0 : I.some(($)=>{
                var W;
                return (W = u.categories) == null ? void 0 : W.includes($);
            });
        })), u.hasFreeTier, u.hasTrial && (k = k.filter((T)=>T.trial_days && T.trial_days > 0)), u.minMatchScore !== void 0 && (k = k.filter((T)=>T.intent_match_score >= u.minMatchScore)), k.sort((T, I)=>I.intent_match_score - T.intent_match_score), t.maxRecommendations && (k = k.slice(0, t.maxRecommendations)), k;
    }, [
        r,
        d,
        u,
        t.maxRecommendations
    ]), _ = ()=>{
        t.collapsible && (m(!c), a == null || a());
    }, g = (k)=>{
        y(k), x == null || x(k);
    }, M = N("admesh-sidebar", "flex flex-col bg-white dark:bg-slate-900 border-gray-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out", (()=>{
        if (c) return "w-12";
        switch(t.size){
            case "sm":
                return "w-full sm:w-64 max-w-[90vw] sm:max-w-sm";
            case "md":
                return "w-full sm:w-80 max-w-[90vw] sm:max-w-md";
            case "lg":
                return "w-full sm:w-96 max-w-[90vw] sm:max-w-lg";
            case "xl":
                return "w-full sm:w-[28rem] max-w-[90vw] sm:max-w-xl";
            default:
                return "w-full sm:w-80 max-w-[90vw] sm:max-w-md";
        }
    })(), {
        "border-r": t.position === "left",
        "border-l": t.position === "right",
        // Use fixed positioning for full-screen mode, relative for container mode
        // Improved mobile positioning with proper viewport handling
        "fixed top-0 bottom-0 z-[9999]": !h,
        "relative h-full": h,
        "left-0": t.position === "left" && !h,
        "right-0": t.position === "right" && !h,
        // Better mobile transform handling
        "transform -translate-x-full": t.position === "left" && !o && !h,
        "transform translate-x-full": t.position === "right" && !o && !h,
        // Mobile-specific improvements
        "min-h-0": !0,
        // Prevent height issues on mobile
        "overflow-hidden": !h
    }, p), S = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return !o && !t.collapsible ? null : /* @__PURE__ */ e.jsxs(e.Fragment, {
        children: [
            o && !c && /* @__PURE__ */ e.jsx("div", {
                className: N("bg-black bg-opacity-50 z-[9998] sm:hidden transition-opacity duration-300", h ? "absolute inset-0" : "fixed inset-0"),
                onClick: ()=>a == null ? void 0 : a(),
                style: {
                    // Ensure overlay covers the entire viewport on mobile
                    position: h ? "absolute" : "fixed",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    touchAction: "none"
                }
            }),
            /* @__PURE__ */ e.jsxs("div", {
                className: M,
                style: S,
                "data-admesh-theme": s == null ? void 0 : s.mode,
                "data-sidebar-position": t.position,
                "data-sidebar-size": t.size,
                "data-mobile-open": v && o && !c ? "true" : "false",
                "data-container-mode": h ? "true" : "false",
                children: [
                    t.showHeader !== !1 && /* @__PURE__ */ e.jsx($e, {
                        title: n,
                        theme: s,
                        collapsible: t.collapsible,
                        isCollapsed: c,
                        onToggle: _,
                        onSearch: t.showSearch ? g : void 0,
                        showSearch: t.showSearch && !c
                    }),
                    !c && /* @__PURE__ */ e.jsx(Fe, {
                        recommendations: f,
                        displayMode: t.displayMode,
                        theme: s,
                        maxRecommendations: t.maxRecommendations,
                        onRecommendationClick: i,
                        className: "flex-1 overflow-hidden min-h-0"
                    }),
                    c && t.collapsible && /* @__PURE__ */ e.jsxs("div", {
                        className: "flex-1 flex flex-col items-center justify-center p-2",
                        children: [
                            /* @__PURE__ */ e.jsx("button", {
                                onClick: _,
                                className: "p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-800 transition-colors",
                                title: "Expand sidebar",
                                children: /* @__PURE__ */ e.jsx("svg", {
                                    className: "w-5 h-5 text-gray-600 dark:text-gray-400",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /* @__PURE__ */ e.jsx("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M9 5l7 7-7 7"
                                    })
                                })
                            }),
                            /* @__PURE__ */ e.jsx("div", {
                                className: "mt-4 text-xs text-gray-500 dark:text-gray-400 transform -rotate-90 whitespace-nowrap",
                                children: f.length
                            })
                        ]
                    }),
                    !c && /* @__PURE__ */ e.jsx("div", {
                        className: "p-3 border-t border-gray-200 dark:border-slate-700",
                        children: /* @__PURE__ */ e.jsx("div", {
                            className: "text-xs text-gray-400 dark:text-gray-500 text-center",
                            children: "Powered by AdMesh"
                        })
                    })
                ]
            })
        ]
    });
}, er = ({ recommendations: r, trigger: t, theme: s, title: n = "AI Recommendations", position: o = "bottom-right", size: a = "md", autoShow: i = !0, showDelay: x = 1e3, onRecommendationClick: p, onDismiss: h, className: c })=>{
    const [m, d] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1), [y, u] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (i && r.length > 0) {
            const L = setTimeout(()=>{
                d(!0), u(!0);
            }, x);
            return ()=>clearTimeout(L);
        }
    }, [
        i,
        r.length,
        x
    ]);
    const v = ()=>{
        d(!1), h == null || h();
    }, b = ()=>{
        switch(a){
            case "sm":
                return "w-72 max-h-80";
            case "md":
                return "w-80 max-h-96";
            case "lg":
                return "w-96 max-h-[28rem]";
            default:
                return "w-80 max-h-96";
        }
    }, f = ()=>{
        switch(o){
            case "bottom-right":
                return "bottom-4 right-4";
            case "bottom-left":
                return "bottom-4 left-4";
            case "top-right":
                return "top-4 right-4";
            case "top-left":
                return "top-4 left-4";
            default:
                return "bottom-4 right-4";
        }
    };
    if (!m || r.length === 0) return null;
    const _ = N("admesh-auto-recommendation-widget", "fixed z-50 transition-all duration-500 ease-out", f(), b(), {
        "opacity-0 scale-95 translate-y-2": !y,
        "opacity-100 scale-100 translate-y-0": y
    }, c), g = s != null && s.accentColor ? {
        "--admesh-primary": s.accentColor
    } : void 0;
    return /* @__PURE__ */ e.jsx("div", {
        className: _,
        style: g,
        "data-admesh-theme": s == null ? void 0 : s.mode,
        children: /* @__PURE__ */ e.jsxs("div", {
            className: "bg-white dark:bg-slate-900 rounded-lg shadow-2xl border border-gray-200 dark:border-slate-700 overflow-hidden",
            children: [
                /* @__PURE__ */ e.jsxs("div", {
                    className: "flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center",
                                    children: /* @__PURE__ */ e.jsx("svg", {
                                        className: "w-4 h-4",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /* @__PURE__ */ e.jsx("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M13 10V3L4 14h7v7l9-11h-7z"
                                        })
                                    })
                                }),
                                /* @__PURE__ */ e.jsxs("div", {
                                    children: [
                                        /* @__PURE__ */ e.jsx("h3", {
                                            className: "font-semibold text-sm",
                                            children: n
                                        }),
                                        t && /* @__PURE__ */ e.jsxs("p", {
                                            className: "text-xs text-blue-100 truncate max-w-48",
                                            children: [
                                                'Based on: "',
                                                t,
                                                '"'
                                            ]
                                        })
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx("button", {
                            onClick: v,
                            className: "p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors",
                            "aria-label": "Dismiss recommendations",
                            children: /* @__PURE__ */ e.jsx("svg", {
                                className: "w-4 h-4",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /* @__PURE__ */ e.jsx("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2,
                                    d: "M6 18L18 6M6 6l12 12"
                                })
                            })
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsxs("div", {
                    className: "p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-slate-600",
                    children: [
                        /* @__PURE__ */ e.jsxs("div", {
                            className: "flex items-center gap-2 mb-3",
                            children: [
                                /* @__PURE__ */ e.jsx("div", {
                                    className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                }),
                                /* @__PURE__ */ e.jsxs("span", {
                                    className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                    children: [
                                        r.length,
                                        " intelligent match",
                                        r.length > 1 ? "es" : "",
                                        " found"
                                    ]
                                })
                            ]
                        }),
                        /* @__PURE__ */ e.jsx(ye, {
                            recommendations: r,
                            config: {
                                displayMode: "inline",
                                context: "assistant",
                                maxRecommendations: 3,
                                showPoweredBy: !1,
                                autoShow: !0,
                                delayMs: 200
                            },
                            theme: s,
                            onRecommendationClick: p
                        })
                    ]
                }),
                /* @__PURE__ */ e.jsx("div", {
                    className: "px-4 py-3 bg-gray-50 dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700",
                    children: /* @__PURE__ */ e.jsxs("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /* @__PURE__ */ e.jsx("span", {
                                className: "text-xs text-gray-500 dark:text-gray-400",
                                children: "Powered by AdMesh"
                            }),
                            /* @__PURE__ */ e.jsx("button", {
                                onClick: v,
                                className: "text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",
                                children: "Dismiss"
                            })
                        ]
                    })
                })
            ]
        })
    });
}, De = `
/* AdMesh UI SDK Scoped Styles - Smart Recommendations Design */
.admesh-component {
  --admesh-primary: #6366f1;
  --admesh-primary-hover: #4f46e5;
  --admesh-secondary: #8b5cf6;
  --admesh-accent: #06b6d4;
  --admesh-background: #ffffff;
  --admesh-surface: #ffffff;
  --admesh-border: #e2e8f0;
  --admesh-text: #0f172a;
  --admesh-text-muted: #64748b;
  --admesh-text-light: #94a3b8;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --admesh-radius: 0.75rem;
  --admesh-radius-sm: 0.375rem;
  --admesh-radius-lg: 1rem;
  --admesh-radius-xl: 1.5rem;
}

.admesh-component[data-admesh-theme="dark"] {
  --admesh-background: #111827;
  --admesh-surface: #1f2937;
  --admesh-border: #374151;
  --admesh-text: #f9fafb;
  --admesh-text-muted: #9ca3af;
  --admesh-text-light: #6b7280;
  --admesh-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
  --admesh-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --admesh-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --admesh-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Layout Styles */
.admesh-layout {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: var(--admesh-text);
  background-color: var(--admesh-background);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  box-shadow: var(--admesh-shadow);
  border: 1px solid var(--admesh-border);
}

.admesh-layout__header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.admesh-layout__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
}

.admesh-layout__subtitle {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

.admesh-layout__cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.admesh-layout__more-indicator {
  text-align: center;
  padding: 1rem;
  color: var(--admesh-text-muted);
  font-size: 0.875rem;
}

.admesh-layout__empty {
  text-align: center;
  padding: 3rem 1rem;
}

.admesh-layout__empty-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text-muted);
  margin-bottom: 0.5rem;
}

.admesh-layout__empty-content p {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
}

/* Product Card Styles */
.admesh-product-card {
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.admesh-product-card:hover {
  box-shadow: var(--admesh-shadow-lg);
  transform: translateY(-2px);
  border-color: var(--admesh-primary);
}

.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.admesh-product-card__title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--admesh-text);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.admesh-product-card__reason {
  font-size: 0.875rem;
  color: var(--admesh-text-muted);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.admesh-product-card__match-score {
  margin-bottom: 1rem;
}

.admesh-product-card__match-score-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--admesh-text-muted);
  margin-bottom: 0.25rem;
}

.admesh-product-card__match-score-bar {
  width: 100%;
  height: 0.375rem;
  background-color: var(--admesh-border);
  border-radius: var(--admesh-radius-sm);
  overflow: hidden;
}

.admesh-product-card__match-score-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--admesh-primary), #8b5cf6);
  border-radius: var(--admesh-radius-sm);
  transition: width 0.3s ease-in-out;
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.admesh-product-card__badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background-color: var(--admesh-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--admesh-radius-sm);
}

.admesh-product-card__badge--secondary {
  background-color: var(--admesh-secondary);
}

.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.admesh-product-card__keyword {
  padding: 0.125rem 0.375rem;
  background-color: var(--admesh-border);
  color: var(--admesh-text-muted);
  font-size: 0.75rem;
  border-radius: var(--admesh-radius-sm);
}

/* Dark mode specific enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-product-card__keyword {
  background-color: #4b5563;
  color: #d1d5db;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card:hover {
  border-color: var(--admesh-primary);
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-product-card__button:hover {
  background: linear-gradient(90deg, var(--admesh-primary-hover), var(--admesh-primary));
}

.admesh-product-card__footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

/* Mobile-specific sidebar improvements */
@media (max-width: 640px) {
  .admesh-sidebar {
    /* Ensure proper mobile viewport handling */
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile browsers */
    max-height: 100vh !important;
    max-height: 100dvh !important;
    width: 100vw !important;
    max-width: 90vw !important;
    overflow: hidden !important;
  }

  .admesh-sidebar.relative {
    height: 100% !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Improve touch scrolling */
  .admesh-sidebar .overflow-y-auto {
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain !important;
    scroll-behavior: smooth !important;
  }

  /* Prevent body scroll when sidebar is open */
  body:has(.admesh-sidebar[data-mobile-open="true"]) {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }
}

/* Tablet improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .admesh-sidebar {
    max-width: 400px !important;
  }
}

/* Mobile responsiveness improvements for all components */
@media (max-width: 640px) {
  /* Product cards mobile optimization */
  .admesh-card {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  /* Inline recommendations mobile optimization */
  .admesh-inline-recommendation {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  /* Conversation summary mobile optimization */
  .admesh-conversation-summary {
    padding: 1rem !important;
  }

  /* Percentage text mobile improvements */
  .admesh-component .text-xs {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
  }

  .admesh-component .text-sm {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
  }

  /* Button mobile improvements */
  .admesh-component button {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
    min-height: 2rem !important;
    touch-action: manipulation !important;
  }

  /* Badge mobile improvements */
  .admesh-component .rounded-full {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.625rem !important;
    line-height: 1rem !important;
  }

  /* Progress bar mobile improvements */
  .admesh-component .bg-gray-200,
  .admesh-component .bg-slate-600 {
    height: 0.25rem !important;
  }

  /* Flex layout mobile improvements */
  .admesh-component .flex {
    flex-wrap: wrap !important;
  }

  .admesh-component .gap-2 {
    gap: 0.375rem !important;
  }

  .admesh-component .gap-3 {
    gap: 0.5rem !important;
  }
}

.admesh-product-card__button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(90deg, var(--admesh-primary), var(--admesh-primary-hover));
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: var(--admesh-radius);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.admesh-product-card__button:hover {
  transform: translateY(-1px);
  box-shadow: var(--admesh-shadow-lg);
}

/* Utility Classes */
.admesh-text-xs { font-size: 0.75rem; }
.admesh-text-sm { font-size: 0.875rem; }
.admesh-text-base { font-size: 1rem; }
.admesh-text-lg { font-size: 1.125rem; }
.admesh-text-xl { font-size: 1.25rem; }

.admesh-font-medium { font-weight: 500; }
.admesh-font-semibold { font-weight: 600; }
.admesh-font-bold { font-weight: 700; }

.admesh-text-muted { color: var(--admesh-text-muted); }

/* Comparison Table Styles */
.admesh-compare-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--admesh-surface);
  border: 1px solid var(--admesh-border);
  border-radius: var(--admesh-radius);
  overflow: hidden;
}

.admesh-compare-table th,
.admesh-compare-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--admesh-border);
}

.admesh-compare-table th {
  background-color: var(--admesh-background);
  font-weight: 600;
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table td {
  color: var(--admesh-text);
  font-size: 0.875rem;
}

.admesh-compare-table tr:hover {
  background-color: var(--admesh-border);
}

/* Dark mode table enhancements */
.admesh-component[data-admesh-theme="dark"] .admesh-compare-table th {
  background-color: #374151;
}

.admesh-component[data-admesh-theme="dark"] .admesh-compare-table tr:hover {
  background-color: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admesh-layout {
    padding: 1rem;
  }

  .admesh-layout__cards-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .admesh-product-card {
    padding: 1rem;
  }

  .admesh-compare-table {
    font-size: 0.75rem;
  }

  .admesh-compare-table th,
  .admesh-compare-table td {
    padding: 0.5rem;
  }
}
`;
let ie = !1;
const rr = ()=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (ie) return;
        const r = document.createElement("style");
        return r.id = "admesh-ui-sdk-styles", r.textContent = De, document.getElementById("admesh-ui-sdk-styles") || (document.head.appendChild(r), ie = !0), ()=>{
            const t = document.getElementById("admesh-ui-sdk-styles");
            t && document.head.contains(t) && (document.head.removeChild(t), ie = !1);
        };
    }, []);
}, G = (r = {})=>{
    const t = {
        mode: "light",
        primaryColor: "#3b82f6",
        secondaryColor: "#10b981",
        accentColor: "#3b82f6",
        backgroundColor: "#ffffff",
        surfaceColor: "#f9fafb",
        borderColor: "#e5e7eb",
        textColor: "#111827",
        textSecondaryColor: "#6b7280",
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fontSize: {
            small: "12px",
            base: "14px",
            large: "16px",
            title: "18px"
        },
        borderRadius: "8px",
        spacing: {
            small: "4px",
            medium: "8px",
            large: "16px"
        },
        shadows: {
            small: "0 1px 3px rgba(0, 0, 0, 0.1)",
            medium: "0 4px 6px rgba(0, 0, 0, 0.1)",
            large: "0 10px 15px rgba(0, 0, 0, 0.1)"
        },
        icons: {
            expandIcon: "▼",
            collapseIcon: "▲",
            starIcon: "★",
            checkIcon: "✓",
            arrowIcon: "→"
        }
    };
    return {
        ...t,
        ...r,
        fontSize: {
            ...t.fontSize,
            ...r.fontSize
        },
        spacing: {
            ...t.spacing,
            ...r.spacing
        },
        shadows: {
            ...t.shadows,
            ...r.shadows
        },
        icons: {
            ...t.icons,
            ...r.icons
        },
        components: {
            ...t.components,
            ...r.components
        }
    };
}, tr = (r = {})=>G({
        ...{
            mode: "dark",
            backgroundColor: "#1f2937",
            surfaceColor: "#374151",
            borderColor: "#4b5563",
            textColor: "#f9fafb",
            textSecondaryColor: "#9ca3af",
            shadows: {
                small: "0 1px 3px rgba(0, 0, 0, 0.3)",
                medium: "0 4px 6px rgba(0, 0, 0, 0.3)",
                large: "0 10px 15px rgba(0, 0, 0, 0.3)"
            }
        },
        ...r
    }), sr = {
    // Clean, minimal theme
    minimal: G({
        primaryColor: "#000000",
        secondaryColor: "#666666",
        borderRadius: "4px",
        shadows: {
            small: "none",
            medium: "0 1px 3px rgba(0, 0, 0, 0.1)",
            large: "0 2px 6px rgba(0, 0, 0, 0.1)"
        }
    }),
    // Modern, colorful theme
    vibrant: G({
        primaryColor: "#8b5cf6",
        secondaryColor: "#06b6d4",
        accentColor: "#f59e0b",
        borderRadius: "12px",
        gradients: {
            primary: "linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%)",
            secondary: "linear-gradient(135deg, #06b6d4 0%, #10b981 100%)",
            accent: "linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)"
        }
    }),
    // Professional, corporate theme
    corporate: G({
        primaryColor: "#1e40af",
        secondaryColor: "#059669",
        backgroundColor: "#f8fafc",
        surfaceColor: "#ffffff",
        borderColor: "#cbd5e1",
        borderRadius: "6px",
        fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, sans-serif'
    }),
    // High contrast theme for accessibility
    highContrast: G({
        primaryColor: "#000000",
        secondaryColor: "#ffffff",
        backgroundColor: "#ffffff",
        surfaceColor: "#f5f5f5",
        borderColor: "#000000",
        textColor: "#000000",
        textSecondaryColor: "#333333",
        borderRadius: "0px",
        shadows: {
            small: "none",
            medium: "0 0 0 2px #000000",
            large: "0 0 0 3px #000000"
        }
    })
}, ar = (...r)=>{
    const t = G();
    return r.reduce((s, n)=>G({
            ...s,
            ...n
        }), t);
}, or = (r)=>{
    var s, n, o, a, i, x;
    const t = getComputedStyle(r);
    return {
        primaryColor: ((s = t.getPropertyValue("--admesh-primary-color")) == null ? void 0 : s.trim()) || void 0,
        secondaryColor: ((n = t.getPropertyValue("--admesh-secondary-color")) == null ? void 0 : n.trim()) || void 0,
        backgroundColor: ((o = t.getPropertyValue("--admesh-background-color")) == null ? void 0 : o.trim()) || void 0,
        textColor: ((a = t.getPropertyValue("--admesh-text-color")) == null ? void 0 : a.trim()) || void 0,
        borderRadius: ((i = t.getPropertyValue("--admesh-border-radius")) == null ? void 0 : i.trim()) || void 0,
        fontFamily: ((x = t.getPropertyValue("--admesh-font-family")) == null ? void 0 : x.trim()) || void 0
    };
}, lr = "0.2.1", nr = {
    trackingEnabled: !0,
    debug: !1,
    theme: {
        mode: "light",
        accentColor: "#2563eb"
    }
};
;
 //# sourceMappingURL=index.mjs.map
}}),
}]);

//# sourceMappingURL=node_modules_9d14916a._.js.map