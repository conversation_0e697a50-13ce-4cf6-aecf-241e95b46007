from openai import OpenAI
import logging
import numpy as np
from typing import List, Dict, Any
from firebase_admin import firestore
from firebase.config import get_db

logger = logging.getLogger(__name__)

# Initialize OpenAI client (automatically uses OPENAI_API_KEY env var)
client = OpenAI()
db = get_db()

def embed_text(text: str) -> List[float]:
    """
    Generate embeddings for the given text using OpenAI's text-embedding-3-small model.

    Args:
        text: The text to embed

    Returns:
        List of floats representing the embedding vector

    Raises:
        Exception: If the OpenAI API call fails
    """
    try:
        logger.info(f"🔮 Generating embedding for text: {text[:100]}...")

        response = client.embeddings.create(
            input=text,
            model="text-embedding-3-small"
        )

        embedding = response.data[0].embedding
        logger.info(f"✅ Generated embedding with {len(embedding)} dimensions")

        return embedding

    except Exception as e:
        logger.error(f"❌ Failed to generate embedding: {str(e)}")
        raise Exception(f"Failed to generate embedding: {str(e)}")


def cosine_similarity(a: List[float], b: List[float]) -> float:
    """
    Calculate cosine similarity between two embedding vectors.

    Args:
        a: First embedding vector
        b: Second embedding vector

    Returns:
        Cosine similarity score between 0 and 1
    """
    try:
        a_array = np.array(a)
        b_array = np.array(b)

        # Calculate cosine similarity
        dot_product = np.dot(a_array, b_array)
        norm_a = np.linalg.norm(a_array)
        norm_b = np.linalg.norm(b_array)

        if norm_a == 0 or norm_b == 0:
            return 0.0

        similarity = dot_product / (norm_a * norm_b)

        # Ensure the result is between 0 and 1
        return float(max(0.0, min(1.0, similarity)))

    except Exception as e:
        logger.error(f"❌ Failed to calculate cosine similarity: {str(e)}")
        return 0.0


def get_product_embedding(product_id: str, product_data: Dict[str, Any]) -> List[float]:
    """
    Retrieve or generate product embedding. This is a transitional function that:
    1. First tries to get embedding from the new product_embeddings collection
    2. Falls back to the old products collection method if not found
    3. Migrates old embeddings to the new collection when found

    Args:
        product_id: The product ID
        product_data: The product data dictionary

    Returns:
        List of floats representing the embedding vector

    Raises:
        Exception: If embedding generation fails
    """
    try:
        # First, try to get embedding from the new collection
        new_collection_embedding = get_product_embedding_from_collection(product_id)
        if new_collection_embedding:
            logger.info(f"✅ Found embedding in product_embeddings collection for {product_id}")
            return new_collection_embedding

        # Check if embedding exists in the old product data (for migration)
        existing_embedding = product_data.get("embedding")
        if existing_embedding and isinstance(existing_embedding, list) and len(existing_embedding) > 0:
            logger.info(f"✅ Found existing embedding in products collection for {product_id}, migrating to new collection...")

            # Migrate to new collection
            metadata = {
                "source_text": generate_embedding_text_from_product(product_data),
                "generation_method": "migrated_from_products_collection",
                "original_embedding_updated_at": product_data.get("embedding_updated_at"),
                "migration_date": firestore.SERVER_TIMESTAMP
            }

            success = store_product_embedding_in_collection(product_id, existing_embedding, metadata)
            if success:
                logger.info(f"✅ Successfully migrated embedding for product {product_id} to new collection")
            else:
                logger.warning(f"⚠️ Failed to migrate embedding for product {product_id} to new collection")

            return existing_embedding

        # Generate new embedding if not found anywhere
        logger.info(f"🔮 No embedding found for product {product_id}, generating new one...")

        # Generate embedding text
        embedding_text = generate_embedding_text_from_product(product_data)

        # Generate embedding
        product_embedding = embed_text(embedding_text)

        # Store in the new collection
        metadata = {
            "source_text": embedding_text,
            "generation_method": "openai_text-embedding-3-small",
            "created_during": "on_demand_generation"
        }

        success = store_product_embedding_in_collection(product_id, product_embedding, metadata)
        if success:
            logger.info(f"💾 Generated and stored new embedding for product {product_id}")
        else:
            logger.warning(f"⚠️ Generated embedding but failed to store in new collection for product {product_id}")

        return product_embedding

    except Exception as e:
        logger.error(f"❌ Failed to get/generate embedding for product {product_id}: {str(e)}")
        # Return a zero vector as fallback to prevent crashes
        return [0.0] * 1536  # text-embedding-3-small has 1536 dimensions


# ========================
# Product Embeddings Collection Utilities
# ========================

def get_product_embedding_from_collection(product_id: str) -> List[float]:
    """
    Retrieve product embedding from the dedicated product_embeddings collection.

    Args:
        product_id: The product ID

    Returns:
        List of floats representing the embedding vector, or None if not found
    """
    try:
        embedding_doc = db.collection("product_embeddings").document(product_id).get()
        if embedding_doc.exists:
            embedding_data = embedding_doc.to_dict()
            embedding = embedding_data.get("embedding")
            if embedding and isinstance(embedding, list) and len(embedding) > 0:
                logger.info(f"✅ Retrieved embedding from product_embeddings collection for {product_id}")
                return embedding

        logger.info(f"❌ No embedding found in product_embeddings collection for {product_id}")
        return None

    except Exception as e:
        logger.error(f"❌ Failed to retrieve embedding from product_embeddings collection for {product_id}: {str(e)}")
        return None


def store_product_embedding_in_collection(product_id: str, embedding: List[float], metadata: Dict[str, Any] = None) -> bool:
    """
    Store product embedding in the dedicated product_embeddings collection.

    Args:
        product_id: The product ID
        embedding: The embedding vector
        metadata: Optional metadata about the embedding generation

    Returns:
        True if successful, False otherwise
    """
    try:
        embedding_data = {
            "product_id": product_id,
            "embedding": embedding,
            "dimensions": len(embedding),
            "model": "text-embedding-3-small",
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }

        # Add optional metadata
        if metadata:
            embedding_data.update(metadata)

        db.collection("product_embeddings").document(product_id).set(embedding_data)
        logger.info(f"💾 Stored embedding in product_embeddings collection for {product_id}")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to store embedding in product_embeddings collection for {product_id}: {str(e)}")
        return False


def update_product_embedding_in_collection(product_id: str, embedding: List[float], metadata: Dict[str, Any] = None) -> bool:
    """
    Update product embedding in the dedicated product_embeddings collection.

    Args:
        product_id: The product ID
        embedding: The new embedding vector
        metadata: Optional metadata about the embedding update

    Returns:
        True if successful, False otherwise
    """
    try:
        update_data = {
            "embedding": embedding,
            "dimensions": len(embedding),
            "updated_at": firestore.SERVER_TIMESTAMP
        }

        # Add optional metadata
        if metadata:
            update_data.update(metadata)

        db.collection("product_embeddings").document(product_id).update(update_data)
        logger.info(f"🔄 Updated embedding in product_embeddings collection for {product_id}")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to update embedding in product_embeddings collection for {product_id}: {str(e)}")
        return False


def delete_product_embedding_from_collection(product_id: str) -> bool:
    """
    Delete product embedding from the dedicated product_embeddings collection.

    Args:
        product_id: The product ID

    Returns:
        True if successful, False otherwise
    """
    try:
        db.collection("product_embeddings").document(product_id).delete()
        logger.info(f"🗑️ Deleted embedding from product_embeddings collection for {product_id}")
        return True

    except Exception as e:
        logger.error(f"❌ Failed to delete embedding from product_embeddings collection for {product_id}: {str(e)}")
        return False


def generate_embedding_text_from_product(product_data: Dict[str, Any]) -> str:
    """
    Generate comprehensive text for embedding from product data.

    Args:
        product_data: The product data dictionary

    Returns:
        String containing all relevant product information for embedding
    """
    embedding_text_parts = []

    # Add title and description
    if product_data.get("title"):
        embedding_text_parts.append(f"Product: {product_data['title']}")
    if product_data.get("description"):
        embedding_text_parts.append(f"Description: {product_data['description']}")

    # Add categories
    if product_data.get("categories") and isinstance(product_data["categories"], list):
        categories_text = ", ".join(product_data["categories"])
        embedding_text_parts.append(f"Categories: {categories_text}")

    # Add keywords
    if product_data.get("keywords") and isinstance(product_data["keywords"], list):
        keywords_text = ", ".join(product_data["keywords"])
        embedding_text_parts.append(f"Keywords: {keywords_text}")

    # Add audience segment
    if product_data.get("audience_segment"):
        embedding_text_parts.append(f"Audience: {product_data['audience_segment']}")

    # Add integrations
    if product_data.get("integration_list") and isinstance(product_data["integration_list"], list):
        integrations_text = ", ".join(product_data["integration_list"])
        embedding_text_parts.append(f"Integrations: {integrations_text}")

    if not embedding_text_parts:
        return f"Product {product_data.get('id', 'unknown')}"

    return ". ".join(embedding_text_parts)


def get_or_create_product_embedding_new(product_id: str, product_data: Dict[str, Any] = None) -> List[float]:
    """
    Retrieve or generate product embedding using the new product_embeddings collection.
    This is the new version that will replace get_product_embedding after migration.

    Args:
        product_id: The product ID
        product_data: Optional product data dictionary (if not provided, will fetch from products collection)

    Returns:
        List of floats representing the embedding vector

    Raises:
        Exception: If embedding generation fails
    """
    try:
        # First, try to get embedding from the new collection
        existing_embedding = get_product_embedding_from_collection(product_id)
        if existing_embedding:
            return existing_embedding

        # If not found, we need to generate it
        logger.info(f"🔮 No embedding found in product_embeddings collection for {product_id}, generating new one...")

        # Get product data if not provided
        if product_data is None:
            product_doc = db.collection("products").document(product_id).get()
            if not product_doc.exists:
                raise Exception(f"Product {product_id} not found")
            product_data = product_doc.to_dict()

        # Generate embedding text
        embedding_text = generate_embedding_text_from_product(product_data)

        # Generate embedding
        product_embedding = embed_text(embedding_text)

        # Store in the new collection
        metadata = {
            "source_text": embedding_text,
            "generation_method": "openai_text-embedding-3-small"
        }

        success = store_product_embedding_in_collection(product_id, product_embedding, metadata)
        if not success:
            logger.warning(f"⚠️ Failed to store embedding in new collection for product {product_id}")

        return product_embedding

    except Exception as e:
        logger.error(f"❌ Failed to get/generate embedding for product {product_id}: {str(e)}")
        # Return a zero vector as fallback to prevent crashes
        return [0.0] * 1536  # text-embedding-3-small has 1536 dimensions
