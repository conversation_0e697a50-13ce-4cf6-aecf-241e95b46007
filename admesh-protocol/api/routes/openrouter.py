from typing import Dict, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from auth.deps import verify_firebase_token
import requests
import json
import os
import re
import logging
import uuid
from datetime import datetime, timezone
from firebase.config import get_db
from google.cloud import firestore
import random
import time
from pathlib import Path
from urllib.parse import urlparse, urlunparse
from slugify import slugify

ENV = os.getenv("ENV", "development")

router = APIRouter()
db = get_db()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
MAX_FOLLOWUPS = 5

cached_results: Dict[str, dict] = {}
CACHE_TTL_SECONDS = 300


def get_site_url():
    if ENV == "production":
        return os.getenv("PROD_SITE_URL", "https://api.useadmesh.com")
    return os.getenv("SITE_URL", "http://127.0.0.1:8000")


class QueryRequest(BaseModel):
    query: str
    model: str = "mistralai/mistral-7b-instruct"
    agent_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    previous_query: Optional[str] = None
    summary: Optional[str] = None
    followup_suggestions: Optional[str] = None  # Renamed from followup_questions

def build_prompt(query: str, model: str, previous_query: Optional[str], summary: Optional[str]) -> str:
    known_intents = """
Known intent types:
- Compare Products
- Best for Use Case
- Budget-Conscious
- Feature-Specific Requirement
- Integration Needs
- Scalability
- Support & Ease-of-Use
- Security & Privacy
- Trial/Demo First
- Reviews & Social Proof
- Stack Strategy
- Learning Path Intent
- Product Discovery
- Comparative Analysis
- Information Gathering
"""

    keyword_hints = """
# HINTS for intent detection:
- Compare Products → "X vs Y", "which is better", "compare features"
- Best for Use Case → "best tool for", "recommendation for my scenario", "what's ideal for"
- Budget-Conscious → "free", "cheap", "affordable", "low-cost", "under $X"
- Feature-Specific Requirement → "has X feature", "looking for tool that can do Y"
- Integration Needs → "works with", "integrates with", "connects to", "sync with"
- Scalability → "handle large projects", "enterprise-ready", "scale to many users"
- Support & Ease-of-Use → "easy to learn", "support", "customer service", "learning curve"
- Security & Privacy → "safe", "secure", "GDPR", "SOC2", "privacy"
- Trial/Demo First → "trial", "freemium", "test before buying", "try for free"
- Reviews & Social Proof → "what do people think", "ratings", "popular", "user feedback"
- Stack Strategy → "tech stack", "what tools to build with", "architecture advice"
- Learning Path Intent → "how to learn", "courses for", "resources for beginners"
- Information Gathering → "best foods", "benefits of", "how to", "why is", "tips for"
"""

    informational_intents = ["Information Gathering"]

    base_structured = f"""
You are AdMesh, an AI assistant helping users make confident software and tool decisions based on intent and real user decision factors.

{known_intents}

{keyword_hints}

Tasks:
1. Detect the **intent type** from the query. Prefer choosing from the known list unless absolutely necessary.
2. Identify key **decision datapoints** (e.g. pricing, free tier, integrations, UX, support, security).
3. Summarize what the user is trying to achieve.
4. Provide 3 to 5 highly relevant **product/tool recommendations**.
5. For each recommendation, include:
   - title
   - reason
   - url
   - pricing
   - has_free_tier (true/false)
   - trial_days (number of days or 0)
   - features (key highlights)
   - integrations (notable tools/services)
   - support (e.g., ["chat", "email"])
   - security (e.g., ["GDPR", "SOC2"])
   - reviews_summary
   - confidence_score (0 to 1.0)

6. Suggest 2–3 helpful **follow-up suggestions** that guide the user toward clearer decisions or logical next steps.

    ❌ Do NOT ask the user introspective or survey-style questions.
    ✅ Instead, suggest actions that help them evaluate, compare, or discover tools based on their original intent.
    🎯 Examples of helpful follow-up suggestions:

    🆚 Comparisons:
    - “Compare Asana and Wrike based on collaboration and pricing”
    - “See key differences between Trello and Monday.com”

    💼 Use-Case Fit:
    - “Find tools tailored for remote teams or async workflows”
    - “Explore project tools best suited for client management”

    🧩 Feature-Focused:
    - “Explore tools with Gantt charts or timeline views”
    - “Find tools with built-in AI or automation features”

    🔗 Integrations:
    - “See tools that integrate well with Slack and Google Drive”
    - “Compare tools with strong Zapier or Notion integrations”

    🔐 Support & Security:
    - “Find tools with 24/7 support and SOC2 compliance”
    - “Compare user permission and role management features”

    ❌ Avoid these styles:
    - “Would you like more info on Asana or Wrike?”
    - “Are you looking for any specific features?”
    ⚠️ DO NOT ask users if they “want more info” or “have any features in mind.” Replace questions with direct discovery prompts that assume user intent and drive next steps.
    Common mistakes to avoid:
    - Starting follow-up suggestions with "Would you like", "Are you", "Do you want"
    - Ending with question marks (?)
    - Asking for user preferences or opinions
    - Using vague or generic phrases like "any specific features" or "more info"
    - Asking if they want to "see" or "compare" something
    - Using "if" statements to suggest follow-ups
    - Asking for "any other questions" or "anything else
7. Provide a **Final Verdict** — pick one tool that best fits the user's goal and explain *why*.

Output strictly in the following JSON format:
{{
  "intent": {{
    "type": "...",
    "goal": [...],
    "known_mentions": [...],
    "category": "..."
  }},
  "decision_factors": {{
    "highlighted": [...],
    "reasoning": "..."
  }},
  "response": {{
    "summary": "...",
    "recommendations": [...],
    "followup_suggestions": [...],
    "final_verdict": "..."
  }},
  "model_used": "{model}",
  "tokens_used": 800
}}

DO NOT explain anything outside the JSON.
Only return valid JSON — no markdown, no prose.
"""

    base_free_form = """
You are AdMesh, an AI assistant that provides clear, concise, and helpful information based on user queries.

The user query is informational. Provide a detailed, engaging, and easy-to-understand response without structured recommendations. Include practical tips or actionable advice relevant to the query.

Avoid any structured JSON format, markdown, or metadata.

Simply answer the question in plain text.
"""

    # Basic info intent check
    is_informational = any(
        hint in query.lower()
        for hint in ["best foods", "benefits of", "how to", "why is", "tips for"]
    )

    if is_informational:
        return f"""{base_free_form}
User query: "{query}"
"""

    if previous_query and summary:
        return f"""{base_structured}

Original query: "{previous_query}"
Summary: "{summary}"
Follow-up question: "{query}"
"""

    return f"""{base_structured}
User query: "{query}"
"""

def clean_response(raw: str) -> str:
    raw = raw.strip()
    if raw.startswith("```json"):
        raw = raw[7:]
    if raw.endswith("```"):
        raw = raw[:-3]

    start = raw.find("{")
    end = raw.rfind("}")
    if start != -1 and end != -1:
        json_str = raw[start:end+1]
        try:
            json.loads(json_str)
            return json_str  # ✅ Valid JSON block
        except Exception:
            logger.warning("❌ Failed JSON parse – falling back to raw")
            logger.debug(f"🧪 Raw content:\n{json_str}")

    return raw  # ❌ Not JSON, return plain text fallback

def call_openrouter(prompt: str, model: str) -> str:
    headers = {
        "Authorization": f"Bearer {os.getenv('OPENROUTER_API_KEY')}",
        "Content-Type": "application/json",
        "HTTP-Referer": get_site_url(),  # Optional but good for attribution
        "X-Title": os.getenv("SITE_TITLE", "AdMesh")
    }

    # Construct message with new format (content block style)
    payload = {
        "model": model,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    }
                    # Optionally add an image block like:
                    # {
                    #     "type": "image_url",
                    #     "image_url": {
                    #         "url": "https://example.com/image.jpg"
                    #     }
                    # }
                ]
            }
        ]
    }

    try:
        res = requests.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload)
        res.raise_for_status()
        return res.json()["choices"][0]["message"]["content"]
    except requests.exceptions.HTTPError as e:
        print(f"❌ OpenRouter API Error: {e} - Status {res.status_code}")
        print(f"Response text: {res.text}")
        raise

# def create_admesh_link(url: str, title: str, offer_id: Optional[str] = None) -> str:
#     base = "https://api.useadmesh.com/redirect"
#     slug = slugify(title)
#     return f"{base}?to={quote_plus(url)}&source={slug}" + (f"&offer_id={offer_id}" if offer_id else "")

# ✅ URL normalization function
def normalize_url(raw_url: str) -> str:
    parsed = urlparse(raw_url.strip())
    scheme = parsed.scheme or "https"
    netloc = parsed.netloc.lower().replace("www.", "")
    path = parsed.path.rstrip("/")
    return urlunparse((scheme, netloc, path, '', '', ''))

# ✅ Slug generation from title
def generate_slug(title: str) -> str:
    return slugify(title)

# ✅ Upsert product logic
def upsert_product(product: dict) -> tuple[str, dict]:
    ref = db.collection("products")

    # Get the original title and a lowercase version for comparison
    original_title = product.get("title", "")
    title_lower = original_title.lower()

    # Generate slug and normalized URL
    slug = generate_slug(title_lower)  # slugify already makes it lowercase
    normalized_url = normalize_url(product.get("url", ""))

    # Set the slug in the product dict
    product["slug"] = slug
    product["normalized_url"] = normalized_url

    # 🔍 Step 1: Try to find existing product by title
    # First try an exact match on the lowercase slug (most efficient)
    existing = ref.where("slug", "==", slug).limit(1).stream()
    for doc in existing:
        doc_ref = ref.document(doc.id)
        doc_data = doc.to_dict()

        # Get the redirect_url from the database if it exists
        if "url" in doc_data:
            product["redirect_url"] = doc_data["url"]
            logger.info(f"✅ Found redirect_url in database: {doc_data['url']}")

        doc_ref.update({
            "view_count": firestore.Increment(1),
            "last_viewed_at": firestore.SERVER_TIMESTAMP
        })
        logger.info(f"✅ Updated existing product: {doc.id} with slug: {slug}")
        return doc.id, doc_data

    # 🆕 Step 2: Create new product
    doc_id = str(uuid.uuid4())

    # Set redirect_url to the product URL
    product["redirect_url"] = product.get("url", "")

    new_data = {
        **product,
        "id": doc_id,
        "view_count": 1,
        "clicks": 0,
        "conversions": 0,
        "created_at": firestore.SERVER_TIMESTAMP,
        "last_viewed_at": firestore.SERVER_TIMESTAMP,
        "active_offers": [],
        "inactive_offers": [],
        "status": "active"
    }
    ref.document(doc_id).set(new_data)

    logger.info(f"✅ Created new product: {doc_id} with title: {original_title}")
    return doc_id, new_data


def find_active_offer(product_id: str) -> Optional[dict]:
    ref = db.collection("offers")
    for doc in ref.where("product_id", "==", product_id).where("offer_status", "==", "active").limit(1).stream():
        return {"offer_id": doc.id, **doc.to_dict()}
    return None


def save_query_session(query, intent, recs, model, agent_id, user_id, session_id) -> str:
    query_normalized = query.strip().lower()
    recommendation_ids = [r.get("id") for r in recs if r.get("id")]
    queries_ref = db.collection("queries")
    sessions_ref = db.collection("sessions")

    # Try to find any existing query matching the same string
    existing_match = None
    for doc in queries_ref.where("query_normalized", "==", query_normalized).limit(1).stream():
        existing_match = doc

    record = {
        "query": query.strip(),
        "query_normalized": query_normalized,
        "intent": intent,
        "recommendation_ids": recommendation_ids,
        "model_used": model,
        "agent_id": agent_id,
        "user_id": user_id,
        "session_id": session_id,
        "timestamp": safe_timestamp()  # Use safe_timestamp instead of SERVER_TIMESTAMP for arrays
    }

    if existing_match:
        doc_ref = queries_ref.document(existing_match.id)
        logger.info(f"🔁 Updating existing query: {query}")
        doc_ref.update({
            "last_used_at": firestore.SERVER_TIMESTAMP,
            "usages": firestore.ArrayUnion([record])
        })
    else:
        new_id = str(uuid.uuid4())
        logger.info(f"🆕 Creating new query entry: {query}")
            # Create the base doc first (no nested timestamp)
        queries_ref.document(new_id).set({
            "query": query.strip(),
            "query_normalized": query_normalized,
            "usages": [],  # Empty array first
        })

        # Then update timestamps and append the usage with SERVER_TIMESTAMP
        queries_ref.document(new_id).update({
            "usages": firestore.ArrayUnion([record])
        })

        # Then update with timestamps
        queries_ref.document(new_id).update({
            "created_at": firestore.SERVER_TIMESTAMP,
            "last_used_at": firestore.SERVER_TIMESTAMP
        })


    # ⬇️ Update session document to store all queries in order
    if session_id:
        session_doc = sessions_ref.document(session_id).get()
        if session_doc.exists:
            # Update existing session
            # First update the queries array with a regular timestamp
            sessions_ref.document(session_id).update({
                "queries": firestore.ArrayUnion([{
                    "query": query.strip(),
                    "timestamp": safe_timestamp()
                }])
            })

            sessions_ref.document(session_id).update({
            "last_updated": firestore.SERVER_TIMESTAMP
        })
        else:
            # This should not happen, but just in case
            logger.warning(f"Session {session_id} not found when saving query")

    return existing_match.id if existing_match else new_id



@router.post("/recommend")
async def recommend(request: QueryRequest):
    logger.info(f"🔍 New request: {request}")

    if request.user_id and not request.user_id.startswith("user_"):
        user_doc = db.collection("users").document(request.user_id).get()
        print(f"User Document: {user_doc.to_dict()}")
        if not user_doc.exists:
            return {"error": "User not found"}
        user_data = user_doc.to_dict()
        current_credits = user_data.get("credits", 0)
        if current_credits <= 0:
            return {
                "response": {
                    "summary": "🛑 You’ve run out of credits. Complete tasks or purchase more to continue.",
                    "recommendations": [],
                    "followup_suggestions": []
                },
                "session_id": request.session_id or None,
                "end_of_session": True
            }

    # Check if this is a follow-up query based on session_id
    context, end_of_session = None, False
    sessions_ref = db.collection("sessions")

    # If session_id is provided, check if it exists in the database
    if request.session_id:
        doc = sessions_ref.document(request.session_id).get()
        if doc.exists:
            # This is a follow-up query
            data = doc.to_dict()
            if data.get("followup_count", 0) >= MAX_FOLLOWUPS:
                return {
                    "response": {
                        "summary": "🛑 You've reached the maximum number of follow-ups. Please start a new session to continue.",
                        "recommendations": [],
                        "followup_suggestions": []
                    },
                    "session_id": request.session_id,
                    "end_of_session": True
                }
            # Get the initial query and summary for context
            context = {
                "initial_query": data.get("initial_query"),
                "initial_summary": data.get("initial_summary")
            }
            # Handle as a follow-up query
            return await handle_followup_recommendation(request)

    # For initial queries, use the standard prompt
    prompt = build_prompt(
        request.query,
        request.model,
        None,  # No previous query for initial queries
        None   # No summary for initial queries
    )

    try:
        raw = call_openrouter(prompt, request.model)
        print("🧠 LLM Response (raw):", repr(raw))  
        cleaned = clean_response(raw)

        # Check if the response is valid JSON and contains valid recommendations
        try:
            result_json = json.loads(cleaned)
            recs = result_json.get("response", {}).get("recommendations", [])
            if not isinstance(recs, list) or not all(isinstance(r, dict) for r in recs):
                raise ValueError("Recommendations are not valid structured data")
        except Exception:
            logger.warning("⚠️ Free-form response fallback")
            return {
                "response": {
                    "summary": cleaned.strip(),
                    "recommendations": [],
                    "followup_suggestions": []
                },
                "session_id": request.session_id or None,
                "end_of_session": True
            }

        # Early return after free-form fallback
        if not isinstance(recs, list) or not all(isinstance(r, dict) for r in recs):
            logger.warning("⚠️ Invalid recommendations format. Skipping product creation.")
            return {
                "response": {
                    "summary": result_json.get("response", {}).get("summary", ""),
                    "recommendations": [],
                    "followup_suggestions": result_json.get("response", {}).get("followup_suggestions", [])
                },
                "session_id": request.session_id or None,
                "end_of_session": True
            }

        # Proceed with structured JSON response
        KNOWN_INTENT_TYPES = {
            "Compare Products", "Best for Use Case", "Budget-Conscious", "Feature-Specific Requirement",
            "Integration Needs", "Scalability", "Support & Ease-of-Use", "Security & Privacy",
            "Trial/Demo First", "Reviews & Social Proof", "Stack Strategy", "Learning Path Intent",
            "Product Discovery"  # Added this
        }

        intent_type = result_json["intent"]["type"]

        if intent_type not in KNOWN_INTENT_TYPES:
            intent_id = str(uuid.uuid4())
            # Create the document with basic fields first
            db.collection("intent_types").document(intent_id).set({
                "id": intent_id,
                "name": intent_type,
                "goal_examples": result_json["intent"].get("goal", []),
                "known_mentions": result_json["intent"].get("known_mentions", []),
                "category": result_json["intent"].get("category", ""),
                "reasoning": result_json.get("decision_factors", {}).get("reasoning", ""),
                "auto_detected": True,
                "reviewed": False
            })

            # Then update with the timestamp
            db.collection("intent_types").document(intent_id).update({
                "created_at": firestore.SERVER_TIMESTAMP
            })

        enriched, ids, recommendation_mappings = [], [], []

        for i, product in enumerate(recs):
            # Skip invalid product entries
            if not isinstance(product, dict) or "url" not in product or "title" not in product:
                logger.warning(f"⚠️ Skipping invalid product at index {i}: {product}")
                continue

            pid, _ = upsert_product(product)
            print(f"Product ID: {pid}")
            ids.append(pid)
            # Check if the product has an active offer
            # and create an admesh link
            # if not, create a new ad_id
            # and create an admesh link

            offer = find_active_offer(pid)
            print(f"Offer: {offer}")

            # Get the redirect_url from the product
            redirect_url = product.get("redirect_url", product.get("url", ""))

            from api.routes.click import build_admesh_link

            if offer:
                print(f"Found active offer: {offer['offer_id']}")
                # Use the utility function to build the admesh_link
                admesh_link = build_admesh_link(
                    ad_id=offer['offer_id'],
                    product_id=pid,
                    redirect_url=redirect_url,
                    # We don't include rec_id, session_id, agent_id, or user_id here
                    # as these will be added by the frontend when needed
                )
                product["offer_id"] = offer["offer_id"]
                product["reward_note"] = offer.get("reward_note")
                product["admesh_link"] = admesh_link
                recommendation_mappings.append({
                    "product_id": pid,
                    "offer_id": offer["offer_id"],
                    "admesh_link": product["admesh_link"],
                    "title": product["title"],
                    "url": product["url"],
                    "redirect_url": redirect_url
                })
            else:
                print("No active offer found.")
                ad_id = str(uuid.uuid4())
                # Use the utility function to build the admesh_link
                admesh_link = build_admesh_link(
                    ad_id=ad_id,
                    product_id=pid,
                    redirect_url=redirect_url,
                    # We don't include rec_id, session_id, agent_id, or user_id here
                    # as these will be added by the frontend when needed
                )
                product["ad_id"] = ad_id
                product["admesh_link"] = admesh_link
                recommendation_mappings.append({
                    "product_id": pid,
                    "ad_id": ad_id,
                    "admesh_link": product["admesh_link"],
                    "title": product["title"],
                    "url": product["url"],
                    "redirect_url": redirect_url
                })

              # Add admesh_link to the product
            enriched.append(product)

        # Generate a unique recommendation ID
        recommendation_id = str(uuid.uuid4())
        db.collection("recommendations").add({
            "recommendation_id": recommendation_id,
            "session_id": request.session_id,
            "agent_id": request.agent_id,
            "user_id": request.user_id,
            "query": request.query,
            "products": recommendation_mappings,
            "created_at": firestore.SERVER_TIMESTAMP
        })

        # Add recommendation_id at the parent level in the response
        result_json["recommendation_id"] = recommendation_id
        result_json["response"]["recommendations"] = enriched

        # Extract final_verdict from the result JSON
        final_verdict = result_json["response"].get("final_verdict", "")
        result_json["response"]["final_verdict"] = final_verdict  # Add final_verdict to the response

        summary = result_json["response"]["summary"]

        # For initial queries, create or update the session document
        # The session_id should always be provided by the frontend
        if not request.session_id:
            # This should not happen, but just in case
            logger.warning("No session_id provided for initial query")
            request.session_id = str(uuid.uuid4())

        # Check if the session already exists
        session_doc = sessions_ref.document(request.session_id).get()
        if not session_doc.exists:
            # Create a new session document for initial query
            # First create the document with basic fields (without nested timestamps)
            sessions_ref.document(request.session_id).set({
                "agent_id": request.agent_id,
                "user_id": request.user_id,
                "initial_query": request.query,
                "initial_summary": summary,
                "followup_count": 0,
                "product_ids": ids,
                "user_type": "anonymous" if request.user_id and request.user_id.startswith("user_") else "authenticated",
                "queries": []  # Empty array for now
            })

            # Then add the query with timestamp via update
            # Use safe_timestamp() for timestamps in arrays and nested structures
            sessions_ref.document(request.session_id).update({
                "queries": firestore.ArrayUnion([{
                    "query": request.query.strip(),
                    "timestamp": safe_timestamp()
                }]),
                "created_at": firestore.SERVER_TIMESTAMP,
                "last_updated": firestore.SERVER_TIMESTAMP
            })
        elif context:
            session_doc = sessions_ref.document(request.session_id).get()
            existing_product_ids = session_doc.to_dict().get("product_ids", [])
            unique_product_ids = list(set(existing_product_ids + ids))
            sessions_ref.document(request.session_id).update({
                "followup_count": firestore.Increment(1),
                "product_ids": unique_product_ids
            })
            end_of_session = True

        save_query_session(
            request.query,
            result_json["intent"],
            enriched,
            request.model,
            request.agent_id,
            request.user_id,
            request.session_id
        )

        db.collection("recommendations").add({
            "session_id": request.session_id,
            "agent_id": request.agent_id,
            "user_id": request.user_id,
            "query": request.query,
            "products": recommendation_mappings,
            "created_at": firestore.SERVER_TIMESTAMP
        })

        if request.user_id and not request.user_id.startswith("user_"):
            deduct_credit(request.user_id, 1)
        print(f"Deducted credit for user {result_json}")
        return {
            **result_json,
            "session_id": request.session_id,
            "rec_product_ids": ids,
            "recommendation_id": recommendation_id,
            "end_of_session": end_of_session
        }

    except Exception as e:
        logger.exception("🔥 Error during /recommend")
        return {
            "error": str(e),
            "raw": raw if "raw" in locals() else ""
        }


def safe_timestamp():
    """Return a safe timestamp for use in Firestore arrays and nested structures.
    Use this instead of firestore.SERVER_TIMESTAMP when adding timestamps to arrays or nested objects.
    """
    return datetime.now(timezone.utc)


def extract_json_array(text: str) -> str:
    match = re.search(r'\[.*\]', text, re.DOTALL)
    if not match:
        raise ValueError("No JSON array found in model output")
    return match.group(0)

async def handle_followup_recommendation(request: QueryRequest):
    # Validate that session_id is provided
    if not request.session_id:
        return {
            "response": {
                "summary": "🛑 No session ID provided. Please start a new session.",
                "recommendations": [],
                "followup_suggestions": []
            },
            "end_of_session": True,
            "response_format": "free-form"
        }

    # Get the session document
    context_doc = db.collection("sessions").document(request.session_id).get()
    if not context_doc.exists:
        return {
            "response": {
                "summary": "🛑 Previous session not found. Please start a new session.",
                "recommendations": [],
                "followup_suggestions": []
            },
            "end_of_session": True,
            "response_format": "free-form"
        }

    context = context_doc.to_dict()
    previous_query = request.previous_query or context.get("initial_query", "")
    summary = request.summary or context.get("initial_summary", "")

    prompt = build_followup_prompt(
        followup=request.query,
        previous_query=previous_query,
        summary=summary,
        model=request.model
    )

    try:
        raw = call_openrouter(prompt, request.model)
        cleaned = clean_response(raw)

        try:
            result_json = json.loads(cleaned)
        except json.JSONDecodeError:
            logger.warning("⚠️ Follow-up response not valid JSON.")
            if request.user_id and not request.user_id.startswith("user_"):
                deduct_credit(request.user_id, 1)
            return {
                "response": {
                    "summary": cleaned.strip(),
                    "recommendations": [],
                    "followup_suggestions": []
                },
                "session_id": request.session_id,
                "end_of_session": True,
                "response_format": "free-form"
            }

        intent_type = result_json["intent"]["type"]
        recs = result_json["response"].get("recommendations", [])
        if not isinstance(recs, list) or not all(isinstance(r, dict) for r in recs):
            logger.warning("⚠️ Follow-up returned malformed recommendations — downgrading to free-form.")
            if request.user_id and not request.user_id.startswith("user_"):
                deduct_credit(request.user_id, 1)
            return {
                "response": {
                    "summary": cleaned.strip(),
                    "recommendations": [],
                    "followup_suggestions": []
                },
                "session_id": request.session_id,
                "end_of_session": True,
                "response_format": "free-form"
            }

        KNOWN_INTENT_TYPES = {
            "Compare Products", "Best for Use Case", "Budget-Conscious", "Feature-Specific Requirement",
            "Integration Needs", "Scalability", "Support & Ease-of-Use", "Security & Privacy",
            "Trial/Demo First", "Reviews & Social Proof", "Stack Strategy", "Learning Path Intent",
            "Comparative Analysis", "Product Discovery"
        }

        if intent_type not in KNOWN_INTENT_TYPES:
            logger.warning(f"⚠️ Unknown intent '{intent_type}' — logging for review.")
            intent_id = str(uuid.uuid4())
            # Create the document with basic fields first
            db.collection("intent_types").document(intent_id).set({
                "id": intent_id,
                "name": intent_type,
                "goal_examples": result_json["intent"].get("goal", []),
                "known_mentions": result_json["intent"].get("known_mentions", []),
                "category": result_json["intent"].get("category", ""),
                "reasoning": result_json.get("decision_factors", {}).get("reasoning", ""),
                "auto_detected": True,
                "reviewed": False
            })

            # Then update with the timestamp
            db.collection("intent_types").document(intent_id).update({
                "created_at": firestore.SERVER_TIMESTAMP
            })

        response_format = "structured"
        enriched, ids, recommendation_mappings = [], [], []

        for i, product in enumerate(recs):
            if not isinstance(product, dict):
                logger.warning(f"⚠️ Skipping product {i}, invalid format: {product}")
                continue

            pid, _ = upsert_product(product)
            ids.append(pid)
            offer = find_active_offer(pid)

            # Get the redirect_url from the product
            redirect_url = product.get("redirect_url", product.get("url", ""))

            from api.routes.click import build_admesh_link

            if offer:
                # Use the utility function to build the admesh_link
                admesh_link = build_admesh_link(
                    ad_id=offer['offer_id'],
                    product_id=pid,
                    redirect_url=redirect_url,
                    # We don't include rec_id, session_id, agent_id, or user_id here
                    # as these will be added by the frontend when needed
                )
                product["offer_id"] = offer["offer_id"]
                product["reward_note"] = offer.get("reward_note")
                recommendation_mappings.append({
                    "product_id": pid,
                    "offer_id": offer["offer_id"],
                    "admesh_link": admesh_link,
                    "title": product["title"],
                    "url": product["url"],
                    "redirect_url": redirect_url
                })
            else:
                ad_id = str(uuid.uuid4())
                # Use the utility function to build the admesh_link
                admesh_link = build_admesh_link(
                    ad_id=ad_id,
                    product_id=pid,
                    redirect_url=redirect_url,
                    # We don't include rec_id, session_id, agent_id, or user_id here
                    # as these will be added by the frontend when needed
                )
                product["ad_id"] = ad_id
                recommendation_mappings.append({
                    "product_id": pid,
                    "ad_id": ad_id,
                    "admesh_link": admesh_link,
                    "title": product["title"],
                    "url": product["url"],
                    "redirect_url": redirect_url
                })

            product["admesh_link"] = admesh_link
            enriched.append(product)

        # Update session doc - first update the count and product IDs
        update_data = {
            "followup_count": firestore.Increment(1)
        }
        if ids:
            update_data["product_ids"] = firestore.ArrayUnion(ids)

        db.collection("sessions").document(request.session_id).update(update_data)

        # Then update the timestamp separately
        db.collection("sessions").document(request.session_id).update({
            "last_updated": firestore.SERVER_TIMESTAMP
        })

        save_query_session(
            query=request.query,
            intent=result_json["intent"],
            recs=enriched,
            model=request.model,
            agent_id=request.agent_id,
            user_id=request.user_id,
            session_id=request.session_id
        )

        recommendation_id = str(uuid.uuid4())
        db.collection("recommendations").add({
            "recommendation_id": recommendation_id,
            "session_id": request.session_id,
            "agent_id": request.agent_id,
            "user_id": request.user_id,
            "query": request.query,
            "products": recommendation_mappings,
            "created_at": firestore.SERVER_TIMESTAMP
        })

        for product in enriched:
            product["recommendation_id"] = recommendation_id

        result_json["recommendation_id"] = recommendation_id
        result_json["response"]["recommendations"] = enriched

        if request.user_id and not request.user_id.startswith("user_"):
            deduct_credit(request.user_id, 1)
        print(f"Deducted credit for user {result_json}")
        return {
            **result_json,
            "session_id": request.session_id,
            "recommendation_ids": ids,
            "end_of_session": True,
            "response_format": response_format
        }

    except Exception as e:
        logger.exception("🔥 Error during follow-up recommendation")
        return {
            "error": str(e),
            "raw": raw if "raw" in locals() else ""
        }

def build_followup_prompt(followup: str, previous_query: str, summary: str, model: str) -> str:
    return f"""
You are AdMesh, an intelligent assistant that helps users make confident software and tool decisions. You're continuing an ongoing conversation.

Context:
- Previous Query: "{previous_query}"
- Summary of Intent: "{summary}"
- Follow-up Question: "{followup}"

---

Instructions:

🔹 If the follow-up is about finding more product/tool recommendations, comparing tools, or exploring alternatives:
→ Respond ONLY with a valid JSON object in the strict format below.

❌ DO NOT wrap your response in markdown, backticks, or code blocks.
✅ Return a clean, valid JSON object only.

🔹 If the user is asking something unrelated to product discovery (e.g., learning resources, abstract advice, opinions, strategy, or definitions):
→ Respond naturally in plain text — like ChatGPT — with helpful, conversational answers.
❌ DO NOT use JSON in this case.

Never say you're an AI or language model.
Only return valid JSON if you are confident.
If unsure, respond with plain text. NEVER return partial or broken JSON.

---

📌 Task Breakdown:
1. Detect the **intent type** from the follow-up.
2. Identify the user’s **goal** and relevant **decision factors**.
3. Provide 3 to 5 relevant product/tool **recommendations**.
4. Include all details: title, reason, URL, pricing, features, integrations, etc.
5. DO NOT ask vague or survey-style questions. Suggest focused next actions.
6. Suggest 2–3 helpful **follow-up suggestions** that guide the user toward clearer decisions or logical next steps.

    ❌ Do NOT ask the user introspective or survey-style questions.
    ✅ Instead, suggest actions that help them evaluate, compare, or discover tools based on their original intent.
    🎯 Examples of helpful follow-up suggestions:

    🆚 Comparisons:
    - “Compare Asana and Wrike based on collaboration and pricing”
    - “See key differences between Trello and Monday.com”

    💼 Use-Case Fit:
    - “Find tools tailored for remote teams or async workflows”
    - “Explore project tools best suited for client management”

    🧩 Feature-Focused:
    - “Explore tools with Gantt charts or timeline views”
    - “Find tools with built-in AI or automation features”

    🔗 Integrations:
    - “See tools that integrate well with Slack and Google Drive”
    - “Compare tools with strong Zapier or Notion integrations”

    🔐 Support & Security:
    - “Find tools with 24/7 support and SOC2 compliance”
    - “Compare user permission and role management features”

    ❌ Avoid these styles:
    - “Would you like more info on Asana or Wrike?”
    - “Are you looking for any specific features?”
    ⚠️ DO NOT ask users if they “want more info” or “have any features in mind.” Replace questions with direct discovery prompts that assume user intent and drive next steps.
    Common mistakes to avoid:
    - Starting follow-up suggestions with "Would you like", "Are you", "Do you want"
    - Ending with question marks (?)
    - Asking for user preferences or opinions
    - Using vague or generic phrases like "any specific features" or "more info"
    - Asking if they want to "see" or "compare" something
    - Using "if" statements to suggest follow-ups
    - Asking for "any other questions" or "anything else
7. Include a **final_verdict** string that clearly names the best tool, and explains why it’s the most suitable for the user — based on price, features, integration, support, etc.

🎯 Example of a strong final_verdict:
> **Final Verdict:** *Make* offers more powerful workflow logic and better value for advanced users compared to Zapier, making it the ideal choice for technical teams or complex automation needs.

---

✅ JSON RESPONSE FORMAT (use only when relevant):
{{
  "intent": {{
    "type": "...",
    "goal": [...],
    "category": "...",
    "known_mentions": [...]
  }},
  "decision_factors": {{
    "highlighted": [...],
    "reasoning": "..."
  }},
  "response": {{
    "summary": "...",
    "recommendations": [
      {{
        "title": "...",
        "reason": "...",
        "url": "...",
        "pricing": "...",
        "has_free_tier": true,
        "trial_days": 14,
        "features": ["...", "..."],
        "integrations": ["...", "..."],
        "support": ["email", "chat"],
        "security": ["GDPR", "SOC2"],
        "reviews_summary": "...",
        "confidence_score": 0.92
      }}
    ],
    "followup_suggestions": ["...", "..."],
    "final_verdict": "..."
  }},
  "model_used": "{model}",
  "tokens_used": 800
}}
""".strip()

def deduct_credit(user_id: str, amount: int = 1) -> bool:
    """
    Deducts credits from the user and logs the equivalent pending credits
    in both `payments` and `users` collection (as `pending_total`).
    """
    if not user_id or user_id.startswith("user_"):
        return False

    user_ref = db.collection("users").document(user_id)
    user_doc = user_ref.get()

    if not user_doc.exists:
        return False

    current_credits = user_doc.to_dict().get("credits", 0)
    if current_credits < amount:
        return False

    # Step 1: Deduct credits from user - first update the credits
    user_ref.update({
        "credits": firestore.Increment(-amount),
        "pending_total": firestore.Increment(amount)  # 👈 add this
    })

    # Then update the timestamp separately
    user_ref.update({
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    # Step 2: Update payments doc
    payments_ref = db.collection("payments").document(user_id)
    payments_doc = payments_ref.get()

    # Use safe_timestamp for nested objects in Firestore
    spend_log = {
        "id": f"spend-{int(time.time())}",
        "credits": amount,
        "type": "query",
        "status": "pending",
        "created_at": safe_timestamp()  # Use safe_timestamp for consistency with other parts of the codebase
    }

    if not payments_doc.exists:
        # First create the document with basic fields (without SERVER_TIMESTAMP)
        payments_ref.set({
            "uid": user_id,
            "confirmed_credits": 0,
            "pending_credits": amount,
            "credit_spends": [spend_log]
        })

        # Then update with timestamps
        payments_ref.update({
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        })
    else:
        # First update the pending_credits and credit_spends
        payments_ref.update({
            "pending_credits": firestore.Increment(amount),
            "credit_spends": firestore.ArrayUnion([spend_log])
        })

        # Then update the timestamp separately
        payments_ref.update({
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    return True


PROMPT_PATH = Path(__file__).parent / "tool_discovery_prompts.json"


@router.get("/suggest")
async def get_dynamic_suggestions():
    try:
        with open(PROMPT_PATH, "r") as f:
            all_suggestions = json.load(f)
            suggestions = random.sample(all_suggestions, k=5)
            return {"suggestions": suggestions}
    except Exception as e:
        logger.exception("❌ Failed to load fallback suggestions")
        fallback = [
            "Best AI tools for solo founders",
            "Compare free vs paid CRM platforms",
            "SaaS tools with no-code integration",
            "Find open-source design tools",
            "Tools that work well with Notion",
        ]
        return {"suggestions": fallback, "error": str(e)}

class ChatRequest(BaseModel):
    prompt: str
    model: str = "openai/gpt-4"
    temperature: float = 0.7
    max_tokens: int = 1000

@router.post("/chat")
async def chat_with_openrouter(
    request: ChatRequest,
    user_data = Depends(verify_firebase_token)
):
    """Chat endpoint for conversational interfaces"""
    try:
        headers = {
            "Authorization": f"Bearer {os.getenv('OPENROUTER_API_KEY')}",
            "Content-Type": "application/json",
            "HTTP-Referer": get_site_url(),
            "X-Title": os.getenv("SITE_TITLE", "AdMesh")
        }

        payload = {
            "model": request.model,
            "messages": [
                {
                    "role": "user",
                    "content": request.prompt
                }
            ],
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }

        response = requests.post(
            "https://openrouter.ai/api/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        response.raise_for_status()

        result = response.json()
        content = result["choices"][0]["message"]["content"]

        return {
            "success": True,
            "response": content,
            "model": request.model,
            "usage": result.get("usage", {})
        }

    except requests.exceptions.RequestException as e:
        logger.error(f"OpenRouter chat API error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chat API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in chat: {str(e)}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")
