from typing import Dict, Any
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from firebase_admin import firestore
from firebase.config import get_db
from auth.api_key import verify_api_key_from_request
import time
import logging
import uuid
import json
import hashlib
from datetime import datetime, timedelta
from api.routes.click import build_admesh_link
from api.routes.openrouter import call_openrouter, clean_response
from api.utils.embedding import embed_text, cosine_similarity, get_product_embedding

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

# Default model to use for all LLM calls
DEFAULT_MODEL = "mistralai/mistral-7b-instruct"

# Query embedding cache settings
EMBEDDING_CACHE_TTL_HOURS = 24  # Cache embeddings for 24 hours
EMBEDDING_SIMILARITY_THRESHOLD = 0.95  # Reuse embeddings if similarity > 95%

# ------------------------
# Query Embedding Caching Functions
# ------------------------

def normalize_query_for_caching(query: str) -> str:
    """
    Normalize a query for better cache matching by removing noise and standardizing format.

    Args:
        query: The raw user query

    Returns:
        Normalized query string for cache key generation
    """
    import re

    # Convert to lowercase and strip whitespace
    normalized = query.lower().strip()

    # Remove special characters and contractions first
    normalized = re.sub(r"'s\b", '', normalized)  # Remove possessive 's
    normalized = re.sub(r"'re\b", '', normalized)  # Remove contractions
    normalized = re.sub(r"'t\b", '', normalized)   # Remove contractions
    normalized = re.sub(r'[^\w\s\-]', ' ', normalized)  # Replace punctuation with spaces

    # Remove common noise words that don't affect semantic meaning
    noise_words = {
        'please', 'help', 'me', 'find', 'looking', 'for', 'need', 'want', 'can', 'you',
        'show', 'tell', 'what', 'which', 'how', 'best', 'good', 'great', 'top', 'recommend',
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'of', 'with', 'by',
        'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above',
        'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
        'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
        'may', 'might', 'must', 'shall', 'thats', 'whats', 'hows', 'wheres', 'whos'
    }

    # Split into words and filter out noise and very short words
    words = normalized.split()
    meaningful_words = []

    for word in words:
        word = word.strip()
        if len(word) > 2 and word not in noise_words:
            meaningful_words.append(word)

    # Keep original word order for phrases that matter (don't sort)
    # This preserves semantic meaning like "project management" vs "management project"
    normalized = ' '.join(meaningful_words)

    # Collapse multiple spaces
    normalized = re.sub(r'\s+', ' ', normalized).strip()

    return normalized


def get_cached_embedding(query: str) -> Optional[List[float]]:
    """
    Check if a semantically similar query embedding exists in cache using cosine similarity.

    This approach:
    1. First tries exact normalized text match (fastest)
    2. If no exact match, generates a temporary embedding for the query
    3. Compares this embedding with cached embeddings using cosine similarity
    4. Returns cached embedding if similarity > threshold

    Args:
        query: The user's query text

    Returns:
        Cached embedding vector if found and similar enough, None otherwise
    """
    try:
        # Normalize the query for better matching
        normalized_query = normalize_query_for_caching(query)

        if len(normalized_query) < 3:  # Skip very short queries
            logger.info(f"⚠️ Query too short for caching: {query[:50]}...")
            return None

        # Create a hash-based lookup for exact normalized matches
        query_hash = hashlib.md5(normalized_query.encode()).hexdigest()

        # First, try exact normalized match using hash (fastest path)
        try:
            exact_match_doc = db.collection("query_embedding_cache").document(query_hash).get()
            if exact_match_doc.exists:
                cache_data = exact_match_doc.to_dict()

                # Check if cache entry is still valid (within TTL)
                created_at = cache_data.get("created_at")
                if created_at:
                    # Convert Firestore timestamp to datetime for comparison
                    if hasattr(created_at, 'seconds'):
                        created_datetime = datetime.fromtimestamp(created_at.seconds)
                    else:
                        created_datetime = created_at

                    cutoff_time = datetime.now() - timedelta(hours=EMBEDDING_CACHE_TTL_HOURS)

                    if created_datetime >= cutoff_time:
                        cached_embedding = cache_data.get("embedding")
                        if cached_embedding:
                            # Update last_used timestamp for LRU-like behavior
                            try:
                                exact_match_doc.reference.update({
                                    "last_used": firestore.SERVER_TIMESTAMP,
                                    "usage_count": firestore.Increment(1)
                                })
                            except Exception as update_error:
                                logger.debug(f"Failed to update cache usage: {str(update_error)}")

                            logger.info(f"🎯 Found exact normalized match in cache: {normalized_query[:50]}...")
                            return cached_embedding
                    else:
                        logger.info(f"⏰ Cache entry expired for query: {normalized_query[:50]}...")
        except Exception as e:
            logger.debug(f"No exact match found: {str(e)}")

        # If no exact match, use cosine similarity with actual embeddings
        logger.info(f"🔍 No exact match found, checking semantic similarity using embeddings...")

        # Generate embedding for the current query to compare against cached ones
        query_embedding = embed_text(query)
        logger.info(f"🔮 Generated temporary embedding for similarity comparison")

        cache_ref = db.collection("query_embedding_cache")
        cutoff_time = datetime.now() - timedelta(hours=EMBEDDING_CACHE_TTL_HOURS)

        # Get recent cache entries for comparison
        recent_entries = cache_ref.where("created_at", ">=", cutoff_time).limit(100).stream()

        best_similarity = 0.0
        best_embedding = None
        best_cache_doc = None

        for doc in recent_entries:
            cache_data = doc.to_dict()
            cached_embedding = cache_data.get("embedding")

            if not cached_embedding:
                continue

            # Calculate cosine similarity between embeddings
            try:
                similarity = cosine_similarity(query_embedding, cached_embedding)

                if similarity > best_similarity and similarity >= EMBEDDING_SIMILARITY_THRESHOLD:
                    best_similarity = similarity
                    best_embedding = cached_embedding
                    best_cache_doc = doc

            except Exception as sim_error:
                logger.debug(f"Failed to calculate similarity: {str(sim_error)}")
                continue

        if best_embedding and best_cache_doc:
            # Update usage statistics for the matched cache entry
            try:
                best_cache_doc.reference.update({
                    "last_used": firestore.SERVER_TIMESTAMP,
                    "usage_count": firestore.Increment(1)
                })
            except Exception as update_error:
                logger.debug(f"Failed to update cache usage: {str(update_error)}")

            logger.info(f"🎯 Found semantically similar query using cosine similarity: {best_similarity:.4f}")
            logger.info(f"💡 Reusing cached embedding - saved API call!")
            return best_embedding

        logger.info(f"❌ No semantically similar cached embedding found (best similarity: {best_similarity:.4f})")

        # Since we already generated the embedding for comparison, return it
        # and cache it for future use
        logger.info(f"💾 Caching the generated embedding for future reuse")
        cache_query_embedding(query, query_embedding)
        return query_embedding

    except Exception as e:
        logger.error(f"❌ Error checking embedding cache: {str(e)}")
        return None


def cache_query_embedding(query: str, embedding: List[float]) -> None:
    """
    Store a query embedding in the cache for future reuse with improved normalization.

    Args:
        query: The user's query text
        embedding: The embedding vector to cache
    """
    try:
        # Normalize the query for better cache matching
        normalized_query = normalize_query_for_caching(query)

        if len(normalized_query) < 3:  # Skip very short queries
            logger.info(f"⚠️ Query too short for caching: {query[:50]}...")
            return

        # Create a hash of the normalized query for the document ID
        query_hash = hashlib.md5(normalized_query.encode()).hexdigest()

        cache_data = {
            "original_query": query.strip(),  # Keep original for reference
            "normalized_query": normalized_query,  # Store normalized for matching
            "embedding": embedding,
            "created_at": firestore.SERVER_TIMESTAMP,
            "last_used": firestore.SERVER_TIMESTAMP,  # Track usage for LRU
            "usage_count": 1,  # Track how often this cache entry is used
            "query_hash": query_hash,
            "embedding_dimensions": len(embedding),
            "cache_version": "v2"  # Version for future migrations
        }

        # Store in cache collection using normalized query hash as document ID
        db.collection("query_embedding_cache").document(query_hash).set(cache_data)
        logger.info(f"💾 Cached embedding for normalized query: {normalized_query[:50]}...")

    except Exception as e:
        logger.error(f"❌ Error caching embedding: {str(e)}")


def cleanup_expired_embeddings() -> None:
    """
    Clean up expired embedding cache entries (older than TTL) and maintain cache size.
    This should be called periodically to maintain cache performance.
    """
    try:
        cutoff_time = datetime.now() - timedelta(hours=EMBEDDING_CACHE_TTL_HOURS)

        # Query for expired entries in batches to avoid large operations
        cache_ref = db.collection("query_embedding_cache")
        expired_entries = cache_ref.where("created_at", "<", cutoff_time).limit(100).stream()

        deleted_count = 0
        batch = db.batch()
        batch_size = 0

        for doc in expired_entries:
            batch.delete(doc.reference)
            batch_size += 1
            deleted_count += 1

            # Commit batch every 50 deletions to avoid timeout
            if batch_size >= 50:
                batch.commit()
                batch = db.batch()
                batch_size = 0

        # Commit remaining deletions
        if batch_size > 0:
            batch.commit()

        if deleted_count > 0:
            logger.info(f"🧹 Cleaned up {deleted_count} expired embedding cache entries")

        # Also clean up cache entries without proper structure (migration cleanup)
        try:
            old_entries = cache_ref.where("cache_version", "==", None).limit(50).stream()
            old_deleted = 0
            for doc in old_entries:
                doc.reference.delete()
                old_deleted += 1

            if old_deleted > 0:
                logger.info(f"🔄 Migrated {old_deleted} old cache entries")

        except Exception as migration_error:
            logger.debug(f"Migration cleanup skipped: {str(migration_error)}")

    except Exception as e:
        logger.error(f"❌ Error cleaning up embedding cache: {str(e)}")

# ------------------------
# Request + Response Models
# ------------------------


class AgentRecommendRequest(BaseModel):
    query: str = Field(..., description="The user's query")
    format: Optional[str] = Field(default="auto", description="Format type: 'product', 'sidebar', 'floating', 'expandable', 'conversation', or 'auto'")
    previous_query: Optional[str] = None
    previous_summary: Optional[str] = None
    session_id: Optional[str] = None
    is_fallback_allowed: Optional[bool] = Field(default=True, description="Whether to allow fallback recommendations when no relevant offers are found. Set to False to return empty results instead of fallback offers.")


class AgentRecommendation(BaseModel):
    title: str
    reason: str
    intent_match_score: float
    admesh_link: str
    ad_id: str
    product_id: str

    # Additional product/offer fields
    url: Optional[str] = ""
    redirect_url: Optional[str] = ""
    description: Optional[str] = ""
    pricing: Optional[str] = ""
    reward_note: Optional[str] = ""
    keywords: Optional[List[str]] = []
    categories: Optional[List[str]] = []
    features: Optional[List[str]] = []
    integrations: Optional[List[str]] = []
    trial_days: Optional[int] = 0
    audience_segment: Optional[str] = ""
    is_open_source: Optional[bool] = False
    offer_trust_score: Optional[float] = 0.5
    brand_trust_score: Optional[float] = 0.5
    is_fallback: Optional[bool] = False

    # New marketing content fields
    # Marketing-optimized title for recommendations
    recommendation_title: Optional[str] = ""
    # Marketing-optimized description for recommendations
    recommendation_description: Optional[str] = ""
    offer_images: Optional[List[Dict]] = []  # Promotional images for the offer
    product_logo: Optional[Dict] = None  # Product logo information
    # Feature sections for expandable units
    feature_sections: Optional[List[Dict]] = []

    # Content variations for different ad formats
    content_variations: Optional[Dict[str, Dict[str, str]]] = {}


class AgentFollowupSuggestion(BaseModel):
    label: str
    query: str
    product_mentions: List[str]
    admesh_links: Dict[str, str]


class AgentRecommendResponse(BaseModel):
    session_id: str
    intent: Dict[str, Any]
    response: Dict[str, Any]
    tokens_used: int
    model_used: str
    recommendation_id: Optional[str] = None
    end_of_session: Optional[bool] = True
    llm_confidence: Optional[float] = Field(None, description="LLM confidence score (0.0-1.0) for intent detection quality, used for analytics and debugging")

# ------------------------
# POST /recommend
# ------------------------


@router.post("/recommend", response_model=AgentRecommendResponse)
async def handle_agent_recommendation(
    payload: AgentRecommendRequest,
    request: Request
):
    try:
        # --- Step 0: Authenticate agent via API key ---
        try:
            api_key_info = verify_api_key_from_request(request)
            agent_id = api_key_info["agent_id"]
            is_test = api_key_info.get("is_test", False)
            logger.info(
                f"🔑 API key verified: agent_id={agent_id}, is_test={is_test}")
        except HTTPException as e:
            logger.error(f"Authentication error: {e.detail}")
            raise e
        except Exception as e:
            logger.exception(
                f"Unexpected error during authentication: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Internal server error")

        # --- Step 1: Generate session ID ---
        session_id = payload.session_id or f"sess_{int(time.time())}_{agent_id[:6]}"

        # --- Step 1.5: Periodic cleanup of expired embedding cache (every ~100 requests) ---
        if int(time.time()) % 100 == 0:  # Simple way to run cleanup occasionally
            logger.info("🧹 Running periodic embedding cache cleanup...")
            cleanup_expired_embeddings()

        # --- Step 2: Detect intent from query ---
        intent = detect_user_intent(payload.query, DEFAULT_MODEL)

        if intent.get("llm_intent_confidence_score", 0) < 0.5:
            raise HTTPException(
                status_code=400, detail="Low intent confidence")

        categories = intent.get("categories")
        keywords = intent.get("keywords", [])
        print("Agent Recomm - categories", categories)
        print("Agent Recomm - keywords", keywords)
        llm_confidence = intent.get("llm_intent_confidence_score", 0.5)

        # --- Step 2.5: Get query embedding (cached or generated with cosine similarity matching) ---
        logger.info(f"🔍 Getting embedding for query: {payload.query[:50]}...")
        query_embedding = get_cached_embedding(payload.query)

        if query_embedding is None:
            # Fallback: generate embedding if cache completely fails
            logger.warning("⚠️ Cache system failed, generating embedding directly...")
            query_embedding = embed_text(payload.query)
            logger.info(f"✅ Generated fallback embedding with {len(query_embedding)} dimensions")
        else:
            logger.info(f"✅ Got query embedding with {len(query_embedding)} dimensions")

        # --- Step 3: Fetch and score matching offers ---
        scored_offers = fetch_active_offers(
            categories=categories,
            keywords=keywords,
            llm_confidence=llm_confidence,
            query_embedding=query_embedding,
            is_test=is_test  # Pass the is_test flag to track views correctly
        )

        # --- Step 3.5: Conditional fallback mechanism if no relevant offers found ---
        if not scored_offers:
            if payload.is_fallback_allowed:
                logger.info(
                    "🔄 No matching offers found, attempting fallback to any active offer")
                fallback_offer = fetch_fallback_offer()
                if fallback_offer:
                    scored_offers = [fallback_offer]
                    logger.info(
                        f"✅ Using fallback offer: {fallback_offer.get('title', 'Unknown')}")
                else:
                    logger.warning("⚠️ No fallback offers available")
            else:
                logger.info("🚫 No matching offers found and fallback is disabled, returning empty recommendations")

        # --- Step 4: Build enriched recommendations with tracking links ---
        recommendations = []
        recommendation_id = str(uuid.uuid4())
        recommendation_mappings = []

        for item in scored_offers:
            offer_id = item.get("offer_id")
            pid = item.get("product_id")
            redirect_url = item.get("redirect_url") or item.get("url", "")

            ad_id = offer_id or str(uuid.uuid4())
            admesh_link = build_admesh_link(
                ad_id=ad_id, product_id=pid, redirect_url=redirect_url)

            # Ensure intent_match_score is between 0 and 1
            intent_match_score = min(
                max(item.get("intent_match_score", 0), 0), 1)

            # Build enriched recommendation with all available fields
            recommendation = {
                "title": item.get("title", ""),
                "reason": item.get("match_reason", ""),
                "intent_match_score": intent_match_score,
                "ad_id": ad_id,
                "product_id": pid,
                "admesh_link": admesh_link,

                # Marketing content fields - use offer_title/offer_description if available
                "recommendation_title": item.get("offer_title") or item.get("title", ""),
                "recommendation_description": item.get("offer_description") or item.get("description", ""),

                # Additional product/offer fields
                "url": item.get("url", ""),
                "redirect_url": redirect_url,
                "description": item.get("description", ""),
                "pricing": item.get("pricing", "") or item.get("pricing_url", ""),
                "reward_note": item.get("reward_note", ""),
                "keywords": item.get("keywords", []),
                "categories": item.get("categories", []),
                "features": item.get("features", []),
                "integrations": item.get("integration_list", []) or item.get("integrations", []),
                "trial_days": item.get("trial_days", 0),
                "audience_segment": item.get("audience_segment", ""),
                "is_open_source": item.get("is_open_source", False),
                "offer_trust_score": item.get("offer_trust_score", 0.5),
                "brand_trust_score": item.get("brand_trust_score", 0.5),
                "is_fallback": item.get("is_fallback", False),

                # Visual assets
                "offer_images": item.get("offer_images", []),
                "product_logo": item.get("product_logo"),

                # Feature sections for expandable units
                "feature_sections": item.get("feature_sections", []),

                # Content variations for different ad formats
                "content_variations": generate_content_variations(
                    title=item.get("title", ""),
                    reason=item.get("match_reason", ""),
                    keywords=item.get("keywords", [])
                )
            }

            recommendations.append(recommendation)

            recommendation_mappings.append({
                "product_id": pid,
                "ad_id": ad_id,
                "admesh_link": admesh_link,
                "title": item.get("title", ""),
                "url": item.get("url", ""),
                "redirect_url": redirect_url
            })

        # --- Step 5: Log session and recommendations ---
        save_query_session(
            query=payload.query,
            intent=intent,
            model=DEFAULT_MODEL,
            agent_id=agent_id,
            user_id=None,
            session_id=session_id
        )

        db.collection("recommendations").add({
            "recommendation_id": recommendation_id,
            "session_id": session_id,
            "agent_id": agent_id,
            "query": payload.query,
            "products": recommendation_mappings,
            "llm_confidence": llm_confidence,  # Store LLM confidence for analytics
            "created_at": firestore.SERVER_TIMESTAMP
        })

        # --- Step 6: Apply format-specific logic ---
        format_type = payload.format or "auto"
        recommendations = apply_format_specific_logic(
            recommendations, format_type, intent, agent_id, is_test)

        # --- Step 7: Generate follow-up suggestions ---
        followups = build_followup_suggestions(
            intent, recommendations, session_id)

        # --- Step 8: Return API response ---
        # Check if we're using fallback offers
        is_fallback_response = any(rec.get("is_fallback", False)
                                   for rec in recommendations)
        print("recommendations", recommendations)
        return {
            "session_id": session_id,
            "intent": intent,
            "response": {
                "summary": summarize_detected_intent(intent, is_fallback=is_fallback_response),
                "recommendations": recommendations,
                "followup_suggestions": followups,
                "is_fallback": is_fallback_response
            },
            "tokens_used": 500,  # Placeholder until token tracking is implemented
            "model_used": DEFAULT_MODEL,
            "recommendation_id": recommendation_id,
            "end_of_session": True,
            "llm_confidence": llm_confidence  # Include LLM confidence in response
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in /recommend: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


def compute_llm_confidence_score(intent: Dict[str, Any], query: str) -> float:
    """
    Compute a confidence score based on how well the extracted intent aligns with the query.
    """
    score = 0.0

    # 1. Categories match
    categories = intent.get("categories", [])
    if isinstance(categories, list) and categories:
        # Check if any category is in the query
        if any(cat.lower() in query.lower() for cat in categories if isinstance(cat, str)):
            score += 0.3

    # 2. Known mentions match
    mentions = intent.get("known_mentions", [])
    if isinstance(mentions, list) and any(m.lower() in query.lower() for m in mentions):
        score += 0.3

    # 3. Goal is descriptive enough
    if len(intent.get("goal", "").strip()) >= 15:
        score += 0.3

    return min(round(score + 0.1, 2), 1.0)


def detect_user_intent(query: str, model: str) -> Dict[str, Any]:
    """
    Detects user intent from a query using an LLM, normalizes and scores the result,
    and stores new intent types into Firestore if not already present.
    """
    intent_prompt = f"""
You are AdMesh, an AI assistant that helps users discover and evaluate the best software and tools.

Your task is to analyze the user's query and return their intent in structured JSON format — without recommending any specific products.

User query: "{query}"

Return a valid JSON object with the following keys:
{{
  "intent_group": "transactional",
  "type": "product_discovery",
  "categories": [...],              // List of relevant product categories (e.g. "ad_network", "crm", "analytics", etc.)
  "known_mentions": [...],          // List of product/brand names mentioned in the query
  "keywords": [...],                // Key terms in the query that help match offers
  "goal": "...",                    // A plain English explanation of what the user wants
  "llm_intent_confidence_score": 0.85
}}

Output only valid JSON. Do not include any text, comments, or markdown. Your response must be parseable by a JSON parser.
"""

    try:
        # Step 1: Call LLM
        raw = call_openrouter(intent_prompt, model)
        cleaned = clean_response(raw)
        intent = json.loads(cleaned)

        # Step 2: Validate expected fields and provide defaults if missing
        required_fields = ["type", "intent_group",
                           "categories", "goal", "known_mentions"]

        # Check if any required fields are missing and provide defaults
        if not all(field in intent for field in required_fields):
            logger.warning(f"Missing required fields in intent: {intent}")

            # Create default values for missing fields
            intent_type = intent.get("type", intent.get(
                "intent_type", "product_discovery"))
            intent_group = intent.get("intent_group", "commercial")

            # Get categories or use default
            categories = intent.get("categories", ["software"])
            goal = intent.get("goal", query)
            known_mentions = intent.get("known_mentions", [])
            keywords = intent.get("keywords", [])
        else:
            intent_type = intent.get("type", intent.get(
                "intent_type", "product_discovery"))
            intent_group = intent["intent_group"]
            categories = intent["categories"]
            goal = intent["goal"]
            known_mentions = intent["known_mentions"]
            keywords = intent.get("keywords", [])

        # Step 3: Normalize fields
        intent_type = intent_type.strip().lower().replace(" ", "_")
        intent_group = intent_group.strip().lower().replace(" ", "_")

        # Ensure categories is a list and normalize values
        if not isinstance(categories, list):
            categories = ["software"]  # Default category
        else:
            categories = [c.strip().lower().replace(" ", "_")
                          for c in categories if isinstance(c, str)]
            if not categories:  # If list is empty after filtering
                categories = ["software"]

        goal = goal.strip()
        known_mentions = known_mentions if isinstance(
            known_mentions, list) else []

        # Step 3.5: Enhanced keyword extraction with fallback logic
        from itertools import chain

        def generate_phrases(text: str) -> list[str]:
            """Generate n-grams (1-3 words) from the input text"""
            tokens = text.lower().split()
            phrases = []
            for n in range(1, min(4, len(tokens)+1)):  # unigrams to trigrams
                phrases.extend(" ".join(tokens[i:i+n])
                               for i in range(len(tokens)-n+1))
            return phrases

        # Combine LLM-provided + query-derived keywords
        llm_keywords = intent.get("keywords", [])
        query_phrases = generate_phrases(query)

        # Use known whitelist or simple filters to eliminate noise
        BAD_KEYWORDS = {"best", "top", "cheap", "buy", "free", "good", "great", "find", "looking", "for", "need",
                        "want", "help", "me", "i", "a", "an", "the", "is", "are", "and", "or", "but", "with", "without"}

        # Filter out junk and deduplicate
        keywords = list({
            kw.strip().lower()
            for kw in chain(llm_keywords, query_phrases)
            if isinstance(kw, str) and kw.lower() not in BAD_KEYWORDS and len(kw) > 2
        })

        # Sort by length (longer phrases first) for better matching
        keywords = sorted(keywords, key=len, reverse=True)

        logger.info(f"🔍 Extracted keywords: {keywords}")
        logger.info(f"🔍 Original LLM keywords: {llm_keywords}")
        logger.info(f"🔍 Query phrases: {query_phrases}")

        # Step 4: Validate intent_group
        valid_groups = {"informational", "navigational",
                        "commercial", "transactional"}
        if intent_group not in valid_groups:
            intent_group = "commercial"

        # Step 5: Score fallback
        confidence_score = intent.get("llm_intent_confidence_score")
        if not isinstance(confidence_score, (int, float)):
            confidence_score = compute_llm_confidence_score({
                "categories": categories,
                "goal": goal,
                "known_mentions": known_mentions
            }, query)

        confidence_score = max(0.0, min(float(confidence_score), 1.0))

        # Step 6: Store intent_type if new
        intent_doc = db.collection("intent_types").document(intent_type).get()
        if not intent_doc.exists:
            db.collection("intent_types").document(intent_type).set({
                "intent_group": intent_group,
                "categories": categories,  # Store as a list
                "goal_examples": [goal],
                "source": "auto-added",
                "created_at": firestore.SERVER_TIMESTAMP
            })
            logger.info(f"🆕 Added new intent_type: {intent_type}")
        print("categories", categories)
        print("goal", goal)
        print("known_mentions", known_mentions)
        print("keywords", keywords)
        print("confidence_score", confidence_score)
        print("intent_group", intent_group)
        print("intent_type", intent_type)
        print("raw", raw)
        print("cleaned", cleaned)
        print("intent", intent)
        # Step 7: Return intent object
        return {
            "intent_group": intent_group,
            "intent_type": intent_type,
            "categories": categories,  # Return as a list
            "goal": goal,
            "known_mentions": known_mentions,
            "keywords": keywords,
            "llm_intent_confidence_score": round(confidence_score, 4)
        }

    except Exception as e:
        logger.exception(f"❌ Failed to detect intent: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to detect intent from query")


def fetch_active_offers(categories: List[str], keywords: List[str], llm_confidence: float, query_embedding: List[float], is_test: bool = False) -> List[Dict[str, Any]]:
    """
    Fetches active offers matching categories and keywords, then scores them using:
    - category match
    - keyword overlap
    - semantic similarity (using embeddings)
    - llm confidence
    - payout
    - trust scores

    Args:
        categories: List of categories to match
        keywords: List of keywords to match
        llm_confidence: Confidence score from the LLM
        query_embedding: Embedding vector for the user query
        is_test: Whether this is a test request (determines which view counter to increment)
    """
    logger.info(
        f"🔍 Starting fetch_active_offers with categories={categories}, keywords={keywords}, llm_confidence={llm_confidence}, is_test={is_test}")

    offers_ref = db.collection("offers")
    query_ref = offers_ref.where("offer_status", "==", "active")
    logger.info(
        f"📊 Query: offer_status=active (no category filtering - using semantic matching only)")

    # Set semantic similarity threshold for filtering - this is the ONLY filter
    SEMANTIC_SIMILARITY_THRESHOLD = 0.3  # Only include offers with similarity > 0.3
    logger.info(
        f"🎯 Using semantic similarity threshold: {SEMANTIC_SIMILARITY_THRESHOLD} (primary filtering method)")

    offers = []
    filtered_by_embedding_count = 0
    logger.info(f"🔄 Starting to process query results")

    for doc in query_ref.stream():
        offer_id = doc.id
        logger.info(f"📝 Processing offer: {offer_id}")

        offer = doc.to_dict()
        if not offer:
            logger.warning(f"⚠️ Offer {offer_id} has no data, skipping")
            continue

        product_id = offer.get("product_id")
        if not product_id:
            logger.warning(f"⚠️ Offer {offer_id} has no product_id, skipping")
            continue
        logger.info(f"🔗 Found product_id: {product_id} for offer: {offer_id}")

        product_doc = db.collection("products").document(product_id).get()
        if not product_doc.exists:
            logger.warning(
                f"⚠️ Product {product_id} does not exist, skipping offer: {offer_id}")
            continue
        logger.info(f"✅ Found product document for product_id: {product_id}")

        product = product_doc.to_dict()
        logger.info(
            f"📋 Product data retrieved for {product_id}: {product.get('title', 'No Title')}")

        # --- Semantic Matching: Generate or retrieve product embedding ---
        product_embedding = get_product_embedding(product_id, product)

        # Calculate semantic similarity score - PRIMARY SCORING METHOD
        semantic_score = cosine_similarity(query_embedding, product_embedding)
        logger.info(
            f"📊 Semantic similarity: {semantic_score:.4f} (primary score)")

        # Filter out offers with low semantic similarity - ONLY FILTER
        if semantic_score < SEMANTIC_SIMILARITY_THRESHOLD:
            logger.info(
                f"🚫 Filtering out offer {offer_id} due to low semantic similarity: {semantic_score:.4f} < {SEMANTIC_SIMILARITY_THRESHOLD}")
            filtered_by_embedding_count += 1
            continue

        # Scoring components (semantic similarity gets highest weight)
        logger.info(f"🧮 Starting scoring calculations for offer: {offer_id}")

        # Semantic similarity: 60% weight (primary factor)
        semantic_score_scaled = round(0.6 * min(semantic_score, 1.0), 4)
        logger.info(f"📊 Semantic score (60% weight): {semantic_score_scaled}")

        # LLM confidence: 20% weight
        confidence_score = round(0.2 * min(max(llm_confidence, 0.0), 1.0), 4)
        logger.info(f"📊 Confidence score (20% weight): {confidence_score}")

        # Handle payout which can be a number or a dictionary
        payout = offer.get("payout", 0)
        logger.info(
            f"💰 Raw payout data: {payout} (type: {type(payout).__name__})")

        if isinstance(payout, dict):
            # If payout is a dictionary, extract the amount
            logger.info(f"💰 Payout is a dictionary: {payout}")
            payout_amount = float(payout.get("amount", 0))
            logger.info(f"💰 Extracted payout amount: {payout_amount}")
        else:
            # If payout is a number or string, convert it to float
            logger.info(f"💰 Payout is a {type(payout).__name__}: {payout}")
            payout_amount = float(payout)
            logger.info(f"💰 Converted payout to float: {payout_amount}")

        cpa_score = round(0.2 * min(payout_amount / 100, 1.0), 4)
        logger.info(f"📊 CPA score: {cpa_score}")

        trust_offer = float(offer.get("offer_trust_score", 0.5))
        logger.info(f"📊 Offer trust score: {trust_offer}")

        trust_brand = float(offer.get("brand_trust_score", 0.5))
        logger.info(f"📊 Brand trust score: {trust_brand}")

        # Trust score: 20% weight
        offer_trust_score = round(0.2 * ((trust_offer + trust_brand) / 2), 4)
        logger.info(
            f"📊 Combined offer trust score (20% weight): {offer_trust_score}")

        # Sum all component scores - semantic similarity is primary factor
        raw_score = semantic_score_scaled + \
            confidence_score + cpa_score + offer_trust_score

        # Normalize to ensure it's between 0 and 1
        final_score = round(min(raw_score, 1.0), 4)

        logger.info(
            f"📊 Raw score sum: {raw_score}, Normalized final score: {final_score}")

        # Create user-friendly match reason based on semantic similarity
        reason_parts = []

        if semantic_score > 0.85:
            reason_parts.append("highly relevant to your query")
        elif semantic_score > 0.6:
            reason_parts.append("well-matched to your needs")
        elif semantic_score > 0.4:
            reason_parts.append("relevant to your query")

        if trust_offer >= 0.8:  # High trust
            reason_parts.append("highly trusted platform")
        elif trust_offer >= 0.6:
            reason_parts.append("trusted platform")

        if payout_amount >= 50:  # High payout
            reason_parts.append("excellent earning potential")

        # Fallback reason if no specific matches
        if not reason_parts:
            reason_parts.append("recommended based on semantic matching")

        match_reason = f"This offer {' and '.join(reason_parts)}."

        # Keep debug info in logs (semantic-focused)
        debug_reason = f"semantic={semantic_score_scaled}, confidence={confidence_score}, payout=${payout_amount:.2f}, cpa={cpa_score}, trust={offer_trust_score}"
        logger.info(f"📝 Debug match reason: {debug_reason}")
        logger.info(f"👤 User-friendly reason: {match_reason}")

        # Note: offer_views increment moved to format-specific logic after sorting/filtering

        offers.append({
            **offer,
            **product,
            "offer_id": doc.id,
            "product_id": product_id,
            "intent_match_score": final_score,
            "match_reason": match_reason
        })
        logger.info(
            f"✅ Added offer {offer_id} to results with score {final_score}")

    
    sorted_offers = sorted(
        offers, key=lambda x: x["intent_match_score"], reverse=True)[:3]

    # Log filtering statistics
    logger.info(
        f"🏁 Returning top {len(sorted_offers)} offers from {len(offers)} total matches")
    return sorted_offers


def fetch_fallback_offer() -> Optional[Dict[str, Any]]:
    """
    Fetches a single active offer as a fallback when no relevant matches are found.
    Returns the offer with the highest trust score and payout.

    Returns:
        A single offer dict with fallback scoring, or None if no active offers exist
    """
    logger.info("🔄 Starting fallback offer fetch")

    try:
        # Query for any active offers, ordered by trust score and payout
        offers_ref = db.collection("offers")
        query_ref = offers_ref.where("active", "==", True).limit(
            10)  # Get top 10 to choose from

        fallback_candidates = []

        for doc in query_ref.stream():
            offer_id = doc.id
            offer = doc.to_dict()

            if not offer:
                continue

            product_id = offer.get("product_id")
            if not product_id:
                continue

            # Get product data
            product_doc = db.collection("products").document(product_id).get()
            if not product_doc.exists:
                continue

            product = product_doc.to_dict()

            # Calculate fallback score based on trust and payout only
            trust_offer = float(offer.get("offer_trust_score", 0.5))
            trust_brand = float(offer.get("brand_trust_score", 0.5))
            combined_trust = (trust_offer + trust_brand) / 2

            # Handle payout
            payout = offer.get("payout", 0)
            if isinstance(payout, dict):
                payout_amount = float(payout.get("amount", 0))
            else:
                payout_amount = float(payout)

            # Simple fallback scoring: 70% trust, 30% payout (normalized to max $100)
            fallback_score = (0.7 * combined_trust) + \
                (0.3 * min(payout_amount / 100, 1.0))

            # Generate match reason using the same logic as regular offers
            reason_parts = []

            if trust_offer >= 0.8:  # High trust
                reason_parts.append("highly trusted platform")

            if payout_amount >= 50:  # Good payout
                reason_parts.append("competitive rewards")

            if product.get("trial_days", 0) > 0:
                reason_parts.append(
                    f"{product.get('trial_days')} day trial available")

            # Fallback reason if no specific matches
            if not reason_parts:
                reason_parts.append("quality option from our inventory")

            match_reason = f"This offer {' and '.join(reason_parts)}."

            fallback_candidates.append({
                **offer,
                **product,
                "offer_id": offer_id,
                "product_id": product_id,
                "intent_match_score": round(fallback_score, 4),
                "match_reason": match_reason,
                "is_fallback": True
            })

        if not fallback_candidates:
            logger.warning("⚠️ No active offers found for fallback")
            return None

        # Sort by fallback score and take the best one
        best_fallback = max(fallback_candidates,
                            key=lambda x: x["intent_match_score"])

        # Note: offer_views increment moved to format-specific logic after sorting/filtering

        logger.info(
            f"✅ Selected fallback offer: {best_fallback.get('title', 'Unknown')} with score {best_fallback['intent_match_score']}")
        return best_fallback

    except Exception as e:
        logger.exception(f"❌ Error in fetch_fallback_offer: {str(e)}")
        return None


def generate_content_variations(title: str, reason: str, keywords: List[str]) -> Dict[str, Dict[str, str]]:
    """
    Generate statement and question variations for ad content.

    Args:
        title: Product/service title
        reason: Match reason or description
        keywords: List of relevant keywords

    Returns:
        Dictionary with statement and question variations
    """
    # Extract category from keywords or use generic term
    category = keywords[0].lower() if keywords else 'solutions'

    # Clean up reason for statement variation
    benefit = reason.split(
        '.')[0] if reason else f"offering best {category} for your business"
    if benefit.lower().startswith('this offer'):
        benefit = benefit[11:]  # Remove "This offer " prefix

    return {
        "statement": {
            "text": f"{title} is {benefit.lower()}, visit",
            "cta": title
        },
        "question": {
            "text": f"Looking for {category} for your business? Try",
            "cta": title
        }
    }


def summarize_detected_intent(intent: Dict[str, Any], is_fallback: bool = False) -> str:
    """Generate a summary of the detected intent."""
    goal = intent.get("goal", "")
    if isinstance(goal, list):
        goal = ", ".join(goal)

    # Get the first category for the summary
    categories = intent.get("categories", ["software"])
    category_str = categories[0] if categories else "software"

    if is_fallback:
        return f"While we couldn't find specific {category_str} tools matching your exact query '{goal}', here's a trusted option from our available inventory that might be of interest:"
    else:
        return f"Here are {category_str} tools that match your goal: {goal}"


def build_followup_suggestions(
    intent: Dict[str, Any],
    offers: List[Dict[str, Any]],
    session_id: str
) -> List[Dict[str, Any]]:
    """
    Generate contextual follow-up suggestions based on recommended products.
    Each suggestion includes product mentions and their admesh links.
    """
    # Get the first category for suggestions
    categories = intent.get("categories", ["software"])
    category_str = categories[0].lower() if categories else "software"

    suggestions = []

    # Skip if no offers
    if not offers:
        logger.warning("⚠️ No offers available for followup suggestions")
        return []

    logger.info(
        f"🔍 Building followup suggestions for {len(offers)} offers in category: {category_str}")

    # Extract product titles and admesh links
    product_titles = []
    product_links = {}

    for offer in offers:
        title = offer.get("title", "")
        if title:
            product_titles.append(title)
            product_links[title] = offer.get("admesh_link", "")

    logger.info(f"📋 Product titles: {product_titles}")

    # 1️⃣ Feature comparison suggestion for top product
    if product_titles:
        top_product = product_titles[0]
        suggestions.append({
            "label": f"What features does {top_product} offer?",
            "query": f"{top_product} key features and benefits",
            "product_mentions": [top_product],
            "admesh_links": {top_product: product_links.get(top_product, "")},
            "session_id": session_id
        })

    # 2️⃣ Compare top 2 products
    if len(product_titles) >= 2:
        p1, p2 = product_titles[0], product_titles[1]
        suggestions.append({
            "label": f"Compare {p1} vs {p2}",
            "query": f"Compare {p1} vs {p2} for {category_str}",
            "product_mentions": [p1, p2],
            "admesh_links": {
                p1: product_links.get(p1, ""),
                p2: product_links.get(p2, "")
            },
            "session_id": session_id
        })

    # 3️⃣ Pricing suggestion for top product
    if product_titles:
        top_product = product_titles[0]
        suggestions.append({
            "label": f"{top_product} pricing plans",
            "query": f"{top_product} pricing and plans",
            "product_mentions": [top_product],
            "admesh_links": {top_product: product_links.get(top_product, "")},
            "session_id": session_id
        })

    # 4️⃣ Alternatives to second best product (if available)
    if len(product_titles) >= 2:
        second_product = product_titles[1]
        alternatives = [p for p in product_titles if p !=
                        second_product][:3]  # Top 3 alternatives

        suggestion = {
            "label": f"Alternatives to {second_product}",
            "query": f"Best alternatives to {second_product}",
            "product_mentions": alternatives,
            "admesh_links": {p: product_links.get(p, "") for p in alternatives},
            "session_id": session_id
        }
        suggestions.append(suggestion)

    # 5️⃣ Category exploration with top product
    if product_titles and category_str:
        top_product = product_titles[0]
        suggestions.append({
            "label": f"Best {category_str} tools like {top_product}",
            "query": f"Best {category_str} tools similar to {top_product}",
            "product_mentions": [top_product],
            "admesh_links": {top_product: product_links.get(top_product, "")},
            "session_id": session_id
        })

    logger.info(f"✅ Generated {len(suggestions)} followup suggestions")
    return suggestions


def save_query_session(
    query: str,
    intent: Dict[str, Any],
    model: str,
    agent_id: str,
    user_id: Optional[str],
    session_id: str
) -> None:
    """Save the query session to Firestore."""
    try:
        # Create session document
        sessions_ref = db.collection("sessions")
        session_data = {
            "session_id": session_id,
            "agent_id": agent_id,
            "user_id": user_id,
            "initial_query": query,
            "intent": intent,
            "model_used": model,
            "created_at": firestore.SERVER_TIMESTAMP,
            "last_updated": firestore.SERVER_TIMESTAMP,
            "followup_count": 0
        }

        # Save session
        sessions_ref.document(session_id).set(session_data)

    except Exception as e:
        logger.error(f"Error saving query session: {str(e)}")


def increment_offer_views(offer_id: str, is_test: bool) -> None:
    """
    Increment offer_views counter for a specific offer.

    Args:
        offer_id: The offer ID to increment views for
        is_test: Whether this is a test request (determines which counter to increment)
    """
    try:
        if is_test:
            db.collection("offers").document(offer_id).update({
                "offer_views.test": firestore.Increment(1),
                "offer_views.total": firestore.Increment(1)
            })
            logger.info(f"📊 Incremented test view count for offer {offer_id}")
        else:
            db.collection("offers").document(offer_id).update({
                "offer_views.production": firestore.Increment(1),
                "offer_views.total": firestore.Increment(1)
            })
            logger.info(f"📊 Incremented production view count for offer {offer_id}")
    except Exception as e:
        logger.error(f"❌ Failed to increment offer_views for offer {offer_id}: {str(e)}")


def apply_format_specific_logic(recommendations: List[Dict], format_type: str, intent: Dict, agent_id: str, is_test: bool) -> List[Dict]:
    """
    Apply format-specific logic to recommendations based on the requested format.

    Args:
        recommendations: List of recommendation dictionaries
        format_type: The requested format ('conversation', 'product', 'sidebar', 'floating', 'expandable', 'auto')
        intent: The detected intent
        agent_id: The agent ID for tracking
        is_test: Whether this is a test request

    Returns:
        Modified list of recommendations based on format requirements
    """
    if not recommendations:
        return recommendations

    if format_type == "product":
        # Return only one matched product and increment offer_views for that single product
        best_recommendation = max(
            recommendations, key=lambda x: x.get("intent_match_score", 0))

        # Increment offer_views for the single best-matched product only
        offer_id = best_recommendation.get("ad_id") or best_recommendation.get("offer_id")
        if offer_id:
            increment_offer_views(offer_id, is_test)

        update_single_product_metrics(best_recommendation, agent_id, is_test)
        return [best_recommendation]

    elif format_type in ["sidebar", "floating"]:
        # Return maximum 3 recommendations for sidebar/floating components
        top_recommendations = sorted(recommendations, key=lambda x: x.get(
            "intent_match_score", 0), reverse=True)[:3]

        # Increment offer_views for the actual returned products only
        for rec in top_recommendations:
            offer_id = rec.get("ad_id") or rec.get("offer_id")
            if offer_id:
                increment_offer_views(offer_id, is_test)
            update_product_view_metrics(rec, agent_id, is_test)
        return top_recommendations

    elif format_type == "expandable":
        # Include feature sections data; generate if missing
        enhanced_recommendations = []
        for rec in recommendations:
            enhanced_rec = rec.copy()

            # Ensure feature_sections exist, generate if missing
            if not enhanced_rec.get("feature_sections"):
                enhanced_rec["feature_sections"] = generate_feature_sections_with_llm(
                    title=rec.get("title", ""),
                    description=rec.get("description", ""),
                    features=rec.get("features", [])
                )

            enhanced_recommendations.append(enhanced_rec)

            # Increment offer_views for actual returned products
            offer_id = enhanced_rec.get("ad_id") or enhanced_rec.get("offer_id")
            if offer_id:
                increment_offer_views(offer_id, is_test)
            update_product_view_metrics(enhanced_rec, agent_id, is_test)

        return enhanced_recommendations

    elif format_type == "conversation":
        # Return response optimized for conversation summary components
        conversation_optimized = []
        for rec in recommendations:
            conv_rec = rec.copy()

            # Optimize content for conversation context
            conv_rec["conversation_context"] = generate_conversation_context(
                rec.get("title", ""),
                rec.get("reason", ""),
                intent.get("goal", "")
            )

            conversation_optimized.append(conv_rec)

            # Increment offer_views for actual returned products
            offer_id = conv_rec.get("ad_id") or conv_rec.get("offer_id")
            if offer_id:
                increment_offer_views(offer_id, is_test)
            update_product_view_metrics(conv_rec, agent_id, is_test)

        return conversation_optimized

    else:
        # Auto or default format - return all recommendations with standard metrics
        for rec in recommendations:
            # Increment offer_views for actual returned products
            offer_id = rec.get("ad_id") or rec.get("offer_id")
            if offer_id:
                increment_offer_views(offer_id, is_test)
            update_product_view_metrics(rec, agent_id, is_test)
        return recommendations


def update_single_product_metrics(recommendation: Dict, agent_id: str, is_test: bool):
    """Update view/impression metrics for a single product (format='product')."""
    try:
        product_id = recommendation.get("product_id")
        offer_id = recommendation.get("ad_id")

        if not product_id:
            return

        # Update product view count
        product_ref = db.collection("products").document(product_id)
        product_ref.update({
            "view_count": firestore.Increment(1),
            "last_viewed": firestore.SERVER_TIMESTAMP
        })

        # Update offer impression count if offer exists
        if offer_id:
            offer_ref = db.collection("offers").document(offer_id)
            offer_ref.update({
                "impression_count": firestore.Increment(1),
                "last_impression": firestore.SERVER_TIMESTAMP
            })

        # Log single product view event
        db.collection("analytics_events").add({
            "event_type": "single_product_view",
            "product_id": product_id,
            "offer_id": offer_id,
            "agent_id": agent_id,
            "is_test": is_test,
            "format": "product",
            "timestamp": firestore.SERVER_TIMESTAMP
        })

    except Exception as e:
        logger.error(f"Error updating single product metrics: {str(e)}")


def update_product_view_metrics(recommendation: Dict, agent_id: str, is_test: bool):
    """Update view/impression metrics for multiple products (standard format)."""
    try:
        product_id = recommendation.get("product_id")
        offer_id = recommendation.get("ad_id")

        if not product_id:
            return

        # Update product view count (lighter increment for multi-product views)
        product_ref = db.collection("products").document(product_id)
        product_ref.update({
            "multi_view_count": firestore.Increment(1),
            "last_multi_viewed": firestore.SERVER_TIMESTAMP
        })

        # Update offer impression count
        if offer_id:
            offer_ref = db.collection("offers").document(offer_id)
            offer_ref.update({
                "multi_impression_count": firestore.Increment(1),
                "last_multi_impression": firestore.SERVER_TIMESTAMP
            })

        # Log multi-product view event
        db.collection("analytics_events").add({
            "event_type": "multi_product_view",
            "product_id": product_id,
            "offer_id": offer_id,
            "agent_id": agent_id,
            "is_test": is_test,
            "format": "multi",
            "timestamp": firestore.SERVER_TIMESTAMP
        })

    except Exception as e:
        logger.error(f"Error updating product view metrics: {str(e)}")


def generate_feature_sections_with_llm(title: str, description: str, features: List[str]) -> List[Dict]:
    """Generate feature sections using LLM if missing from database."""
    try:
        # Create prompt for LLM to generate feature sections
        prompt = f"""
        Generate detailed feature sections for the product "{title}".

        Product Description: {description}
        Existing Features: {', '.join(features) if features else 'None provided'}

        Create 3-4 feature sections with the following structure:
        - Section title (e.g., "Core Features", "Integration Capabilities", "Security & Compliance")
        - 2-3 specific features per section
        - Brief description for each feature

        Return as JSON array with this structure:
        [
          {{
            "title": "Section Title",
            "features": [
              {{"name": "Feature Name", "description": "Brief description"}},
              {{"name": "Feature Name", "description": "Brief description"}}
            ]
          }}
        ]
        """

        response = call_openrouter(prompt, DEFAULT_MODEL)
        cleaned_response = clean_response(response)

        try:
            feature_sections = json.loads(cleaned_response)
            if isinstance(feature_sections, list):
                return feature_sections
        except json.JSONDecodeError:
            pass

        # Fallback: create basic feature sections from existing features
        if features:
            return [{
                "title": "Key Features",
                "features": [{"name": feature, "description": f"Advanced {feature.lower()} capabilities"} for feature in features[:6]]
            }]

        return []

    except Exception as e:
        logger.error(f"Error generating feature sections with LLM: {str(e)}")
        return []


def generate_conversation_context(title: str, reason: str, goal: str) -> str:
    """Generate conversation-optimized context for the recommendation."""
    try:
        prompt = f"""
        Create a natural conversation context for recommending "{title}" to a user.

        User's Goal: {goal}
        Recommendation Reason: {reason}

        Generate a brief, conversational explanation (1-2 sentences) of why this product fits their needs.
        Make it sound natural and helpful, not salesy.
        """

        response = call_openrouter(prompt, DEFAULT_MODEL)
        return clean_response(response).strip()

    except Exception as e:
        logger.error(f"Error generating conversation context: {str(e)}")
        return reason  # Fallback to original reason

    except Exception as e:
        logger.exception(f"Failed to save agent session: {str(e)}")
        # Continue execution even if saving fails
