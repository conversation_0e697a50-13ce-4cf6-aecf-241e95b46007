from fastapi import APIRouter, HTTPException, Request, Depends, Path
from pydantic import BaseModel, HttpUrl
from datetime import datetime
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from auth.deps import require_role
from google.cloud import firestore
from google.cloud.firestore import transactional
from typing import Optional, Literal, List, Dict
import uuid
import logging
from api.utils.display_ids import ensure_unique_offer_display_id, resolve_offer_id

router = APIRouter()
db = get_db()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PayoutModel(BaseModel):
    amount: int  # Amount in cents
    currency: str = "USD"
    model: Literal["CPA", "CPL", "CPI", "RevShare"]

class OfferIncentive(BaseModel):
    type: str  # "discount", "bonus", "free_trial", "credit", "extended_plan"
    headline: str
    details: str
    cta_label: str

class OfferImage(BaseModel):
    url: str
    storage_path: str
    filename: str
    content_type: str
    dimensions: Dict[str, int]  # {"width": 1200, "height": 800}

class FeatureSection(BaseModel):
    title: str
    description: str
    icon: str

class OfferModel(BaseModel):
    product_id: Optional[str] = None
    goal: Optional[Literal["signup", "purchase", "lead", "app_install", "click"]] = None
    payout: Optional[PayoutModel] = None

    reward_note: Optional[str] = None  # 💡 Fallback: "$X per goal"
    suggestion_reason: Optional[str] = None  # 🧠 Used for ranking or relevance notes

    offer_trust_score: Optional[float] = 50.0  # Renamed from trust_score
    trust_score: Optional[float] = 50.0  # Kept for backward compatibility
    valid_until: Optional[datetime] = None
    offer_status: Optional[Literal["active", "inactive", "cancelled"]] = "inactive"  # Default to inactive
    offer_incentive: Optional[OfferIncentive] = None  # Optional offer incentive

    # New marketing content fields
    offer_title: Optional[str] = None  # Auto-generated marketing title
    offer_description: Optional[str] = None  # Auto-generated marketing description
    offer_images: Optional[List[OfferImage]] = []  # Promotional images
    feature_sections: Optional[List[FeatureSection]] = []  # Feature sections for expandable units

    # Budget tracking fields
    offer_total_budget_allocated: Optional[float] = None  # Total budget allocated for the offer
    offer_total_budget_spent: Optional[float] = None  # Budget spent for the offer
    offer_total_promo_spent: Optional[float] = None  # Promo credit spent for the offer
    offer_total_promo_available: Optional[float] = None  # Available promo credit for the offer

class IntegrationModel(BaseModel):
    method: Literal["redirect_pixel", "server_api", "manual"]
    webhook_url: Optional[HttpUrl] = None
    notes: Optional[str] = None
    redirect_url: Optional[str] = None
    target_urls: Optional[list[str]] = []

# Extended offer model for API requests that includes additional fields
class Offer(OfferModel):
    url: HttpUrl
    categories: list[str]
    keywords: list[str] = []

    meta: dict = {}

class OfferUpdate(BaseModel):
    product_id: Optional[str] = None
    goal: Optional[Literal["signup", "purchase", "lead", "app_install", "click"]] = None
    payout: Optional[PayoutModel] = None
    reward_note: Optional[str] = None
    suggestion_reason: Optional[str] = None
    offer_trust_score: Optional[float] = None  # Renamed from trust_score
    trust_score: Optional[float] = None  # Kept for backward compatibility
    valid_until: Optional[datetime] = None
    offer_status: Optional[Literal["active", "inactive", "cancelled"]] = None
    offer_incentive: Optional[OfferIncentive] = None  # Optional offer incentive

    # Budget tracking fields
    promo_applied: Optional[bool] = None

    # New budget tracking fields
    offer_total_budget_allocated: Optional[float] = None  # Total budget allocated for the offer
    offer_total_budget_spent: Optional[float] = None  # Budget spent for the offer
    offer_total_promo_spent: Optional[float] = None  # Promo credit spent for the offer
    offer_total_promo_available: Optional[float] = None  # Available promo credit for the offer

    # Additional fields
    url: Optional[HttpUrl] = None
    categories: Optional[list[str]] = None
    keywords: Optional[list[str]] = None

    # Marketing content fields
    offer_title: Optional[str] = None
    offer_description: Optional[str] = None
    feature_sections: Optional[List[FeatureSection]] = None




@router.post("/register")
async def register_offer(request: Request, offer: Offer, user=Depends(require_role("brand"))):
    # Removed subscription limits functionality

    id_token = request.headers.get("Authorization", "").replace("Bearer ", "")
    try:
        decoded_token = firebase_auth.verify_id_token(id_token)
        brand_id = decoded_token["uid"]
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid Firebase token")

    # Validate product_id is provided
    if not offer.product_id:
        raise HTTPException(status_code=400, detail="product_id is required")

    # Removed subscription limit checks for keywords and offers

    product_ref = db.collection("products").document(offer.product_id)
    product_doc = product_ref.get()
    if not product_doc.exists:
        raise HTTPException(status_code=404, detail="Product not found")

    # Get product data for marketing content generation
    product_data = product_doc.to_dict()

    # Generate fallback reward_note if not provided
    if not offer.reward_note and offer.payout:
        goal_text = offer.goal or "conversion"
        # Convert cents to dollars for display
        amount_dollars = offer.payout.amount / 100
        offer.reward_note = f"${amount_dollars:.2f} per {goal_text}"

    offer_id = str(uuid.uuid4())

    # Generate display ID for the offer
    offer_title = offer.offer_title or offer.title or product_data.get("title", "Untitled Offer")
    brand_name = None  # We could fetch this from brand collection if needed
    offer_display_id = ensure_unique_offer_display_id(offer_title, brand_name)

    offer_data = {
        **offer.model_dump(mode="json"),
        "brand_id": brand_id,
        "offer_id": offer_id,
        "offer_display_id": offer_display_id,
        "offer_status": offer.offer_status if offer.offer_status is not None else "inactive",  # Set offers as inactive by default
    }

    # Auto-generate marketing content if not provided (fallback for cases where /intel endpoint wasn't used)
    if not offer_data.get("offer_title") or not offer_data.get("offer_description"):
        try:
            from api.utils.marketing_content_generator import MarketingContentGenerator

            marketing_content = MarketingContentGenerator.generate_marketing_content(
                product_title=product_data["title"],
                product_description=product_data["description"],
                categories=product_data.get("categories", []),
                keywords=product_data.get("keywords", []),
                audience_segment=product_data.get("audience_segment")
            )

            # Use generated content if not provided in payload
            if not offer_data.get("offer_title"):
                offer_data["offer_title"] = marketing_content["offer_title"]
            if not offer_data.get("offer_description"):
                offer_data["offer_description"] = marketing_content["offer_description"]
            if not offer_data.get("feature_sections"):
                offer_data["feature_sections"] = marketing_content.get("feature_sections", [])

            logger.info(f"✅ Auto-generated marketing content with {len(marketing_content.get('feature_sections', []))} features for offer {offer_id}")

        except Exception as e:
            logger.error(f"Failed to generate marketing content: {str(e)}")
            # Continue without auto-generated content

    offer_data.update({
        "conversion_count": {
            "total": 0,
            "production": 0,
            "test": 0
        },
        "click_count": {
            "total": 0,
            "production": 0,
            "test": 0
        },
        "offer_views": {                # Track views for the offer
            "test": 0,                  # Test views
            "production": 0,            # Production views
            "total": 0                  # Total views
        },
        "total_spent": {
            "production": 0.0,
            "test": 0.0,
            "all": 0.0
        },
        "total_budget_available": 0.0,  # Available budget for the offer
        "total_budget_spent": 0.0,      # Budget spent for the offer
        "total_promo_available": 0.0,   # Available promo credit for the offer
        "total_promo_spent": 0.0,       # Promo credit spent for the offer
        "offer_total_budget_allocated": 0.0,  # Total budget allocated for the offer
        "offer_total_budget_spent": 0.0,      # Budget spent for the offer
        "offer_total_promo_spent": 0.0,       # Promo credit spent for the offer
        "offer_total_promo_available": 0.0,   # Available promo credit for the offer
        "promo_applied": False,         # Flag to indicate if promo credit has been applied
        "offer_trust_score": 100,       # Set offer_trust_score to 100 for all new offers
        "created_at": firestore.SERVER_TIMESTAMP
    })

    db.collection("offers").document(offer_id).set(offer_data)

    # Update brand and product references based on offer_status
    if offer_data["offer_status"] == "active":
        db.collection("brands").document(brand_id).update({
            "active_offers": firestore.ArrayUnion([offer_id]),
            "active_products": firestore.ArrayUnion([offer.product_id]),
            "inactive_offers": firestore.ArrayRemove([offer_id]),
            "inactive_products": firestore.ArrayRemove([offer.product_id]),
        })
        product_ref.update({
            "active_offers": firestore.ArrayUnion([offer_id]),
            "inactive_offers": firestore.ArrayRemove([offer_id]),
        })
    else:
        # For inactive or cancelled offers
        db.collection("brands").document(brand_id).update({
            "inactive_offers": firestore.ArrayUnion([offer_id]),
            "inactive_products": firestore.ArrayUnion([offer.product_id]),
            "active_offers": firestore.ArrayRemove([offer_id]),
            "active_products": firestore.ArrayRemove([offer.product_id]),
        })
        product_ref.update({
            "inactive_offers": firestore.ArrayUnion([offer_id]),
            "active_offers": firestore.ArrayRemove([offer_id]),
        })

    # Removed subscription usage tracking

    return {"status": "success", "offer_id": offer_id}

@router.patch("/{offer_identifier}")
async def update_offer(
    offer_identifier: str,
    update: OfferUpdate,
    user=Depends(require_role("brand"))
):
    # Removed subscription limits functionality

    # Resolve the identifier to actual document ID
    offer_id = resolve_offer_id(offer_identifier)
    if not offer_id:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()
    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    existing_offer = offer_doc.to_dict()
    brand_id = existing_offer["brand_id"]
    product_id = existing_offer["product_id"]
    update_data = update.model_dump(exclude_unset=True, mode="json")

    # Removed subscription limit checks for keywords and offers

    # Handle status changes with proper wallet transaction logic
    current_offer_status = existing_offer.get("offer_status", existing_offer.get("status", "inactive")).lower()
    new_offer_status = update_data.get("offer_status", current_offer_status)

    # Handle backward compatibility for active/status fields
    if "active" in update_data or "status" in update_data:
        # Convert old active + status to new offer_status
        if "active" in update_data and "status" in update_data:
            # Both provided - use status if active is True, otherwise inactive
            new_offer_status = update_data["status"].lower() if update_data["active"] else "inactive"
        elif "active" in update_data:
            # Only active provided - convert to offer_status
            new_offer_status = "active" if update_data["active"] else "inactive"
        elif "status" in update_data:
            # Only status provided - use it directly (convert to lowercase)
            new_offer_status = update_data["status"].lower()

        # Update the update_data to use offer_status
        update_data["offer_status"] = new_offer_status
        # Remove old fields from update_data
        update_data.pop("active", None)
        update_data.pop("status", None)

    # Determine if this is a wallet-affecting change
    wallet_change_needed = False
    wallet_operation = None
    activity_log_message = None

    # Status change logic
    if "offer_status" in update_data or "status" in update_data or "active" in update_data:
        # Determine the transition type
        if current_offer_status == "cancelled" and new_offer_status == "active":
            # cancelled → active: Reactivation (money moves from available to allocated)
            wallet_change_needed = True
            wallet_operation = "reactivate"
            activity_log_message = "Offer reactivated from cancelled status"

        elif current_offer_status == "active" and new_offer_status == "cancelled":
            # active → cancelled: Cancellation (money moves from allocated to available)
            wallet_change_needed = True
            wallet_operation = "cancel"
            activity_log_message = "Offer cancelled"

        elif current_offer_status in ["active", "inactive"] and new_offer_status in ["active", "inactive"]:
            # active ↔ inactive: Status-only change (no money movement)
            wallet_change_needed = False
            if current_offer_status != new_offer_status:
                if new_offer_status == "active":
                    activity_log_message = "Offer activated (status change only)"
                else:
                    activity_log_message = "Offer deactivated (status change only)"

        elif current_offer_status == "inactive" and new_offer_status == "active":
            # inactive → active: Status-only change (money already allocated)
            wallet_change_needed = False
            activity_log_message = "Offer activated from inactive status"

    # Handle wallet transactions with ACID compliance
    if wallet_change_needed:
        # Use Firestore transaction to ensure ACID properties
        @transactional
        def execute_wallet_transaction(transaction, wallet_operation, update_data):
            # Get fresh data within transaction
            offer_ref = db.collection("offers").document(offer_id)
            wallet_ref = db.collection("wallets").document(brand_id)

            # Read current state within transaction
            offer_doc = offer_ref.get(transaction=transaction)
            wallet_doc = wallet_ref.get(transaction=transaction)

            if not offer_doc.exists:
                raise HTTPException(status_code=404, detail="Offer not found")
            if not wallet_doc.exists:
                raise HTTPException(status_code=400, detail="Wallet not found")

            current_offer = offer_doc.to_dict()
            current_wallet = wallet_doc.to_dict()

            if wallet_operation == "cancel":
                # Calculate remaining budget to refund
                total_allocated = current_offer.get("offer_total_budget_allocated", 0.0)
                total_spent = current_offer.get("offer_total_budget_spent", 0.0)
                remaining_budget = total_allocated - total_spent

                if remaining_budget > 0:
                    current_available = current_wallet.get("total_available_balance", 0.0)
                    current_allocated = current_wallet.get("total_budget_allocated", 0.0)

                    # Validate that we have enough allocated budget to refund
                    if current_allocated < remaining_budget:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Insufficient allocated budget. Allocated: ${current_allocated:.2f}, Refund: ${remaining_budget:.2f}"
                        )

                    new_available = current_available + remaining_budget
                    new_allocated = current_allocated - remaining_budget

                    # Update wallet within transaction
                    transaction.update(wallet_ref, {
                        "total_available_balance": new_available,
                        "total_budget_allocated": new_allocated,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })

                    # Update offer within transaction
                    transaction.update(offer_ref, {
                        **update_data,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })

                    # Return data for transaction logging (done outside transaction)
                    return {
                        "success": True,
                        "operation": "refund",
                        "amount": remaining_budget,
                        "balance_after": new_available,
                        "description": f"Budget refund from cancelled offer: {current_offer.get('offer_title', 'Untitled')}"
                    }
                else:
                    # No refund needed, just update offer
                    transaction.update(offer_ref, {
                        **update_data,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    })
                    return {"success": True, "operation": "cancel_no_refund"}

            elif wallet_operation == "reactivate":
                budget_amount = update_data.get("offer_total_budget_allocated")
                if not budget_amount or budget_amount <= 0:
                    raise HTTPException(
                        status_code=400,
                        detail="Budget amount required for offer reactivation. Please specify offer_total_budget_allocated."
                    )

                current_available = current_wallet.get("total_available_balance", 0.0)
                current_allocated = current_wallet.get("total_budget_allocated", 0.0)

                # Validate sufficient balance
                if current_available < budget_amount:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Insufficient wallet balance. Available: ${current_available:.2f}, Required: ${budget_amount:.2f}"
                    )

                new_available = current_available - budget_amount
                new_allocated = current_allocated + budget_amount

                # Ensure allocated budget never goes negative
                if new_allocated < 0:
                    raise HTTPException(
                        status_code=400,
                        detail="Invalid operation: allocated budget cannot be negative"
                    )

                # Update wallet within transaction
                transaction.update(wallet_ref, {
                    "total_available_balance": new_available,
                    "total_budget_allocated": new_allocated,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

                # Update offer with fresh budget allocation
                offer_updates = {
                    **update_data,
                    "offer_total_budget_spent": 0.0,
                    "offer_total_promo_spent": 0.0,
                    "remaining_budget": budget_amount,
                    "reactivated_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }

                transaction.update(offer_ref, offer_updates)

                # Return data for transaction logging (done outside transaction)
                return {
                    "success": True,
                    "operation": "allocate",
                    "amount": budget_amount,
                    "balance_after": new_available,
                    "description": f"Budget allocated for reactivated offer: {current_offer.get('offer_title', 'Untitled')}"
                }

            return {"success": False}

        # Execute the transaction
        transaction = db.transaction()
        try:
            result = execute_wallet_transaction(transaction, wallet_operation, update_data)

            # Create transaction log outside of Firestore transaction (to avoid nested transactions)
            if result.get("success") and result.get("operation") in ["refund", "allocate"]:
                from api.routes.brands import create_wallet_transaction

                tx_type = "credit" if result["operation"] == "refund" else "debit"
                tx_category = "budget_refund" if result["operation"] == "refund" else "budget_allocation"

                create_wallet_transaction(
                    brand_id=brand_id,
                    transaction_type=tx_type,
                    category=tx_category,
                    amount=result["amount"],
                    description=result["description"],
                    balance_after=result["balance_after"],
                    reference_id=offer_id,
                    reference_type="offer"
                )

                logger.info(f"💰 ACID Transaction: {result['operation']} ${result['amount']:.2f} for offer {offer_id}")

        except Exception as e:
            logger.error(f"❌ ACID Transaction failed for offer {offer_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Transaction failed: {str(e)}")

    else:
        # No wallet changes needed, just update the offer
        update_data["updated_at"] = firestore.SERVER_TIMESTAMP
        offer_ref.update(update_data)

    # Update brand and product references for status changes (not cancellation)
    if "offer_status" in update_data and new_offer_status != "cancelled":
        brand_ref = db.collection("brands").document(brand_id)
        product_id = existing_offer["product_id"]  # Ensure product_id is available in this scope
        product_ref = db.collection("products").document(product_id)

        if new_offer_status == "active":
            brand_ref.update({
                "inactive_offers": firestore.ArrayRemove([offer_id]),
                "active_offers": firestore.ArrayUnion([offer_id])
            })
            product_ref.update({
                "inactive_offers": firestore.ArrayRemove([offer_id]),
                "active_offers": firestore.ArrayUnion([offer_id])
            })
        else:  # inactive
            brand_ref.update({
                "active_offers": firestore.ArrayRemove([offer_id]),
                "inactive_offers": firestore.ArrayUnion([offer_id])
            })
            product_ref.update({
                "active_offers": firestore.ArrayRemove([offer_id]),
                "inactive_offers": firestore.ArrayUnion([offer_id])
            })

    # Create activity log entry
    if activity_log_message:
        try:
            # Create offer activity log
            activity_data = {
                "offer_id": offer_id,
                "brand_id": brand_id,
                "action": "status_change",
                "message": activity_log_message,
                "previous_offer_status": current_offer_status,
                "new_offer_status": new_offer_status,
                "wallet_transaction": wallet_change_needed,
                "timestamp": firestore.SERVER_TIMESTAMP
            }

            db.collection("offers").document(offer_id).collection("activity_log").add(activity_data)
            logger.info(f"📝 Activity log: {activity_log_message} for offer {offer_id}")

        except Exception as e:
            logger.warning(f"Failed to create activity log for offer {offer_id}: {str(e)}")

    return {
        "status": "success",
        "message": "Offer updated",
        "wallet_transaction": wallet_change_needed,
        "activity_message": activity_log_message
    }

@router.post("/{offer_identifier}/apply-promo")
async def apply_promo_credit(
    offer_identifier: str,
    user=Depends(require_role("brand"))
):
    """
    Apply promotional credit to an offer

    This endpoint applies available promotional credit from the brand's wallet to the offer.
    The promo credit will be used for conversions before regular budget.

    Args:
        offer_identifier: The ID or display ID of the offer to apply promo credit to

    Returns:
        Success message with updated offer details

    Raises:
        HTTPException: If the offer is not found or the user is not authorized
    """
    # Get brand_id from authenticated user
    brand_id = user["uid"]

    # Resolve the identifier to actual document ID
    offer_id = resolve_offer_id(offer_identifier)
    if not offer_id:
        raise HTTPException(status_code=404, detail="Offer not found")

    # Get the offer document
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()

    # Check if the offer belongs to the authenticated brand
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this offer")

    # Check if promo credit has already been applied
    if offer_data.get("promo_applied", False):
        return {
            "status": "success",
            "message": "Promo credit has already been applied to this offer",
            "offer_id": offer_id,
            "promo_applied": True,
            "total_promo_available": offer_data.get("total_promo_available", 0.0)
        }

    # Get the brand's wallet
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        raise HTTPException(status_code=404, detail="Brand wallet not found")

    wallet_data = wallet_doc.to_dict()

    # Check if there's promo credit available
    promo_available = wallet_data.get("total_promo_available_balance", 0.0)

    if promo_available <= 0:
        return {
            "status": "error",
            "message": "No promotional credit available in your wallet",
            "offer_id": offer_id,
            "promo_applied": False
        }

    # Get the offer budget
    offer_budget = offer_data.get("offer_total_budget_allocated", 0.0)

    # Calculate the amount of promo credit to apply (up to the offer budget)
    promo_to_apply = min(promo_available, offer_budget)

    # Update the offer with promo credit
    offer_ref.update({
        "promo_applied": True,
        "total_promo_available": promo_to_apply,
        "offer_total_promo_available": promo_to_apply,
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    # Update the wallet
    wallet_ref.update({
        "total_promo_available_balance": firestore.Increment(-promo_to_apply),
        "total_budget_allocated": firestore.Increment(promo_to_apply),
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    return {
        "status": "success",
        "message": f"Successfully applied ${promo_to_apply/100:.2f} in promotional credit to this offer",
        "offer_id": offer_id,
        "promo_applied": True,
        "total_promo_available": promo_to_apply
    }

@router.delete("/{offer_identifier}")
async def delete_offer(offer_identifier: str, user=Depends(require_role("brand"))):
    # Removed subscription limits functionality

    # Resolve the identifier to actual document ID
    offer_id = resolve_offer_id(offer_identifier)
    if not offer_id:
        raise HTTPException(status_code=404, detail="Offer not found")

    # Get the offer document
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    # Get offer data
    offer_data = offer_doc.to_dict()
    brand_id = offer_data.get("brand_id")
    product_id = offer_data.get("product_id")
    was_active = offer_data.get("active", False)

    # Security check: ensure the user can only delete their own offers
    if brand_id != user["uid"]:
        logger.warning(f"Unauthorized delete attempt: User {user['uid']} tried to delete offer {offer_id} owned by {brand_id}")
        raise HTTPException(status_code=403, detail="You can only delete your own offers")

    # Check if offer has active conversions
    conversion_count_data = offer_data.get("conversion_count", 0)

    # Handle both old integer format and new dictionary format
    if isinstance(conversion_count_data, dict):
        conversion_count = conversion_count_data.get("total", 0)
    else:
        conversion_count = conversion_count_data or 0

    if conversion_count > 0:
        logger.info(f"Offer {offer_id} has {conversion_count} conversions but will be deleted anyway")

    # Delete the offer
    offer_ref.delete()
    logger.info(f"Offer deleted: {offer_id} by brand {brand_id}")

    # Update brand and product references
    if brand_id:
        brand_ref = db.collection("brands").document(brand_id)
        brand_ref.update({
            "active_offers": firestore.ArrayRemove([offer_id]),
            "inactive_offers": firestore.ArrayRemove([offer_id])
        })

    if product_id:
        product_ref = db.collection("products").document(product_id)
        product_ref.update({
            "active_offers": firestore.ArrayRemove([offer_id]),
            "inactive_offers": firestore.ArrayRemove([offer_id])
        })

        # If the offer was active, update the active offer count in the subscription
        # Removed subscription usage tracking

    return {"status": "success", "message": "Offer deleted successfully"}

@router.post("/{offer_identifier}/cancel")
async def cancel_offer(offer_identifier: str, user=Depends(require_role("brand"))):
    """
    Cancel an offer and return remaining budget to the brand's wallet
    """
    try:
        # Resolve the identifier to actual document ID
        offer_id = resolve_offer_id(offer_identifier)
        if not offer_id:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()
        brand_id = offer_data.get("brand_id")
        product_id = offer_data.get("product_id")  # Get product_id for transaction

        # Security check: ensure the user can only cancel their own offers
        if brand_id != user["uid"]:
            logger.warning(f"Unauthorized cancel attempt: User {user['uid']} tried to cancel offer {offer_id} owned by {brand_id}")
            raise HTTPException(status_code=403, detail="You can only cancel your own offers")

        # Check if offer is already cancelled or inactive
        current_offer_status = offer_data.get("offer_status", offer_data.get("status", "inactive")).lower()
        if current_offer_status in ["cancelled", "inactive"]:
            raise HTTPException(status_code=400, detail=f"Offer is already {current_offer_status}")

        # Use ACID transaction for cancellation
        @transactional
        def cancel_offer_transaction(transaction):
            # Get fresh data within transaction
            offer_ref = db.collection("offers").document(offer_id)
            wallet_ref = db.collection("wallets").document(brand_id)
            brand_ref = db.collection("brands").document(brand_id)
            product_ref = db.collection("products").document(product_id)

            # Read current state within transaction
            offer_doc = offer_ref.get(transaction=transaction)
            wallet_doc = wallet_ref.get(transaction=transaction)

            if not offer_doc.exists:
                raise HTTPException(status_code=404, detail="Offer not found")
            if not wallet_doc.exists:
                raise HTTPException(status_code=400, detail="Wallet not found")

            current_offer = offer_doc.to_dict()
            current_wallet = wallet_doc.to_dict()

            # Verify offer is still active (prevent double cancellation)
            current_offer_status = current_offer.get("offer_status", current_offer.get("status", "inactive")).lower()
            if current_offer_status != "active":
                raise HTTPException(status_code=400, detail=f"Offer is already {current_offer_status}")

            # Calculate remaining budget
            total_allocated = current_offer.get("offer_total_budget_allocated", 0.0)
            total_spent = current_offer.get("offer_total_budget_spent", 0.0)
            remaining_budget = total_allocated - total_spent

            # Update offer status within transaction
            transaction.update(offer_ref, {
                "offer_status": "cancelled",
                "cancelled_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            # Remove from brand/product offer lists within transaction
            transaction.update(brand_ref, {
                "active_offers": firestore.ArrayRemove([offer_id]),
                "inactive_offers": firestore.ArrayRemove([offer_id])
            })
            transaction.update(product_ref, {
                "active_offers": firestore.ArrayRemove([offer_id]),
                "inactive_offers": firestore.ArrayRemove([offer_id])
            })

            # Handle wallet refund if there's remaining budget
            if remaining_budget > 0:
                current_available = current_wallet.get("total_available_balance", 0.0)
                current_allocated = current_wallet.get("total_budget_allocated", 0.0)

                # Validate that we have enough allocated budget to refund
                if current_allocated < remaining_budget:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Insufficient allocated budget. Allocated: ${current_allocated:.2f}, Refund: ${remaining_budget:.2f}"
                    )

                new_available = current_available + remaining_budget
                new_allocated = current_allocated - remaining_budget

                # Ensure allocated budget never goes negative
                if new_allocated < 0:
                    raise HTTPException(
                        status_code=400,
                        detail="Invalid operation: allocated budget cannot be negative"
                    )

                # Update wallet within transaction
                transaction.update(wallet_ref, {
                    "total_available_balance": new_available,
                    "total_budget_allocated": new_allocated,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

                return {
                    "remaining_budget": remaining_budget,
                    "balance_after": new_available,
                    "offer_title": current_offer.get("offer_title", "Untitled")
                }
            else:
                return {
                    "remaining_budget": 0.0,
                    "balance_after": current_wallet.get("total_available_balance", 0.0),
                    "offer_title": current_offer.get("offer_title", "Untitled")
                }

        # Execute the ACID transaction
        transaction = db.transaction()
        try:
            result = cancel_offer_transaction(transaction)

            # Create transaction log outside of Firestore transaction (to avoid nested transactions)
            if result["remaining_budget"] > 0:
                from api.routes.brands import create_wallet_transaction
                create_wallet_transaction(
                    brand_id=brand_id,
                    transaction_type="credit",
                    category="budget_refund",
                    amount=result["remaining_budget"],
                    description=f"Budget refund from cancelled offer: {result['offer_title']}",
                    balance_after=result["balance_after"],
                    reference_id=offer_id,
                    reference_type="offer"
                )

        except Exception as e:
            logger.error(f"❌ ACID Transaction failed for offer cancellation {offer_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Cancellation failed: {str(e)}")

        logger.info(f"Offer cancelled: {offer_id} by brand {brand_id}, refunded ${result['remaining_budget']:.2f}")

        return {
            "status": "success",
            "message": "Offer cancelled successfully",
            "offer_id": offer_id,
            "remaining_budget_refunded": result["remaining_budget"],
            "offer_title": offer_data.get("offer_title", "Untitled")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling offer {offer_identifier}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cancel offer")

class ReinstateOfferRequest(BaseModel):
    budget_amount: float  # New budget amount to allocate

@router.post("/{offer_identifier}/reinstate")
async def reinstate_offer(
    offer_identifier: str,
    request: ReinstateOfferRequest,
    user=Depends(require_role("brand"))
):
    """
    Reinstate a cancelled offer with new budget allocation
    """
    try:
        # Resolve the identifier to actual document ID
        offer_id = resolve_offer_id(offer_identifier)
        if not offer_id:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()
        brand_id = offer_data.get("brand_id")

        # Security check: ensure the user can only reinstate their own offers
        if brand_id != user["uid"]:
            logger.warning(f"Unauthorized reinstate attempt: User {user['uid']} tried to reinstate offer {offer_id} owned by {brand_id}")
            raise HTTPException(status_code=403, detail="You can only reinstate your own offers")

        # Check if offer is actually cancelled
        current_offer_status = offer_data.get("offer_status", offer_data.get("status", "inactive")).lower()
        if current_offer_status != "cancelled":
            raise HTTPException(status_code=400, detail="Only cancelled offers can be reinstated")

        # Validate budget amount
        if request.budget_amount <= 0:
            raise HTTPException(status_code=400, detail="Budget amount must be greater than 0")

        if request.budget_amount < 1.0:  # Minimum $1
            raise HTTPException(status_code=400, detail="Minimum budget amount is $1.00")

        # Use ACID transaction for reinstatement
        @transactional
        def reinstate_offer_transaction(transaction):
            # Get fresh data within transaction
            offer_ref = db.collection("offers").document(offer_id)
            wallet_ref = db.collection("wallets").document(brand_id)
            brand_ref = db.collection("brands").document(brand_id)
            product_ref = db.collection("products").document(offer_data.get("product_id"))

            # Read current state within transaction
            offer_doc = offer_ref.get(transaction=transaction)
            wallet_doc = wallet_ref.get(transaction=transaction)

            if not offer_doc.exists:
                raise HTTPException(status_code=404, detail="Offer not found")
            if not wallet_doc.exists:
                raise HTTPException(status_code=400, detail="Wallet not found")

            current_offer = offer_doc.to_dict()
            current_wallet = wallet_doc.to_dict()

            # Verify offer is cancelled (prevent double reinstatement)
            current_offer_status = current_offer.get("offer_status", current_offer.get("status", "inactive")).lower()
            if current_offer_status != "cancelled":
                raise HTTPException(status_code=400, detail="Only cancelled offers can be reinstated")

            # Check wallet balance
            current_available = current_wallet.get("total_available_balance", 0.0)
            current_allocated = current_wallet.get("total_budget_allocated", 0.0)

            if current_available < request.budget_amount:
                raise HTTPException(
                    status_code=400,
                    detail=f"Insufficient wallet balance. Available: ${current_available:.2f}, Required: ${request.budget_amount:.2f}"
                )

            new_available = current_available - request.budget_amount
            new_allocated = current_allocated + request.budget_amount

            # Ensure balances are valid
            if new_available < 0:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid operation: available balance cannot be negative"
                )
            if new_allocated < 0:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid operation: allocated budget cannot be negative"
                )

            # Update offer within transaction
            transaction.update(offer_ref, {
                "offer_status": "active",
                "offer_total_budget_allocated": request.budget_amount,
                "remaining_budget": request.budget_amount,  # Set remaining budget to full allocated amount
                "reactivated_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
                # Note: Preserving all historical performance metrics (clicks, conversions, spent amounts)
                # This maintains valuable historical data for brands
            })

            # Update brand/product offer lists within transaction
            transaction.update(brand_ref, {
                "active_offers": firestore.ArrayUnion([offer_id]),
                "inactive_offers": firestore.ArrayRemove([offer_id])  # Remove from inactive if present
            })
            transaction.update(product_ref, {
                "active_offers": firestore.ArrayUnion([offer_id]),
                "inactive_offers": firestore.ArrayRemove([offer_id])  # Remove from inactive if present
            })

            # Update wallet within transaction
            transaction.update(wallet_ref, {
                "total_available_balance": new_available,
                "total_budget_allocated": new_allocated,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            return {
                "budget_amount": request.budget_amount,
                "balance_after": new_available,
                "offer_title": current_offer.get("offer_title", "Untitled")
            }

        # Execute the ACID transaction
        transaction = db.transaction()
        try:
            result = reinstate_offer_transaction(transaction)

            # Create transaction log outside of Firestore transaction (to avoid nested transactions)
            from api.routes.brands import create_wallet_transaction
            create_wallet_transaction(
                brand_id=brand_id,
                transaction_type="debit",
                category="budget_allocation",
                amount=result["budget_amount"],
                description=f"Budget allocated for reinstated offer: {result['offer_title']}",
                balance_after=result["balance_after"],
                reference_id=offer_id,
                reference_type="offer"
            )

        except Exception as e:
            logger.error(f"❌ ACID Transaction failed for offer reinstatement {offer_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Reinstatement failed: {str(e)}")

        logger.info(f"Offer reinstated: {offer_id} by brand {brand_id}, allocated ${request.budget_amount:.2f}")

        return {
            "status": "success",
            "message": "Offer reinstated successfully",
            "offer_id": offer_id,
            "budget_allocated": request.budget_amount,
            "remaining_wallet_balance": result["balance_after"],
            "offer_title": result["offer_title"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reinstating offer {offer_identifier}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to reinstate offer")

@router.get("/get/{offer_identifier}")
async def get_offer_by_id(offer_identifier: str, user=Depends(require_role("brand"))):
    """
    Get a specific offer by ID or display ID

    This endpoint retrieves a single offer by its ID or display ID. The user must be authenticated
    and have the 'brand' role. The offer must belong to the authenticated brand.

    Args:
        offer_identifier: The ID or display ID of the offer to retrieve

    Returns:
        The offer data if found, with all fields properly formatted

    Raises:
        HTTPException: If the offer is not found or the user is not authorized
    """
    # Get brand_id from authenticated user
    brand_id = user["uid"]

    # Resolve the identifier to actual document ID
    offer_id = resolve_offer_id(offer_identifier)
    if not offer_id:
        raise HTTPException(status_code=404, detail="Offer not found")

    # Get the offer document
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()

    # Check if the offer belongs to the authenticated brand
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to access this offer")

    # Add the ID to the offer data
    offer_data["id"] = offer_id
    offer_data["offer_id"] = offer_id  # Add offer_id for consistency
    offer_data["offer_display_id"] = offer_data.get("offer_display_id", offer_id)  # Add display ID

    # Get product information if available
    product_id = offer_data.get("product_id")
    view_count = 0
    if product_id:
        product_doc = db.collection("products").document(product_id).get()
        if product_doc.exists:
            product_data = product_doc.to_dict()
            view_count = product_data.get("view_count", 0)
            offer_data["view_count"] = view_count

    # Get wallet information
    wallet_data = None
    if brand_id:
        wallet_ref = db.collection("wallets").document(brand_id)
        wallet_doc = wallet_ref.get()
        if wallet_doc.exists:
            wallet_data = wallet_doc.to_dict()
            offer_data["wallet"] = wallet_data
            print(f"DEBUG - Wallet data for brand {brand_id}: {wallet_data}")
        else:
            print(f"DEBUG - No wallet found for brand {brand_id}")
            # Create default wallet data
            default_wallet = {
                "brand_id": brand_id,
                "total_available_balance": 0.0,
                "total_promo_available_balance": 0.0,
                "total_promo_balance_spent": 0.0,
                "total_balance_spent": 0.0,
                "total_budget_allocated": 0.0
            }
            offer_data["wallet"] = default_wallet

    # Normalize clicks - extract both total and production clicks
    clicks_data = offer_data.get("click_count", 0)
    if isinstance(clicks_data, dict):
        clicks_total = clicks_data.get("total", 0)
        clicks_production = clicks_data.get("production", 0)
        clicks_test = clicks_data.get("test", 0)

        # Add explicit fields for easier access
        offer_data["clicks"] = clicks_total
        offer_data["clicks_production"] = clicks_production
        offer_data["clicks_test"] = clicks_test
    else:
        # Legacy format - assume all are production
        clicks_total = clicks_production = clicks_data
        clicks_test = 0

        # Add explicit fields for easier access
        offer_data["clicks"] = clicks_total
        offer_data["clicks_production"] = clicks_production
        offer_data["clicks_test"] = 0

        # Update click_count to use the new format
        offer_data["click_count"] = {
            "total": clicks_total,
            "production": clicks_production,
            "test": 0
        }

    # Normalize conversion counts - extract both total and production conversions
    conversion_data = offer_data.get("conversion_count", 0)
    if isinstance(conversion_data, dict):
        conversions_production = conversion_data.get("production", 0)
        conversions_test = conversion_data.get("test", 0)
        conversions_total = conversion_data.get("total", conversions_production + conversions_test)

        # Add explicit fields for easier access
        offer_data["conversions"] = conversions_production
        offer_data["conversions_production"] = conversions_production
        offer_data["conversions_test"] = conversions_test
        offer_data["conversions_total"] = conversions_total
    else:
        # Legacy format - assume all are production
        conversions_total = conversions_production = conversion_data
        conversions_test = 0

        # Add explicit fields for easier access
        offer_data["conversions"] = conversions_production
        offer_data["conversions_production"] = conversions_production
        offer_data["conversions_test"] = 0
        offer_data["conversions_total"] = conversions_total

        # Update conversion_count to use the new format
        offer_data["conversion_count"] = {
            "total": conversions_total,
            "production": conversions_production,
            "test": 0
        }

    # Normalize total spent
    spent_data = offer_data.get("total_spent", 0.0)
    if isinstance(spent_data, dict):
        production_spent = spent_data.get("production", 0.0)
        test_spent = spent_data.get("test", 0.0)
        total_spent = production_spent + test_spent

        # Add explicit field for easier access
        offer_data["total_spent_offer"] = production_spent
        offer_data["spent"] = production_spent
    else:
        # Legacy format - assume all is production
        production_spent = spent_data
        test_spent = 0.0
        total_spent = production_spent

        # Add explicit field for easier access
        offer_data["total_spent_offer"] = production_spent
        offer_data["spent"] = production_spent

        # Update total_spent to use the new format
        offer_data["total_spent"] = {
            "production": production_spent,
            "test": 0.0,
            "all": production_spent
        }

    # Calculate CTR using only production clicks and conversions (excluding test clicks and conversions)
    if clicks_production > 0:
        ctr = round((conversions_production / clicks_production * 100), 1)
    else:
        ctr = 0
    offer_data["ctr"] = ctr

    # Ensure tracking information is present
    if "tracking" not in offer_data:
        offer_data["tracking"] = {
            "method": "redirect_pixel",
            "redirect_url": "",
            "target_urls": []
        }

    # Ensure payout information is properly formatted
    if "payout" in offer_data and offer_data["payout"]:
        if "model" not in offer_data["payout"]:
            offer_data["payout"]["model"] = "CPA"
    else:
        offer_data["payout"] = {
            "amount": 0,
            "currency": "USD",
            "model": "CPA"
        }

    # Ensure other required fields are present
    if "keywords" not in offer_data:
        offer_data["keywords"] = []
    if "categories" not in offer_data:
        offer_data["categories"] = []
    if "offer_description" not in offer_data:
        offer_data["offer_description"] = ""
    if "url" not in offer_data:
        offer_data["url"] = ""
    if "reward_note" not in offer_data:
        offer_data["reward_note"] = ""
    if "offer_trust_score" not in offer_data:
        offer_data["offer_trust_score"] = 50.0

    # For backward compatibility, also set trust_score if it doesn't exist
    if "trust_score" not in offer_data:
        offer_data["trust_score"] = offer_data["offer_trust_score"]
    if "goal" not in offer_data:
        offer_data["goal"] = "signup"
    if "offer_total_budget_allocated" not in offer_data:
        offer_data["offer_total_budget_allocated"] = 0.0

    # Handle backward compatibility for offer_status
    if "offer_status" not in offer_data:
        # Convert from old active + status fields
        if "active" in offer_data and "status" in offer_data:
            offer_data["offer_status"] = offer_data["status"].lower() if offer_data["active"] else "inactive"
        elif "active" in offer_data:
            offer_data["offer_status"] = "active" if offer_data["active"] else "inactive"
        elif "status" in offer_data:
            offer_data["offer_status"] = offer_data["status"].lower()
        else:
            offer_data["offer_status"] = "inactive"

    # Add backward compatibility fields
    offer_data["active"] = offer_data["offer_status"] == "active"
    offer_data["status"] = offer_data["offer_status"]

    return offer_data

@router.get("/brand/all")
async def get_all_brand_offers(request: Request, user=Depends(require_role("brand"))):
    print("Fetching offers for brand")
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise HTTPException(status_code=401, detail="Missing auth token")

    try:
        token = auth_header.replace("Bearer ", "")
        decoded = firebase_auth.verify_id_token(token)
        brand_id = decoded["uid"]
        print(f"Brand ID: {brand_id}")
    except Exception as e:
        logger.error(f"Invalid Firebase token: {e}")
        raise HTTPException(status_code=401, detail="Invalid Firebase token")

    offers_query = db.collection("offers").where("brand_id", "==", brand_id)
    offers_snapshot = offers_query.stream()

    offers = []
    offer_clicks_total = 0  # All clicks (test + production)
    offer_clicks_production = 0  # Only production clicks
    offer_conversions_production = 0  # Only production conversions
    offer_conversions_test = 0  # Only test conversions
    offer_conversions_total = 0  # All conversions (test + production)
    total_budget = 0.0
    total_spent = 0.0
    total_view_count = 0  # Total view count from products
    total_offer_views_production = 0  # Total production views from offers
    total_offer_views_test = 0  # Total test views from offers

    for doc in offers_snapshot:
        data = doc.to_dict()
        offer_id = doc.id
        product_id = data.get("product_id")

        # Get view_count from the product document
        view_count = 0
        if product_id:
            product_doc = db.collection("products").document(product_id).get()
            if product_doc.exists:
                product_data = product_doc.to_dict()
                view_count = product_data.get("view_count", 0)
                # Add to total view count
                total_view_count += view_count

        # Extract offer_views data
        offer_views_data = data.get("offer_views", {})
        offer_views_production = 0
        offer_views_test = 0
        offer_views_total = 0

        if isinstance(offer_views_data, dict):
            offer_views_production = offer_views_data.get("production", 0)
            offer_views_test = offer_views_data.get("test", 0)
            offer_views_total = offer_views_data.get("total", 0)

            # Add to total offer views
            total_offer_views_production += offer_views_production
            total_offer_views_test += offer_views_test

        # Normalize clicks - extract both total and production clicks
        clicks_data = data.get("click_count", 0)
        clicks_total = clicks_production = clicks_test = 0

        if isinstance(clicks_data, dict):
            clicks_total = clicks_data.get("total", 0)
            clicks_production = clicks_data.get("production", 0)
            clicks_test = clicks_data.get("test", 0)
        else:
            # Legacy format - assume all are production
            clicks_total = clicks_production = clicks_data

        # Normalize conversion counts - extract both total and production conversions
        conversion_data = data.get("conversion_count", 0)
        conversions_production = conversions_test = conversions_total = 0

        if isinstance(conversion_data, dict):
            conversions_production = conversion_data.get("production", 0)
            conversions_test = conversion_data.get("test", 0)
            conversions_total = conversion_data.get("total", conversions_production + conversions_test)
        else:
            # Legacy format - assume all are production
            conversions_total = conversions_production = conversion_data

        # Get budget from offer_total_budget_allocated
        budget = data.get("offer_total_budget_allocated", 0.0)

        # Normalize total spent
        spent_data = data.get("total_spent", 0.0)
        production_spent = test_spent = 0.0

        if isinstance(spent_data, dict):
            production_spent = spent_data.get("production", 0.0)
            test_spent = spent_data.get("test", 0.0)
            spent = production_spent
        else:
            # Legacy format - assume all is production
            spent = production_spent = spent_data

        # Accumulate totals for all offers
        offer_clicks_total += clicks_total
        offer_clicks_production += clicks_production
        offer_conversions_test += conversions_test
        offer_conversions_production += conversions_production
        offer_conversions_total += conversions_total
        total_budget += budget
        total_spent += production_spent

        # Create a comprehensive offer object with all fields
        offer_obj = {
            "id": offer_id,
            "offer_id": offer_id,  # Add offer_id for consistency
            "offer_display_id": data.get("offer_display_id", offer_id),  # Add display ID
            "brand_id": data.get("brand_id", brand_id),
            "product_id": product_id,

            # Basic information
            "offer_title": data.get("offer_title", "Untitled Offer"),
            "offer_description": data.get("offer_description", ""),
            "url": data.get("url", ""),
            "keywords": data.get("keywords", []),
            "categories": data.get("categories", []),
            "view_count": view_count,  # Add view_count from product

            # Click tracking
            "clicks": clicks_total,
            "clicks_production": clicks_production,
            "clicks_test": clicks_data.get("test", 0) if isinstance(clicks_data, dict) else 0,
            "click_count": {
                "total": clicks_total,
                "production": clicks_production,
                "test": clicks_data.get("test", 0) if isinstance(clicks_data, dict) else 0
            },

            # Conversion tracking
            "conversions": conversions_production,
            "conversions_total": conversions_total,
            "conversions_test": conversions_test,
            "conversions_production": conversions_production,
            "conversion_count": {
                "total": conversions_total,
                "production": conversions_production,
                "test": conversions_test
            },

            # Financial data
            "offer_total_budget_allocated": budget,
            "offer_total_budget_spent": data.get("offer_total_budget_spent", 0.0),
            "offer_total_promo_spent": data.get("offer_total_promo_spent", 0.0),
            "offer_total_promo_available": data.get("offer_total_promo_available", 0.0),
            "offer_intial_promo_balance": data.get("offer_intial_promo_balance", 0.0),
            "promo_conversions_left": data.get("promo_conversions_left", 0),
            "promo_applied": data.get("promo_applied", False),
            "total_spent_offer": production_spent,
            "total_spent": {
                "production": production_spent,
                "test": test_spent,
                "all": production_spent + test_spent
            },
            "spent": spent,

            # Metrics - calculate CTR using only production clicks and conversions (excluding test clicks and conversions)
            "ctr": round((conversions_production / clicks_production * 100), 1) if clicks_production > 0 else 0,

            # Status - properly handle cancelled, active, and inactive states using offer_status
            "offer_status": data.get("offer_status", data.get("status", "inactive")).lower(),
            "status": data.get("offer_status", data.get("status", "inactive")).lower(),  # Backward compatibility
            "active": data.get("offer_status", data.get("status", "inactive")).lower() == "active",

            # Payout and reward
            "reward_note": data.get("reward_note") or (
                f"${data['payout']['amount'] / 100} {data['payout'].get('currency', 'USD')}"
                if data.get("payout") and data.get("payout").get("amount") else "—"
            ),
            "payout": data.get("payout", {
                "amount": 0,
                "currency": "USD",
                "model": "CPA"
            }),

            # Goal and trust score
            "goal": data.get("goal", "signup"),
            "trust_score": data.get("trust_score", 50.0),

            # Timestamps
            "created_at": data.get("created_at"),
            "updated_at": data.get("updated_at"),
            "last_converted_at": data.get("last_converted_at"),

            # Tracking information
            "tracking": data.get("tracking", {
                "method": "redirect_pixel",
                "redirect_url": "",
                "target_urls": []
            }),

            # Additional metadata
            "meta": data.get("meta", {}),
            "valid_until": data.get("valid_until"),
            "suggestion_reason": data.get("suggestion_reason")
        }

        # Add the offer to the list
        offers.append(offer_obj)

    brand_doc = db.collection("brands").document(brand_id).get()
    product_clicks = 0
    product_conversions_production = 0

    if brand_doc.exists:
        brand_data = brand_doc.to_dict()
        product_ids = brand_data.get("active_products", [])
        print(f"Product IDs: {product_ids}")

        for pid in product_ids:
            product_doc = db.collection("products").document(pid).get()
            if product_doc.exists:
                pdata = product_doc.to_dict()
                print(f"Product data: {pdata}")
                product_clicks += pdata.get("clicks", 0)
                product_conversions_production += pdata.get("conversions_production", pdata.get("conversions", 0))

    total_clicks = int(product_clicks) + int(offer_clicks_total)
    total_clicks_production = int(product_clicks) + int(offer_clicks_production)
    total_conversions_production = int(product_conversions_production) + int(offer_conversions_production)
    total_conversions_test = int(offer_conversions_test)
    total_conversions_all = total_conversions_production + total_conversions_test

    # Calculate overall CTR using only production clicks and conversions (excluding test clicks and conversions)
    overall_ctr = round((total_conversions_production / total_clicks_production * 100), 1) if total_clicks_production > 0 else 0

    return {
        "status": "success",
        "offers": offers,
        "totals": {
            # View counts - combine product views and offer views
            "view_count": total_view_count + total_offer_views_production,
            "offer_views": {
                "total": total_offer_views_production + total_offer_views_test,
                "production": total_offer_views_production,
                "test": total_offer_views_test
            },
            "product_views": total_view_count,

            # Click counts
            "clicks": total_clicks,
            "clicks_production": total_clicks_production,
            "clicks_test": offer_clicks_total - offer_clicks_production,
            "click_count": {
                "total": total_clicks,
                "production": total_clicks_production,
                "test": offer_clicks_total - offer_clicks_production
            },

            # Conversion counts
            "conversions": total_conversions_production,
            "conversions_test": total_conversions_test,
            "conversions_all": total_conversions_all,
            "conversions_production": total_conversions_production,
            "conversions_total": total_conversions_all,
            "conversion_count": {
                "total": total_conversions_all,
                "production": total_conversions_production,
                "test": total_conversions_test
            },

            # Financial data
            "offer_total_budget_allocated": total_budget,
            "offer_total_budget_spent": sum(offer.get("offer_total_budget_spent", 0.0) for offer in offers),
            "offer_total_promo_spent": sum(offer.get("offer_total_promo_spent", 0.0) for offer in offers),
            "offer_total_promo_available": sum(offer.get("offer_total_promo_available", 0.0) for offer in offers),
            "total_spent_offer": total_spent,
            "spent": total_spent,
            "total_spent": {
                "production": total_spent,
                "test": 0.0,
                "all": total_spent
            },

            # Metrics
            "ctr": overall_ctr,

            # Detailed stats
            "offer_stats": {
                "clicks_total": offer_clicks_total,
                "clicks_production": offer_clicks_production,
                "clicks_test": offer_clicks_total - offer_clicks_production,
                "conversions_total": offer_conversions_total,
                "conversions_production": offer_conversions_production,
                "conversions_test": offer_conversions_test
            },
            "product_stats": {
                "clicks": product_clicks,
                "conversions": product_conversions_production
            }
        }
    }

class TrackingSetup(BaseModel):
    offer_id: str
    product_id: str
    method: Literal["redirect_pixel", "server_api", "manual"]
    webhook_url: Optional[str] = None
    notes: Optional[str] = None
    redirect_url: Optional[str] = None  # For redirect_pixel method
    target_urls: Optional[list[str]] = []  # List of target URLs

@router.post("/tracking/setup")
async def setup_tracking(tracking_setup: TrackingSetup, user=Depends(require_role("brand"))):
    # Get brand_id from authenticated user
    brand_id = user["uid"]
    offer_id = tracking_setup.offer_id

    # Verify offer exists and belongs to this brand
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this offer")

    # Create tracking data
    tracking_data = {
        "method": tracking_setup.method,
        "webhook_url": tracking_setup.webhook_url,
        "notes": tracking_setup.notes,
        "redirect_url": tracking_setup.redirect_url,
        "target_urls": tracking_setup.target_urls or [],
        "updated_at": firestore.SERVER_TIMESTAMP
    }

    # Create simplified tracking object
    tracking = {
        "method": tracking_data["method"],
        "redirect_url": tracking_data["redirect_url"] or "",
        "target_urls": tracking_data["target_urls"]  # Use provided target URLs
    }

    # Update offer with tracking data
    offer_ref.update({
        "tracking": tracking
    })

    # Generate product embedding now that the offer is finalized
    try:
        # Get the product data to generate comprehensive embedding
        product_id = tracking_setup.product_id
        product_ref = db.collection("products").document(product_id)
        product_doc = product_ref.get()

        if product_doc.exists:
            product_data = product_doc.to_dict()

            # Use the final offer data for embedding generation with new collection
            from api.utils.embedding import (
                embed_text,
                update_product_embedding_in_collection,
                store_product_embedding_in_collection,
                get_product_embedding_from_collection
            )

            # Create enhanced product data that includes offer-specific information
            enhanced_product_data = product_data.copy()

            # Use offer_title and offer_description if available to enhance the product data
            if offer_data.get("offer_title"):
                enhanced_product_data["title"] = offer_data["offer_title"]
            if offer_data.get("offer_description"):
                enhanced_product_data["description"] = offer_data["offer_description"]

            # Create comprehensive text for embedding using enhanced data
            embedding_text_parts = []

            if enhanced_product_data.get("title"):
                embedding_text_parts.append(f"Product: {enhanced_product_data['title']}")
            if enhanced_product_data.get("description"):
                embedding_text_parts.append(f"Description: {enhanced_product_data['description']}")

            # Add categories and keywords from product data
            if enhanced_product_data.get("categories") and isinstance(enhanced_product_data["categories"], list):
                categories_text = ", ".join(enhanced_product_data["categories"])
                embedding_text_parts.append(f"Categories: {categories_text}")

            if enhanced_product_data.get("keywords") and isinstance(enhanced_product_data["keywords"], list):
                keywords_text = ", ".join(enhanced_product_data["keywords"])
                embedding_text_parts.append(f"Keywords: {keywords_text}")

            # Add audience segment if available
            if enhanced_product_data.get("audience_segment"):
                embedding_text_parts.append(f"Audience: {enhanced_product_data['audience_segment']}")

            # Add integrations if available
            if enhanced_product_data.get("integration_list") and isinstance(enhanced_product_data["integration_list"], list):
                integrations_text = ", ".join(enhanced_product_data["integration_list"])
                embedding_text_parts.append(f"Integrations: {integrations_text}")

            # Combine all parts into a single text for embedding
            embedding_text = ". ".join(embedding_text_parts)

            # Generate and store the embedding in the new collection
            try:
                product_embedding = embed_text(embedding_text)

                # Prepare metadata
                metadata = {
                    "source_text": embedding_text,
                    "generation_method": "openai_text-embedding-3-small",
                    "updated_during": "offer_tracking_setup",
                    "offer_id": offer_data.get("id"),
                    "enhanced_with_offer_data": bool(offer_data.get("offer_title") or offer_data.get("offer_description"))
                }

                # Check if embedding already exists
                existing_embedding = get_product_embedding_from_collection(product_id)

                if existing_embedding:
                    # Update existing embedding
                    success = update_product_embedding_in_collection(product_id, product_embedding, metadata)
                    action = "updated"
                else:
                    # Store new embedding
                    success = store_product_embedding_in_collection(product_id, product_embedding, metadata)
                    action = "created"

                if success:
                    logger.info(f"🔮 Generated and {action} final product embedding with {len(product_embedding)} dimensions for product {product_id}")
                else:
                    logger.warning(f"⚠️ Generated embedding but failed to {action.replace('d', '')} in collection for product {product_id}")

            except Exception as embedding_error:
                logger.error(f"❌ Failed to generate/store embedding for product {product_id}: {str(embedding_error)}")

    except Exception as e:
        logger.error(f"❌ Failed to generate product embedding during tracking setup: {str(e)}")
        # Don't fail the tracking setup if embedding generation fails

    return {
        "status": "success",
        "message": "Tracking setup complete",
        "tracking": tracking
    }

class TrackingUpdate(BaseModel):
    product_id: str
    tracking: dict  # Contains method, redirect_url, target_urls, and notes

@router.post("/{offer_identifier}/tracking/update")
async def update_tracking(
    offer_identifier: str,
    payload: TrackingUpdate,
    user=Depends(require_role("brand"))
):
    """Update tracking settings for an offer"""
    # Get brand_id from authenticated user
    brand_id = user["uid"]

    # Resolve the identifier to actual document ID
    offer_id = resolve_offer_id(offer_identifier)
    if not offer_id:
        raise HTTPException(status_code=404, detail="Offer not found")

    # Verify offer exists and belongs to this brand
    offer_ref = db.collection("offers").document(offer_id)
    offer_doc = offer_ref.get()

    if not offer_doc.exists:
        raise HTTPException(status_code=404, detail="Offer not found")

    offer_data = offer_doc.to_dict()
    if offer_data.get("brand_id") != brand_id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this offer")

    # Extract tracking data
    tracking_data = payload.tracking
    tracking_data["updated_at"] = firestore.SERVER_TIMESTAMP

    # Create tracking object
    tracking = {
        "method": tracking_data.get("method", "redirect_pixel"),
        "redirect_url": tracking_data.get("redirect_url", ""),
        "target_urls": tracking_data.get("target_urls", [])  # Use provided target URLs or empty list
    }

    # Update offer with tracking data
    offer_ref.update({
        "tracking": tracking
    })

    return {
        "status": "success",
        "message": "Tracking updated successfully",
        "tracking": tracking
    }
