#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update the AdMesh product with rich data for better UI display
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import initialize_firebase
from google.cloud import firestore

def update_admesh_product():
    """Update the AdMesh product with rich data"""
    
    # Initialize Firebase
    initialize_firebase()
    db = firestore.Client()
    
    # The product ID from the server logs
    product_id = "33ffce2e-b189-4959-96bc-1329502600c5"
    
    # Rich product data
    update_data = {
        "features": [
            "AI-Powered Recommendations",
            "Real-time Analytics",
            "Easy Integration",
            "Custom Branding",
            "Performance Tracking"
        ],
        "integrations": [
            "REST API",
            "JavaScript SDK",
            "React Components",
            "WordPress Plugin",
            "Shopify App"
        ],
        "pricing": "Free - $299/month",
        "trial_days": 14,
        "is_open_source": False,
        "description": "AdMesh is the leading AI-powered advertising platform that helps businesses monetize their content with smart, contextual recommendations.",
        "audience_segment": "SaaS Companies, Content Creators, E-commerce",
        "keywords": [
            "AI advertising",
            "content monetization", 
            "smart recommendations",
            "ad network",
            "revenue optimization"
        ],
        "categories": ["Advertising", "AI", "SaaS", "Marketing"]
    }
    
    try:
        # Update the product
        product_ref = db.collection("products").document(product_id)
        product_ref.update(update_data)
        
        print(f"✅ Successfully updated product {product_id} with rich data")
        print("Updated fields:")
        for key, value in update_data.items():
            print(f"  - {key}: {value}")
            
    except Exception as e:
        print(f"❌ Error updating product: {str(e)}")

if __name__ == "__main__":
    update_admesh_product()
