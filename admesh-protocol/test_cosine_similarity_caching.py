#!/usr/bin/env python3
"""
Test script to demonstrate the cosine similarity-based embedding caching system.
This shows how the new approach uses actual embedding vectors for semantic matching.
"""

import sys
import os
import numpy as np
sys.path.append(os.path.join(os.path.dirname(__file__), 'api', 'routes'))

from agent_recommendation import normalize_query_for_caching

def mock_embed_text(text: str) -> list:
    """
    Mock embedding function that creates deterministic embeddings for testing.
    In reality, this would call OpenAI's embedding API.
    """
    # Create a simple hash-based embedding for testing
    import hashlib
    
    # Normalize the text first
    normalized = normalize_query_for_caching(text)
    
    # Create a deterministic "embedding" based on the normalized text
    hash_obj = hashlib.md5(normalized.encode())
    hash_bytes = hash_obj.digest()
    
    # Convert to a 1536-dimensional vector (like OpenAI's embeddings)
    # This is just for testing - real embeddings would be from OpenAI
    embedding = []
    for i in range(1536):
        byte_index = i % len(hash_bytes)
        embedding.append((hash_bytes[byte_index] - 128) / 128.0)  # Normalize to [-1, 1]
    
    # Add some semantic similarity for related terms
    if 'crm' in normalized:
        # Boost certain dimensions for CRM-related queries
        for i in range(0, 100):
            embedding[i] += 0.3
    elif 'analytics' in normalized:
        # Boost different dimensions for analytics queries
        for i in range(100, 200):
            embedding[i] += 0.3
    elif 'project management' in normalized:
        # Boost other dimensions for project management
        for i in range(200, 300):
            embedding[i] += 0.3
    
    # Normalize the vector
    norm = np.linalg.norm(embedding)
    if norm > 0:
        embedding = [x / norm for x in embedding]
    
    return embedding

def cosine_similarity(a: list, b: list) -> float:
    """Calculate cosine similarity between two vectors."""
    a_array = np.array(a)
    b_array = np.array(b)
    
    dot_product = np.dot(a_array, b_array)
    norm_a = np.linalg.norm(a_array)
    norm_b = np.linalg.norm(b_array)
    
    if norm_a == 0 or norm_b == 0:
        return 0.0
    
    similarity = dot_product / (norm_a * norm_b)
    return float(max(0.0, min(1.0, similarity)))

def test_cosine_similarity_caching():
    """Test the cosine similarity approach with mock embeddings."""
    
    print("🧪 Testing Cosine Similarity-Based Caching")
    print("=" * 60)
    
    # Test queries with their expected semantic relationships
    test_queries = [
        "best CRM software",
        "top CRM tools", 
        "CRM platforms for business",
        "customer relationship management software",
        "analytics tools",
        "data analytics platforms",
        "project management software",
        "task management tools"
    ]
    
    # Generate embeddings for all queries
    embeddings = {}
    for query in test_queries:
        embedding = mock_embed_text(query)
        embeddings[query] = embedding
        normalized = normalize_query_for_caching(query)
        print(f"'{query}' → '{normalized}'")
    
    print("\n🔍 Cosine Similarity Matrix:")
    print("=" * 60)
    
    # Calculate similarity matrix
    queries = list(embeddings.keys())
    similarity_threshold = 0.95
    
    for i, query1 in enumerate(queries):
        print(f"\n{query1}:")
        for j, query2 in enumerate(queries):
            if i != j:
                similarity = cosine_similarity(embeddings[query1], embeddings[query2])
                status = "🎯 CACHE HIT" if similarity >= similarity_threshold else "❌ New embedding"
                print(f"  vs '{query2}': {similarity:.4f} {status}")

def test_semantic_grouping():
    """Test how semantically related queries group together."""
    
    print("\n🎯 Testing Semantic Grouping")
    print("=" * 60)
    
    # Groups of semantically related queries
    semantic_groups = {
        "CRM": [
            "best CRM software",
            "top customer relationship management tools",
            "CRM platforms for small business",
            "customer management software"
        ],
        "Analytics": [
            "data analytics tools",
            "business intelligence software", 
            "analytics platforms",
            "data visualization tools"
        ],
        "Project Management": [
            "project management software",
            "task management tools",
            "team collaboration platforms",
            "project tracking software"
        ]
    }
    
    for group_name, queries in semantic_groups.items():
        print(f"\n📊 {group_name} Group:")
        
        # Generate embeddings for this group
        group_embeddings = []
        for query in queries:
            embedding = mock_embed_text(query)
            group_embeddings.append((query, embedding))
        
        # Calculate intra-group similarities
        for i, (query1, emb1) in enumerate(group_embeddings):
            for j, (query2, emb2) in enumerate(group_embeddings):
                if i < j:  # Avoid duplicates
                    similarity = cosine_similarity(emb1, emb2)
                    cache_status = "✅ Would reuse cache" if similarity >= 0.95 else "🆕 New embedding needed"
                    print(f"  '{query1}' vs '{query2}': {similarity:.4f} - {cache_status}")

def test_cross_group_similarity():
    """Test similarity between different semantic groups."""
    
    print("\n🔀 Testing Cross-Group Similarity")
    print("=" * 60)
    
    test_pairs = [
        ("best CRM software", "data analytics tools"),
        ("project management software", "CRM platforms"),
        ("analytics tools", "task management tools"),
        ("customer management software", "business intelligence software")
    ]
    
    for query1, query2 in test_pairs:
        emb1 = mock_embed_text(query1)
        emb2 = mock_embed_text(query2)
        similarity = cosine_similarity(emb1, emb2)
        
        print(f"'{query1}' vs '{query2}': {similarity:.4f}")
        if similarity >= 0.95:
            print("  ⚠️  Unexpected high similarity between different domains!")
        else:
            print("  ✅ Correctly identified as different domains")

if __name__ == "__main__":
    print("🚀 Cosine Similarity-Based Embedding Caching Test")
    print("=" * 60)
    print("This test uses mock embeddings to demonstrate the concept.")
    print("In production, real OpenAI embeddings would be used.\n")
    
    test_cosine_similarity_caching()
    test_semantic_grouping()
    test_cross_group_similarity()
    
    print("\n📊 Benefits of Cosine Similarity Approach:")
    print("=" * 60)
    print("✅ True semantic similarity using actual embedding vectors")
    print("✅ Catches paraphrases and synonyms that text matching misses")
    print("✅ More accurate than word-based similarity metrics")
    print("✅ Leverages the full power of OpenAI's semantic understanding")
    print("✅ Reduces API calls while maintaining high semantic accuracy")
    print("✅ Works across different languages and writing styles")
