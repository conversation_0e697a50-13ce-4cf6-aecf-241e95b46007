#!/usr/bin/env python3
"""
Test script to verify the product embeddings refactor works correctly.

This script tests:
1. The new product_embeddings collection utilities
2. The transitional get_product_embedding function
3. Migration from old to new collection structure
4. Compatibility with existing recommendation system

Usage:
    python test_product_embeddings_refactor.py
"""

import sys
import os
import logging
from typing import Dict, Any, List

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore
from api.utils.embedding import (
    embed_text,
    get_product_embedding,
    get_product_embedding_from_collection,
    store_product_embedding_in_collection,
    update_product_embedding_in_collection,
    delete_product_embedding_from_collection,
    generate_embedding_text_from_product,
    get_or_create_product_embedding_new
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


class ProductEmbeddingTester:
    """Test suite for the product embeddings refactor."""
    
    def __init__(self):
        self.test_product_id = "test_product_embedding_refactor"
        self.test_product_data = {
            "id": self.test_product_id,
            "title": "Test CRM Software",
            "description": "A comprehensive customer relationship management tool for small businesses",
            "categories": ["crm", "business-tools"],
            "keywords": ["customer management", "sales", "automation"],
            "audience_segment": "small business owners",
            "integration_list": ["slack", "gmail", "zapier"]
        }
        self.cleanup_needed = []
    
    def cleanup(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        # Remove from product_embeddings collection
        try:
            delete_product_embedding_from_collection(self.test_product_id)
        except Exception as e:
            logger.debug(f"Cleanup: {e}")
        
        # Remove from products collection if exists
        try:
            db.collection("products").document(self.test_product_id).delete()
        except Exception as e:
            logger.debug(f"Cleanup: {e}")
        
        logger.info("✅ Cleanup completed")
    
    def test_embedding_text_generation(self) -> bool:
        """Test the generate_embedding_text_from_product function."""
        logger.info("🧪 Testing embedding text generation...")
        
        try:
            embedding_text = generate_embedding_text_from_product(self.test_product_data)
            
            # Verify the text contains expected components
            expected_components = [
                "Test CRM Software",
                "customer relationship management",
                "crm, business-tools",
                "customer management, sales, automation",
                "small business owners",
                "slack, gmail, zapier"
            ]
            
            for component in expected_components:
                if component not in embedding_text:
                    logger.error(f"❌ Missing component in embedding text: {component}")
                    return False
            
            logger.info(f"✅ Generated embedding text: {embedding_text[:100]}...")
            return True
            
        except Exception as e:
            logger.error(f"❌ Embedding text generation failed: {e}")
            return False
    
    def test_new_collection_operations(self) -> bool:
        """Test basic CRUD operations on the new product_embeddings collection."""
        logger.info("🧪 Testing new collection operations...")
        
        try:
            # Generate a test embedding
            embedding_text = generate_embedding_text_from_product(self.test_product_data)
            test_embedding = embed_text(embedding_text)
            
            # Test store operation
            metadata = {"test": "true", "source_text": embedding_text}
            success = store_product_embedding_in_collection(self.test_product_id, test_embedding, metadata)
            if not success:
                logger.error("❌ Failed to store embedding in new collection")
                return False
            
            # Test retrieve operation
            retrieved_embedding = get_product_embedding_from_collection(self.test_product_id)
            if not retrieved_embedding:
                logger.error("❌ Failed to retrieve embedding from new collection")
                return False
            
            if len(retrieved_embedding) != len(test_embedding):
                logger.error("❌ Retrieved embedding has different length")
                return False
            
            # Test update operation
            new_metadata = {"test": "updated", "source_text": embedding_text + " updated"}
            success = update_product_embedding_in_collection(self.test_product_id, test_embedding, new_metadata)
            if not success:
                logger.error("❌ Failed to update embedding in new collection")
                return False
            
            # Test delete operation
            success = delete_product_embedding_from_collection(self.test_product_id)
            if not success:
                logger.error("❌ Failed to delete embedding from new collection")
                return False
            
            # Verify deletion
            deleted_embedding = get_product_embedding_from_collection(self.test_product_id)
            if deleted_embedding:
                logger.error("❌ Embedding still exists after deletion")
                return False
            
            logger.info("✅ New collection operations test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ New collection operations test failed: {e}")
            return False
    
    def test_transitional_function(self) -> bool:
        """Test the transitional get_product_embedding function."""
        logger.info("🧪 Testing transitional get_product_embedding function...")
        
        try:
            # Test 1: No embedding exists anywhere (should generate new)
            embedding1 = get_product_embedding(self.test_product_id, self.test_product_data)
            if not embedding1 or len(embedding1) == 0:
                logger.error("❌ Failed to generate new embedding")
                return False
            
            # Test 2: Embedding now exists in new collection (should retrieve)
            embedding2 = get_product_embedding(self.test_product_id, self.test_product_data)
            if embedding1 != embedding2:
                logger.error("❌ Retrieved embedding doesn't match stored embedding")
                return False
            
            # Test 3: Simulate old collection scenario
            # First, delete from new collection
            delete_product_embedding_from_collection(self.test_product_id)
            
            # Create a product with embedding in old format
            old_product_data = self.test_product_data.copy()
            old_product_data["embedding"] = embedding1
            old_product_data["embedding_updated_at"] = firestore.SERVER_TIMESTAMP
            
            # Test migration scenario
            embedding3 = get_product_embedding(self.test_product_id, old_product_data)
            if embedding1 != embedding3:
                logger.error("❌ Migration didn't preserve embedding")
                return False
            
            # Verify it was migrated to new collection
            migrated_embedding = get_product_embedding_from_collection(self.test_product_id)
            if not migrated_embedding or migrated_embedding != embedding1:
                logger.error("❌ Embedding wasn't properly migrated to new collection")
                return False
            
            logger.info("✅ Transitional function test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Transitional function test failed: {e}")
            return False
    
    def test_new_function(self) -> bool:
        """Test the new get_or_create_product_embedding_new function."""
        logger.info("🧪 Testing new get_or_create_product_embedding_new function...")
        
        try:
            # Clean up first
            delete_product_embedding_from_collection(self.test_product_id)
            
            # Test with product data provided
            embedding1 = get_or_create_product_embedding_new(self.test_product_id, self.test_product_data)
            if not embedding1 or len(embedding1) == 0:
                logger.error("❌ Failed to create embedding with provided data")
                return False
            
            # Test retrieval (should get same embedding)
            embedding2 = get_or_create_product_embedding_new(self.test_product_id)
            if embedding1 != embedding2:
                logger.error("❌ Retrieved embedding doesn't match created embedding")
                return False
            
            logger.info("✅ New function test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ New function test failed: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all tests and return overall result."""
        logger.info("🚀 Starting product embeddings refactor tests...")
        
        try:
            # Clean up before starting
            self.cleanup()
            
            tests = [
                ("Embedding Text Generation", self.test_embedding_text_generation),
                ("New Collection Operations", self.test_new_collection_operations),
                ("Transitional Function", self.test_transitional_function),
                ("New Function", self.test_new_function)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"📋 Running test: {test_name}")
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
                
                # Clean up between tests
                self.cleanup()
            
            # Final results
            logger.info("=" * 60)
            logger.info(f"📊 TEST RESULTS: {passed}/{total} tests passed")
            logger.info("=" * 60)
            
            if passed == total:
                logger.info("🎉 All tests passed! The refactor is working correctly.")
                return True
            else:
                logger.error(f"💥 {total - passed} tests failed. Please review the issues.")
                return False
                
        except Exception as e:
            logger.error(f"💥 Test suite failed: {e}")
            return False
        finally:
            # Final cleanup
            self.cleanup()


def main():
    """Main function to run the tests."""
    tester = ProductEmbeddingTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
