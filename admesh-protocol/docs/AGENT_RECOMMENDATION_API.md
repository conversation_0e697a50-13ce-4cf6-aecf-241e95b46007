# AdMesh Agent Recommendation API Documentation

## Overview

The Agent Recommendation API (`/recommend`) is the core endpoint for retrieving AI-powered product recommendations based on user queries. It uses semantic matching, intent detection, and multiple scoring factors to provide relevant product suggestions.

## Endpoint

```
POST /recommend
```

## Authentication

Requires a valid AdMesh API key in the request headers:
```
Authorization: Bearer admesh_[environment]_[random_string]
```

## Request Parameters

### AgentRecommendRequest

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `query` | string | ✅ | - | The user's search query or question |
| `format` | string | ❌ | "auto" | Response format type (see Format Types below) |
| `previous_query` | string | ❌ | null | Previous query in the conversation |
| `previous_summary` | string | ❌ | null | Summary of previous conversation |
| `session_id` | string | ❌ | auto-generated | Session identifier for tracking |

### Format Types

| Format | Description | Max Recommendations | Special Features |
|--------|-------------|---------------------|------------------|
| `auto` | Default format with all recommendations | 5 | Standard response |
| `product` | Single best match | 1 | Enhanced product metrics |
| `sidebar` | Sidebar/floating widget | 3 | Optimized for UI widgets |
| `floating` | Floating chat interface | 3 | Same as sidebar |
| `expandable` | Expandable ad units | 5 | Includes feature_sections |
| `conversation` | Conversation summary | 5 | Includes conversation_context |

## Response Structure

### AgentRecommendResponse

```json
{
  "session_id": "string",
  "intent": {
    "intent_group": "string",
    "intent_type": "string", 
    "categories": ["string"],
    "goal": "string",
    "known_mentions": ["string"],
    "keywords": ["string"],
    "llm_intent_confidence_score": 0.85
  },
  "response": {
    "summary": "string",
    "recommendations": [AgentRecommendation],
    "followup_suggestions": [AgentFollowupSuggestion],
    "is_fallback": false
  },
  "tokens_used": 500,
  "model_used": "mistralai/mistral-7b-instruct",
  "recommendation_id": "uuid",
  "end_of_session": true
}
```

### Intent Object

| Field | Type | Description |
|-------|------|-------------|
| `intent_group` | string | Intent category: "informational", "navigational", "commercial", "transactional" |
| `intent_type` | string | Specific intent type (e.g., "product_discovery") |
| `categories` | string[] | Product categories (e.g., ["crm", "analytics"]) |
| `goal` | string | Plain English explanation of user's goal |
| `known_mentions` | string[] | Product/brand names mentioned in query |
| `keywords` | string[] | Extracted keywords for matching |
| `llm_intent_confidence_score` | float | Confidence score (0.0-1.0) |

## AgentRecommendation Object

### Core Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `title` | string | ✅ | Product title |
| `reason` | string | ✅ | Why this product matches the query |
| `intent_match_score` | float | ✅ | Relevance score (0.0-1.0) |
| `admesh_link` | string | ✅ | Tracking URL for clicks |
| `ad_id` | string | ✅ | Advertisement identifier |
| `product_id` | string | ✅ | Product identifier |

### Product Information

| Field | Type | Description |
|-------|------|-------------|
| `url` | string | Product website URL |
| `redirect_url` | string | Final redirect URL |
| `description` | string | Product description |
| `pricing` | string | Pricing information |
| `reward_note` | string | Affiliate reward information |
| `keywords` | string[] | Product keywords |
| `categories` | string[] | Product categories |
| `features` | string[] | Key product features |
| `integrations` | string[] | Available integrations |
| `trial_days` | integer | Free trial duration |
| `audience_segment` | string | Target audience |
| `is_open_source` | boolean | Open source status |

### Trust & Quality Scores

| Field | Type | Range | Description |
|-------|------|-------|-------------|
| `offer_trust_score` | float | 0.0-1.0 | Offer reliability score |
| `brand_trust_score` | float | 0.0-1.0 | Brand reputation score |
| `is_fallback` | boolean | - | Whether this is a fallback recommendation |

### Marketing Content

| Field | Type | Description |
|-------|------|-------------|
| `recommendation_title` | string | Marketing-optimized title |
| `recommendation_description` | string | Marketing-optimized description |
| `offer_images` | object[] | Promotional images |
| `product_logo` | object | Product logo information |

### Format-Specific Fields

| Field | Type | Format | Description |
|-------|------|--------|-------------|
| `feature_sections` | object[] | expandable | Structured feature sections |
| `conversation_context` | string | conversation | Conversation-optimized context |
| `content_variations` | object | all | Statement/question variations |

## Content Variations Structure

```json
{
  "content_variations": {
    "statement": {
      "text": "ProductName is highly relevant to your query, visit",
      "cta": "ProductName"
    },
    "question": {
      "text": "Looking for solutions for your business? Try", 
      "cta": "ProductName"
    }
  }
}
```

## Feature Sections Structure (Expandable Format)

```json
{
  "feature_sections": [
    {
      "title": "Core Features",
      "features": [
        {
          "name": "Feature Name",
          "description": "Feature description"
        }
      ]
    }
  ]
}
```

## AgentFollowupSuggestion Object

| Field | Type | Description |
|-------|------|-------------|
| `label` | string | Display text for the suggestion |
| `query` | string | Suggested follow-up query |
| `product_mentions` | string[] | Products mentioned in suggestion |
| `admesh_links` | object | Product name to AdMesh link mapping |
| `session_id` | string | Session identifier |

## Scoring Algorithm

The recommendation scoring uses multiple weighted factors:

### Primary Scoring (Semantic Matching)
- **Semantic Similarity**: 60% weight - Cosine similarity between query and product embeddings
- **LLM Confidence**: 20% weight - Intent detection confidence
- **Payout Score**: 10% weight - Affiliate payout (normalized to $100 max)
- **Trust Score**: 10% weight - Combined offer and brand trust scores

### Filtering
- **Semantic Threshold**: 0.3 minimum similarity score
- **Active Offers Only**: Only active offers are considered
- **Product Availability**: Products must exist and be accessible

### Fallback Mechanism
When no relevant matches are found:
- **Trust-Based Selection**: 70% trust score, 30% payout
- **Quality Assurance**: Selects highest-quality available offers
- **Fallback Indicator**: `is_fallback: true` in response

## Error Responses

### 400 Bad Request
```json
{
  "detail": "Low intent confidence"
}
```

### 401 Unauthorized
```json
{
  "detail": "Invalid API key"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Failed to detect intent from query"
}
```

## Example Request

```bash
curl -X POST "https://api.useadmesh.com/recommend" \
  -H "Authorization: Bearer admesh_prod_abc123xyz789" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "best CRM for small business",
    "format": "product"
  }'
```

## Example Response

```json
{
  "session_id": "sess_1703123456_abc123",
  "intent": {
    "intent_group": "commercial",
    "intent_type": "product_discovery",
    "categories": ["crm"],
    "goal": "Find the best CRM solution for small business",
    "known_mentions": [],
    "keywords": ["crm", "small business", "customer management"],
    "llm_intent_confidence_score": 0.92
  },
  "response": {
    "summary": "Here are CRM tools that match your goal: Find the best CRM solution for small business",
    "recommendations": [
      {
        "title": "HubSpot CRM",
        "reason": "This offer is highly relevant to your query and highly trusted platform.",
        "intent_match_score": 0.89,
        "admesh_link": "https://useadmesh.com/track?ad_id=hubspot-123",
        "ad_id": "hubspot-123",
        "product_id": "hubspot-crm",
        "url": "https://hubspot.com",
        "description": "Free CRM with powerful features for small businesses",
        "pricing": "Free tier available, paid plans from $45/month",
        "trial_days": 14,
        "features": ["Contact Management", "Deal Tracking", "Email Integration"],
        "categories": ["crm", "sales"],
        "keywords": ["crm", "sales", "marketing"],
        "offer_trust_score": 0.95,
        "brand_trust_score": 0.92,
        "is_fallback": false,
        "content_variations": {
          "statement": {
            "text": "HubSpot CRM is highly relevant to your query, visit",
            "cta": "HubSpot CRM"
          },
          "question": {
            "text": "Looking for crm for your business? Try",
            "cta": "HubSpot CRM"
          }
        }
      }
    ],
    "followup_suggestions": [
      {
        "label": "What features does HubSpot CRM offer?",
        "query": "HubSpot CRM key features and benefits",
        "product_mentions": ["HubSpot CRM"],
        "admesh_links": {
          "HubSpot CRM": "https://useadmesh.com/track?ad_id=hubspot-123"
        },
        "session_id": "sess_1703123456_abc123"
      }
    ],
    "is_fallback": false
  },
  "tokens_used": 500,
  "model_used": "mistralai/mistral-7b-instruct",
  "recommendation_id": "rec_uuid_123",
  "end_of_session": true
}
```

## Rate Limits

- **Production**: 1000 requests per hour per API key
- **Test**: 100 requests per hour per API key

## Best Practices

1. **Query Optimization**: Use specific, descriptive queries for better matching
2. **Format Selection**: Choose appropriate format for your UI component
3. **Session Tracking**: Use consistent session_id for conversation continuity
4. **Error Handling**: Implement proper error handling for all response codes
5. **Caching**: Cache responses when appropriate to reduce API calls
6. **Analytics**: Track click-through rates using admesh_link URLs

## Integration Examples

### Basic Integration
```javascript
const response = await fetch('/recommend', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer admesh_prod_your_key',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    query: 'project management software',
    format: 'auto'
  })
});

const data = await response.json();
console.log(data.response.recommendations);
```

### With AdMesh UI SDK
```jsx
import { AdMeshProductCard } from 'admesh-ui-sdk';

// After getting recommendations from API
{recommendations.map(rec => (
  <AdMeshProductCard
    key={rec.ad_id}
    recommendation={rec}
    variation="default"
    showMatchScore={true}
    onClick={(adId, admeshLink) => window.open(admeshLink, '_blank')}
  />
))}
```
