#!/usr/bin/env python3
"""
Test script to demonstrate the improved query embedding caching system.
This shows how the new caching approach handles query normalization and similarity matching.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api', 'routes'))

from agent_recommendation import normalize_query_for_caching

def test_query_normalization():
    """Test the query normalization function with various inputs."""
    
    test_cases = [
        # Original query -> Expected normalized result
        ("Please help me find the best CRM software", "crm software"),
        ("Looking for good analytics tools", "analytics tools"),
        ("What are the top email marketing platforms?", "email marketing platforms"),
        ("Can you show me project management software", "project management software"),
        ("I need a database solution for my startup", "database solution startup"),
        ("Find me the best accounting software please", "accounting software"),
        ("What's the best CRM for small business?", "crm small business"),  # preserve order
        ("CRM software for small business", "crm software small business"),  # preserve order
    ]
    
    print("🧪 Testing Query Normalization")
    print("=" * 60)
    
    for original, expected in test_cases:
        normalized = normalize_query_for_caching(original)
        status = "✅" if normalized == expected else "❌"
        print(f"{status} '{original}'")
        print(f"   → '{normalized}'")
        if normalized != expected:
            print(f"   Expected: '{expected}'")
        print()

def test_semantic_similarity():
    """Test how similar queries would be matched."""
    
    similar_queries = [
        "best CRM software",
        "top CRM tools", 
        "good CRM platforms",
        "CRM software recommendations"
    ]
    
    print("🔍 Testing Semantic Similarity")
    print("=" * 60)
    
    normalized_queries = []
    for query in similar_queries:
        normalized = normalize_query_for_caching(query)
        normalized_queries.append(normalized)
        print(f"'{query}' → '{normalized}'")
    
    print("\nSimilarity Analysis:")
    base_words = set(normalized_queries[0].split())
    
    for i, norm_query in enumerate(normalized_queries[1:], 1):
        query_words = set(norm_query.split())
        intersection = len(base_words.intersection(query_words))
        union = len(base_words.union(query_words))
        jaccard_similarity = intersection / union if union > 0 else 0.0
        
        print(f"  Query {i+1} similarity: {jaccard_similarity:.3f}")
        if jaccard_similarity >= 0.95:
            print(f"    ✅ Would reuse cache (>= 0.95 threshold)")
        else:
            print(f"    ❌ Would generate new embedding")

def test_cache_key_generation():
    """Test cache key generation for different queries."""
    
    import hashlib
    
    queries = [
        "best CRM software",
        "Best CRM Software",  # Different case
        "best crm software",  # Different case
        "CRM software that's the best",  # Different word order
        "Please find me the best CRM software",  # With noise words
    ]
    
    print("🔑 Testing Cache Key Generation")
    print("=" * 60)
    
    cache_keys = {}
    for query in queries:
        normalized = normalize_query_for_caching(query)
        cache_key = hashlib.md5(normalized.encode()).hexdigest()[:8]  # Short hash for display
        
        print(f"'{query}'")
        print(f"  → Normalized: '{normalized}'")
        print(f"  → Cache Key: {cache_key}")
        
        if normalized in cache_keys:
            print(f"  ✅ CACHE HIT! Same as: '{cache_keys[normalized]}'")
        else:
            cache_keys[normalized] = query
            print(f"  🆕 New cache entry")
        print()

if __name__ == "__main__":
    print("🚀 Improved Query Embedding Caching System Test")
    print("=" * 60)
    print()
    
    test_query_normalization()
    print()
    test_semantic_similarity()
    print()
    test_cache_key_generation()
    
    print("📊 Summary of Improvements:")
    print("=" * 60)
    print("✅ Query normalization removes noise words and standardizes format")
    print("✅ Hash-based exact matching for O(1) cache lookups")
    print("✅ Jaccard similarity for semantic matching of related queries")
    print("✅ Usage tracking for LRU-like cache management")
    print("✅ Batch cleanup operations for better performance")
    print("✅ TTL-based expiration with automatic cleanup")
